import React from 'react';
import PropTypes from 'prop-types';
import {FormattedMessage} from 'react-intl';
import classNames from 'classnames';

import styles from './user-menu.css';
import dropdownCaret from './dropdown-caret.svg';

const UserMenu = ({
    className,
    isLoggedIn,
    isOpen,
    isRtl,
    menuBarMenuClassName,
    onClick,
    onClose,
    onLogout,
    onClickUserInfo,
    onClickMyProjects,
    username
}) => (
    <React.Fragment>
        <div
            className={classNames(
                styles.userInfo,
                className,
                {
                    [styles.loggedIn]: isLoggedIn
                }
            )}
            onMouseUp={onClick}
        >
            {isLoggedIn ? (
                <React.Fragment>
                    <span className={styles.profileName}>
                        {username}
                    </span>
                    <div className={styles.dropdownCaretPosition}>
                        <img
                            className={styles.dropdownCaretIcon}
                            src={dropdownCaret}
                        />
                    </div>
                </React.Fragment>
            ) : (
                <FormattedMessage
                    defaultMessage="登录"
                    description="登录按钮文本"
                    id="gui.userMenu.login"
                />
            )}
        </div>
        {isLoggedIn && isOpen && (
            <div
                className={classNames(
                    menuBarMenuClassName,
                    styles.menu
                )}
            >
                <div
                    className={styles.menuItem}
                    onClick={onClickUserInfo}
                >
                    <FormattedMessage
                        defaultMessage="个人信息"
                        description="个人信息菜单项"
                        id="gui.userMenu.userInfo"
                    />
                </div>
                <div
                    className={styles.menuItem}
                    onClick={onClickMyProjects}
                >
                    <FormattedMessage
                        defaultMessage="我的项目"
                        description="我的项目菜单项"
                        id="gui.userMenu.myProjects"
                    />
                </div>
                <div className={styles.menuDivider} />
                <div
                    className={styles.menuItem}
                    onClick={onLogout}
                    data-logout="true"
                >
                    <FormattedMessage
                        defaultMessage="退出登录"
                        description="退出登录菜单项"
                        id="gui.userMenu.logout"
                    />
                </div>
            </div>
        )}
    </React.Fragment>
);

UserMenu.propTypes = {
    className: PropTypes.string,
    isLoggedIn: PropTypes.bool,
    isOpen: PropTypes.bool,
    isRtl: PropTypes.bool,
    menuBarMenuClassName: PropTypes.string,
    onClick: PropTypes.func,
    onClose: PropTypes.func,
    onLogout: PropTypes.func,
    onClickUserInfo: PropTypes.func,
    onClickMyProjects: PropTypes.func,
    username: PropTypes.string
};

export default UserMenu;
