<!DOCTYPE html>
<html>
  <head>
    <% if (htmlWebpackPlugin.options.gtm_id) { %>
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl+'<%= htmlWebpackPlugin.options.gtm_env_auth %>';
        f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','<%- htmlWebpackPlugin.options.gtm_id %>');
    </script>
    <!-- End Google Tag Manager -->
    <% } %>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="google" value="notranslate">
    <link rel="shortcut icon" href="static/favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
  </head>
  <body>
    <% if (htmlWebpackPlugin.options.gtm_id) { %>
      <!-- Google Tag Manager (noscript) -->
      <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=<%- htmlWebpackPlugin.options.gtm_id %><%= htmlWebpackPlugin.options.gtm_env_auth %>" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
      <!-- End Google Tag Manager (noscript) -->
    <% } %>
  </body>
</html>
