/**
 * K1机器人自定义积木块定义
 * 基于demandK1.txt中的C语言函数
 */

import ScratchBlocks from 'scratch-blocks';

/**
 * 创建K1机器人自定义积木块
 * @param {Object} ScratchBlocks - ScratchBlocks对象
 */
const defineK1CustomBlocks = () => {
    // 定义积木块颜色
    const k1Colors = {
        primary: '#4C97FF', // 主要颜色 - 控制类
        secondary: '#3373CC', // 次要颜色
        tertiary: '#2855A8', // 第三颜色

        sensorPrimary: '#5CB1D6', // 传感器主要颜色
        sensorSecondary: '#47A8D1', // 传感器次要颜色
        sensorTertiary: '#3D8EB5' // 传感器第三颜色
    };

    // 前进积木块
    ScratchBlocks.Blocks.k1_forward = {
        init: function() {
            this.jsonInit({
                "message0": "前进 速度 %1 距离 %2",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "SPEED",
                        "check": "Number"
                    },
                    {
                        "type": "input_value",
                        "name": "DISTANCE",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制机器人前进，速度范围[1-8]，距离范围[1-200]"
            });
        }
    };

    // 后退积木块
    ScratchBlocks.Blocks.k1_back = {
        init: function() {
            this.jsonInit({
                "message0": "后退 速度 %1 距离 %2",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "SPEED",
                        "check": "Number"
                    },
                    {
                        "type": "input_value",
                        "name": "DISTANCE",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制机器人后退，速度范围[1-8]，距离范围[1-200]"
            });
        }
    };

    // 左转积木块
    ScratchBlocks.Blocks.k1_turn_left = {
        init: function() {
            this.jsonInit({
                "message0": "左转 角度 %1",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "DEGREE",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制机器人左转"
            });
        }
    };

    // 右转积木块
    ScratchBlocks.Blocks.k1_turn_right = {
        init: function() {
            this.jsonInit({
                "message0": "右转 角度 %1",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "DEGREE",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制机器人右转"
            });
        }
    };

    // 说话积木块
    ScratchBlocks.Blocks.k1_gpp_say = {
        init: function() {
            this.jsonInit({
                "message0": "说话 模式 %1 内容 %2",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "MODE",
                        "check": "Number"
                    },
                    {
                        "type": "input_value",
                        "name": "TEXT",
                        "check": "String"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制机器人说话，模式：0(打断前面语句)，1(等待前面语句说完)"
            });
        }
    };

    // 打开机械手积木块
    ScratchBlocks.Blocks.k1_servo_open = {
        init: function() {
            this.jsonInit({
                "message0": "打开机械手",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制打开机器人的机械手"
            });
        }
    };

    // 关闭机械手积木块
    ScratchBlocks.Blocks.k1_servo_close = {
        init: function() {
            this.jsonInit({
                "message0": "关闭机械手",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制闭合机器人的机械手"
            });
        }
    };

    // 开启巡线模式积木块
    ScratchBlocks.Blocks.k1_tracker_start = {
        init: function() {
            this.jsonInit({
                "message0": "开启巡线模式",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "开启机器人智能巡线模式"
            });
        }
    };

    // 关闭巡线模式积木块
    ScratchBlocks.Blocks.k1_tracker_close = {
        init: function() {
            this.jsonInit({
                "message0": "关闭巡线模式",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "关闭机器人智能巡线模式"
            });
        }
    };

    // 蜂鸣器积木块
    ScratchBlocks.Blocks.k1_beep = {
        init: function() {
            this.jsonInit({
                "message0": "蜂鸣器 频率 %1 时间 %2",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "BOUND",
                        "check": "Number"
                    },
                    {
                        "type": "input_value",
                        "name": "TIME",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制机器人蜂鸣器，频率范围[200-5000]，时间范围[0-3000]毫秒"
            });
        }
    };

    // LED灯积木块
    ScratchBlocks.Blocks.k1_colorful_led = {
        init: function() {
            this.jsonInit({
                "message0": "LED灯 模式 %1 颜色 %2",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "MODE",
                        "check": "Number"
                    },
                    {
                        "type": "input_value",
                        "name": "RGB",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "控制机器人LED灯，模式：0(默认)，1(左侧)，2(右侧)，3(两侧)，颜色范围[1-7]"
            });
        }
    };

    // 设置C程序运行模式积木块
    ScratchBlocks.Blocks.k1_set_cscript_mode = {
        init: function() {
            this.jsonInit({
                "message0": "设置C程序运行模式 %1",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "MODE",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "设置机器人C程序运行模式，1(抢占式执行)，2(等待式执行)"
            });
        }
    };

    // 停止C程序积木块
    ScratchBlocks.Blocks.k1_cexit = {
        init: function() {
            this.jsonInit({
                "message0": "停止当前C程序",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.primary,
                "colourSecondary": k1Colors.secondary,
                "colourTertiary": k1Colors.tertiary,
                "tooltip": "停止当前运行的C程序"
            });
        }
    };

    // 光线传感器积木块 - 两种版本
    // 1. 报告块版本 - 用于条件判断和数值输入
    ScratchBlocks.Blocks.k1_lightsensor_reporter = {
        init: function() {
            this.jsonInit({
                "message0": "光线传感器数值",
                "inputsInline": true,
                "output": "Number",
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "获取机器人光线传感器数值"
            });
        }
    };

    // 2. 命令块版本 - 用于序列化操作
    ScratchBlocks.Blocks.k1_lightsensor = {
        init: function() {
            this.jsonInit({
                "message0": "读取光线传感器数值",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "读取机器人光线传感器数值并打印结果"
            });
        }
    };

    // 距离传感器积木块 - 两种版本
    // 1. 报告块版本
    ScratchBlocks.Blocks.k1_distsensor_reporter = {
        init: function() {
            this.jsonInit({
                "message0": "距离传感器数值(厘米)",
                "inputsInline": true,
                "output": "Number",
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "获取机器人距离传感器数值，单位：厘米"
            });
        }
    };

    // 2. 命令块版本
    ScratchBlocks.Blocks.k1_distsensor = {
        init: function() {
            this.jsonInit({
                "message0": "读取距离传感器数值",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "读取机器人距离传感器数值并打印结果，单位：厘米"
            });
        }
    };

    // 噪声传感器积木块 - 两种版本
    // 1. 报告块版本
    ScratchBlocks.Blocks.k1_mic1sensor_reporter = {
        init: function() {
            this.jsonInit({
                "message0": "噪声传感器数值",
                "inputsInline": true,
                "output": "Number",
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "获取机器人噪声检测传感器数值"
            });
        }
    };

    // 2. 命令块版本
    ScratchBlocks.Blocks.k1_mic1sensor = {
        init: function() {
            this.jsonInit({
                "message0": "读取噪声传感器数值",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "读取机器人噪声检测传感器数值并打印结果"
            });
        }
    };

    // 循迹传感器积木块 - 两种版本
    // 1. 报告块版本
    ScratchBlocks.Blocks.k1_tracker_reporter = {
        init: function() {
            this.jsonInit({
                "message0": "循迹传感器 %1 数值",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "ID",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "output": "Number",
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "获取机器人循迹传感器数值，ID范围[1-4]，返回值：0或1"
            });
        }
    };

    // 2. 命令块版本
    ScratchBlocks.Blocks.k1_tracker = {
        init: function() {
            this.jsonInit({
                "message0": "读取循迹传感器 %1 数值",
                "args0": [
                    {
                        "type": "input_value",
                        "name": "ID",
                        "check": "Number"
                    }
                ],
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "读取机器人循迹传感器数值并打印结果，ID范围[1-4]，返回值：0或1"
            });
        }
    };

    // 手柄按键值积木块 - 两种版本
    // 1. 报告块版本
    ScratchBlocks.Blocks.k1_get_ps2value_reporter = {
        init: function() {
            this.jsonInit({
                "message0": "手柄按键值",
                "inputsInline": true,
                "output": "Number",
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "获取机器人控制手柄按下的按键值"
            });
        }
    };

    // 2. 命令块版本
    ScratchBlocks.Blocks.k1_get_ps2value = {
        init: function() {
            this.jsonInit({
                "message0": "读取手柄按键值",
                "inputsInline": true,
                "previousStatement": null,
                "nextStatement": null,
                "category": "K1机器人",
                "colour": k1Colors.sensorPrimary,
                "colourSecondary": k1Colors.sensorSecondary,
                "colourTertiary": k1Colors.sensorTertiary,
                "tooltip": "读取机器人控制手柄按下的按键值并打印结果"
            });
        }
    };
};

export default defineK1CustomBlocks;
