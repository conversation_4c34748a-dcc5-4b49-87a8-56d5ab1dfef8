<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>循环执行测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            min-height: 300px;
            margin: 10px 0;
            overflow-y: auto;
            max-height: 500px;
            line-height: 1.6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .two-column {
            display: flex;
            gap: 20px;
        }
        .column {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>循环执行测试</h1>
        <p>测试C代码中的循环是否能正确执行指定次数，并验证精灵控制命令的解析</p>

        <div id="status" class="status info">正在初始化...</div>

        <div class="two-column">
            <div class="column">
                <h3>测试选项</h3>
                <button class="btn" onclick="testLoop3()" id="loop3Btn" disabled>循环3次测试</button>
                <button class="btn" onclick="testLoop5()" id="loop5Btn" disabled>循环5次测试</button>
                <button class="btn" onclick="testLoop10()" id="loop10Btn" disabled>循环10次测试</button>
                <button class="btn" onclick="testNestedLoop()" id="nestedBtn" disabled>嵌套循环测试</button>
                <button class="btn" onclick="clearOutput()">清空输出</button>
                
                <h3>命令统计</h3>
                <div id="commandStats" style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">
                    等待执行...
                </div>
            </div>
            
            <div class="column">
                <h3>执行输出</h3>
                <div id="output" class="output">等待测试...</div>
            </div>
        </div>
    </div>

    <script>
        let picocjs = null;
        let commandCount = {};
        
        // 初始化
        function init() {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = '正在加载PicoC...';
            
            const script = document.createElement('script');
            script.src = './static/picoc-test/bundle.umd.js';
            
            script.onload = () => {
                if (window.picocjs) {
                    picocjs = window.picocjs;
                    statusDiv.textContent = 'PicoC加载成功！可以开始测试。';
                    statusDiv.className = 'status success';
                    
                    document.getElementById('loop3Btn').disabled = false;
                    document.getElementById('loop5Btn').disabled = false;
                    document.getElementById('loop10Btn').disabled = false;
                    document.getElementById('nestedBtn').disabled = false;
                } else {
                    statusDiv.textContent = 'PicoC加载失败：未找到picocjs对象';
                    statusDiv.className = 'status error';
                }
            };
            
            script.onerror = () => {
                statusDiv.textContent = 'PicoC加载失败：无法加载脚本文件';
                statusDiv.className = 'status error';
            };
            
            document.head.appendChild(script);
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
            commandCount = {};
            updateCommandStats();
        }

        function appendOutput(text) {
            const outputDiv = document.getElementById('output');
            outputDiv.textContent += text;
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }

        function updateCommandStats() {
            const statsDiv = document.getElementById('commandStats');
            if (Object.keys(commandCount).length === 0) {
                statsDiv.innerHTML = '等待执行...';
                return;
            }
            
            let html = '<strong>命令执行统计：</strong><br>';
            for (const [command, count] of Object.entries(commandCount)) {
                html += `${command}: ${count} 次<br>`;
            }
            statsDiv.innerHTML = html;
        }

        function countCommand(command) {
            commandCount[command] = (commandCount[command] || 0) + 1;
            updateCommandStats();
        }

        async function runTest(code, description) {
            if (!picocjs) {
                appendOutput('PicoC 未加载\n');
                return;
            }

            commandCount = {};
            appendOutput(`\n=== ${description} ===\n`);
            
            try {
                await picocjs.runC(code, (output) => {
                    const processedOutput = output.replace(/\\n/g, '\n');
                    appendOutput(processedOutput);
                    
                    // 统计命令
                    const lines = processedOutput.split('\n');
                    lines.forEach(line => {
                        const trimmedLine = line.trim();
                        if (trimmedLine.startsWith('FORWARD:')) {
                            countCommand('前进');
                        } else if (trimmedLine.startsWith('TURN_LEFT:')) {
                            countCommand('左转');
                        } else if (trimmedLine.startsWith('TURN_RIGHT:')) {
                            countCommand('右转');
                        } else if (trimmedLine.startsWith('GPP_SAY:')) {
                            countCommand('说话');
                        }
                    });
                });
                appendOutput('\n=== 测试完成 ===\n\n');
            } catch (error) {
                appendOutput(`\n错误: ${error.message}\n\n`);
            }
        }

        function testLoop3() {
            const code = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    printf("开始重复执行 3 次\\n");
    int i;
    for(i = 0; i < 3; i++) {
        printf("第 %d 次循环开始\\n", i + 1);
        printf("机器人前进：速度=4，距离=10\\n");
        printf("FORWARD:4:10\\n");
        printf("前进动作完成\\n");
        printf("机器人右转：角度=90度\\n");
        printf("TURN_RIGHT:90\\n");
        printf("右转动作完成\\n");
        printf("第 %d 次循环结束\\n", i + 1);
    }
    printf("重复执行完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            runTest(code, '循环3次测试');
        }

        function testLoop5() {
            const code = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    printf("开始重复执行 5 次\\n");
    int i;
    for(i = 0; i < 5; i++) {
        printf("第 %d 次循环开始\\n", i + 1);
        printf("机器人前进：速度=4，距离=5\\n");
        printf("FORWARD:4:5\\n");
        printf("前进动作完成\\n");
        printf("第 %d 次循环结束\\n", i + 1);
    }
    printf("重复执行完成\\n");
    printf("GPP_SAY:1:5次循环完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            runTest(code, '循环5次测试');
        }

        function testLoop10() {
            const code = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    printf("开始重复执行 10 次\\n");
    int i;
    for(i = 0; i < 10; i++) {
        printf("第 %d 次循环开始\\n", i + 1);
        printf("FORWARD:4:2\\n");
        printf("TURN_LEFT:36\\n");
        printf("第 %d 次循环结束\\n", i + 1);
    }
    printf("重复执行完成\\n");
    printf("GPP_SAY:1:绘制十边形完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            runTest(code, '循环10次测试（绘制十边形）');
        }

        function testNestedLoop() {
            const code = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    printf("开始嵌套循环测试\\n");
    int i, j;
    for(i = 0; i < 3; i++) {
        printf("外层循环第 %d 次开始\\n", i + 1);
        for(j = 0; j < 2; j++) {
            printf("  内层循环第 %d 次\\n", j + 1);
            printf("  FORWARD:4:3\\n");
            printf("  TURN_RIGHT:45\\n");
        }
        printf("外层循环第 %d 次结束\\n", i + 1);
        printf("TURN_LEFT:90\\n");
    }
    printf("嵌套循环测试完成\\n");
    printf("GPP_SAY:1:嵌套循环完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            runTest(code, '嵌套循环测试');
        }

        // 页面加载时初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
