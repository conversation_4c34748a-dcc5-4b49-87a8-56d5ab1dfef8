package com.zxy.scratchserver.controller;

import com.zxy.scratchserver.dto.ApiResponse;
import com.zxy.scratchserver.dto.CreateVersionRequest;
import com.zxy.scratchserver.dto.ProjectDetailResponse;
import com.zxy.scratchserver.dto.ProjectResponse;
import com.zxy.scratchserver.dto.SaveProjectRequest;
import com.zxy.scratchserver.dto.VersionResponse;
import com.zxy.scratchserver.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import java.util.List;

/**
 * 项目控制器
 */
@RestController
@RequestMapping("/api/project")
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    /**
     * 获取当前用户的项目列表
     * @return 项目列表
     */
    @GetMapping("/list")
    public ResponseEntity<?> getUserProjects() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            List<ProjectResponse> projects = projectService.getUserProjects(username);
            return ResponseEntity.ok(ApiResponse.success(projects));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取项目详情
     * @param projectId 项目ID
     * @return 项目详情
     */
    @GetMapping("/{projectId}")
    public ResponseEntity<?> getProjectDetail(@PathVariable Long projectId) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            ProjectDetailResponse project = projectService.getProjectDetail(projectId, username);
            return ResponseEntity.ok(ApiResponse.success(project));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 保存项目
     * @param saveProjectRequest 保存项目请求
     * @return 保存结果
     */
    @PostMapping("/save")
    public ResponseEntity<?> saveProject(@RequestBody SaveProjectRequest saveProjectRequest) {
        System.out.println("收到保存项目请求: " + saveProjectRequest);
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            System.out.println("当前用户: " + username);
            Long projectId = projectService.saveProject(saveProjectRequest, username);
            System.out.println("项目保存成功，ID: " + projectId);
            return ResponseEntity.ok(ApiResponse.success("项目保存成功", projectId));
        } catch (Exception e) {
            System.err.println("保存项目失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 测试接口
     * @return 测试结果
     */
    @GetMapping("/test")
    public ResponseEntity<?> test() {
        return ResponseEntity.ok(ApiResponse.success("测试成功"));
    }

    /**
     * 保存项目修改
     * @param saveProjectRequest 保存项目请求
     * @return 保存结果
     */
    @PostMapping("/save-modification")
    public ResponseEntity<?> saveModification(@RequestBody SaveProjectRequest saveProjectRequest) {
        System.out.println("收到保存项目修改请求: " + saveProjectRequest);
        try {
            // 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            System.out.println("当前用户: " + username);

            // 保存项目修改
            Long projectId = projectService.saveProject(saveProjectRequest, username);
            System.out.println("项目修改保存成功，ID: " + projectId);

            // 创建新版本
            VersionResponse versionResponse = projectService.createProjectVersion(projectId, username);
            System.out.println("项目版本创建成功: " + versionResponse);

            // 构建响应，包含项目ID和版本信息
            ApiResponse<Long> response = ApiResponse.success("项目修改保存成功", projectId);
            response.addData("versionInfo", versionResponse);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("保存项目修改失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取项目的最新版本号 (通过URL参数)
     * @param projectId 项目ID
     * @return 最新版本号
     */
    @GetMapping("/{projectId}/version")
    public ResponseEntity<?> getProjectVersionByPath(@PathVariable Long projectId) {
        try {
            String versionNumber = projectService.getLatestVersionNumber(projectId);
            return ResponseEntity.ok(ApiResponse.success(versionNumber));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 获取项目的最新版本号 (通过查询参数)
     * @param projectId 项目ID
     * @return 最新版本号
     */
    @GetMapping("/version")
    public ResponseEntity<?> getProjectVersion(@RequestParam Long projectId) {
        System.out.println("收到通过查询参数获取项目版本请求，项目ID: " + projectId);
        try {
            String versionNumber = projectService.getLatestVersionNumber(projectId);
            return ResponseEntity.ok(ApiResponse.success(versionNumber));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 为项目创建新版本 (通过URL参数)
     * @param projectId 项目ID
     * @return 新版本信息
     */
    @PostMapping("/{projectId}/version")
    public ResponseEntity<?> createProjectVersionByPath(@PathVariable Long projectId) {
        System.out.println("收到通过URL参数创建项目版本请求，项目ID: " + projectId);

        try {
            // 获取认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null) {
                System.err.println("创建版本失败: 未认证用户");
                return ResponseEntity.status(401).body(ApiResponse.error("未认证用户"));
            }

            String username = authentication.getName();
            System.out.println("当前用户: " + username);

            if (projectId == null) {
                System.err.println("创建版本失败: 项目ID为空");
                return ResponseEntity.badRequest().body(ApiResponse.error("项目ID不能为空"));
            }

            // 创建新版本
            VersionResponse version = projectService.createProjectVersion(projectId, username);
            return ResponseEntity.ok(ApiResponse.success("创建版本成功", version));
        } catch (Exception e) {
            System.err.println("创建版本失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 为项目创建新版本 (通过请求体)
     * @param request 创建版本请求
     * @return 新版本信息
     */
    @PostMapping("/create-version")
    public ResponseEntity<?> createProjectVersion(@RequestBody CreateVersionRequest request) {
        System.out.println("收到通过请求体创建项目版本请求: " + request);

        try {
            // 获取认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null) {
                System.err.println("创建版本失败: 未认证用户");
                return ResponseEntity.status(401).body(ApiResponse.error("未认证用户"));
            }

            String username = authentication.getName();
            System.out.println("当前用户: " + username);

            if (request.getProjectId() == null) {
                System.err.println("创建版本失败: 项目ID为空");
                return ResponseEntity.badRequest().body(ApiResponse.error("项目ID不能为空"));
            }

            // 创建新版本
            System.out.println("开始创建新版本...");
            VersionResponse version = projectService.createProjectVersion(request.getProjectId(), username);
            System.out.println("版本创建成功: " + version);

            // 构建响应
            ApiResponse<VersionResponse> response = ApiResponse.success("创建版本成功", version);
            System.out.println("返回响应: " + response);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            System.err.println("创建版本失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除项目 (通过URL参数)
     * @param projectId 项目ID
     * @return 删除结果
     */
    @DeleteMapping("/{projectId}")
    public ResponseEntity<?> deleteProject(@PathVariable Long projectId) {
        System.out.println("收到通过URL参数删除项目请求，项目ID: " + projectId);

        try {
            // 获取认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null) {
                System.err.println("删除项目失败: 未认证用户");
                return ResponseEntity.status(401).body(ApiResponse.error("未认证用户"));
            }

            String username = authentication.getName();
            System.out.println("当前用户: " + username);

            if (projectId == null) {
                System.err.println("删除项目失败: 项目ID为空");
                return ResponseEntity.badRequest().body(ApiResponse.error("项目ID不能为空"));
            }

            // 删除项目
            boolean result = projectService.deleteProject(projectId, username);

            if (result) {
                System.out.println("项目删除成功，ID: " + projectId);
                return ResponseEntity.ok(ApiResponse.success("项目删除成功"));
            } else {
                System.err.println("项目删除失败，ID: " + projectId);
                return ResponseEntity.badRequest().body(ApiResponse.error("项目删除失败"));
            }
        } catch (Exception e) {
            System.err.println("删除项目失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }

    /**
     * 删除项目 (通过请求体)
     * @param request 包含项目ID的请求
     * @return 删除结果
     */
    @PostMapping("/delete")
    public ResponseEntity<?> deleteProjectByPost(@RequestBody Map<String, Long> request) {
        Long projectId = request.get("projectId");
        System.out.println("收到通过POST请求删除项目，项目ID: " + projectId);

        try {
            // 获取认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null) {
                System.err.println("删除项目失败: 未认证用户");
                return ResponseEntity.status(401).body(ApiResponse.error("未认证用户"));
            }

            String username = authentication.getName();
            System.out.println("当前用户: " + username);

            if (projectId == null) {
                System.err.println("删除项目失败: 项目ID为空");
                return ResponseEntity.badRequest().body(ApiResponse.error("项目ID不能为空"));
            }

            // 删除项目
            boolean result = projectService.deleteProject(projectId, username);

            if (result) {
                System.out.println("项目删除成功，ID: " + projectId);
                return ResponseEntity.ok(ApiResponse.success("项目删除成功"));
            } else {
                System.err.println("项目删除失败，ID: " + projectId);
                return ResponseEntity.badRequest().body(ApiResponse.error("项目删除失败"));
            }
        } catch (Exception e) {
            System.err.println("删除项目失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        }
    }
}
