# 机器人传感器模块技术文档

## 1. 模块概述

机器人传感器模块是一个基于HTML5 Canvas的仿真环境，通过`robot_sensor.html`页面实现机器人在虚拟地图中的运动控制和传感器数据模拟。该模块与Scratch图形化编程界面深度集成，允许用户通过拖拽积木块来控制虚拟机器人，并获取传感器反馈数据。

### 核心功能
- **机器人运动仿真**：支持前进、后退、左转、右转等基本运动
- **传感器数据模拟**：颜色传感器、超声波距离传感器、循迹传感器等
- **地图环境交互**：多种地图场景，包含障碍物和彩色标记线
- **实时数据反馈**：传感器数值实时显示和更新

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scratch GUI   │    │  K1 VM Extension │    │ robot_sensor.html│
│   (积木块界面)   │◄──►│   (通信桥梁)     │◄──►│   (仿真环境)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 通信机制
- **iframe嵌入**：robot_sensor.html作为iframe嵌入到Scratch界面
- **跨窗口通信**：通过`window.robotControl`接口实现控制指令传递
- **参数传递**：支持速度、距离、角度等参数的精确传递

## 3. 机器人运动控制实现

### 3.1 机器人对象定义
```javascript
const ROBOT = {
    x: 45,           // X坐标位置
    y: 34,           // Y坐标位置
    size: 8,         // 机器人尺寸
    angle: 0,        // 朝向角度(弧度)
    speed: 2         // 移动速度
};
```

### 3.2 精确运动控制

#### 距离控制算法
```javascript
function moveRobotByDistance(distance) {
    // 计算新位置
    const newX = ROBOT.x + Math.sin(ROBOT.angle) * distance;
    const newY = ROBOT.y - Math.cos(ROBOT.angle) * distance;
    
    // 碰撞检测
    if (isValidPosition(newX, newY)) {
        ROBOT.x = newX;
        ROBOT.y = newY;
        drawGrid(); // 重绘画面
    }
}
```

#### 角度控制算法
```javascript
function rotateRobotByAngle(angleDegrees) {
    const angleRadians = angleDegrees * Math.PI / 180;
    ROBOT.angle += angleRadians;
    
    // 角度标准化到0-2π范围
    ROBOT.angle = ((ROBOT.angle % (2 * Math.PI)) + (2 * Math.PI)) % (2 * Math.PI);
    drawGrid();
}
```

### 3.3 积木块交互接口

#### 控制函数暴露
```javascript
window.robotControl = {
    forward: controlRobotForward,    // 前进控制
    back: controlRobotBack,          // 后退控制
    turnLeft: controlRobotTurnLeft,  // 左转控制
    turnRight: controlRobotTurnRight // 右转控制
};
```

#### 参数化控制实现
```javascript
function controlRobotForward(speed = 4, distance = 10) {
    console.log(`控制机器人前进，速度: ${speed}, 距离: ${distance}`);
    
    // 只控制仿真器机器人，避免双重控制
    moveRobotByDistance(distance);
    
    // 更新传感器数据
    updateSensorReadings();
}
```

## 4. 传感器仿真系统

### 4.1 颜色传感器仿真

#### 实现原理
颜色传感器通过检测机器人当前位置的Canvas像素颜色来模拟真实传感器的工作。

```javascript
function getColorAtPosition(x, y) {
    const canvas = document.getElementById('gridCanvas');
    const ctx = canvas.getContext('2d');
    
    // 获取指定位置的像素数据
    const imageData = ctx.getImageData(x, y, 1, 1);
    const pixel = imageData.data;
    
    return {
        r: pixel[0],
        g: pixel[1], 
        b: pixel[2],
        a: pixel[3]
    };
}
```

#### 颜色识别算法
```javascript
function identifyColor(rgb) {
    const { r, g, b } = rgb;
    
    // 颜色阈值判断
    if (r > 200 && g < 100 && b < 100) return "红色";
    if (r < 100 && g > 200 && b < 100) return "绿色";
    if (r < 100 && g < 100 && b > 200) return "蓝色";
    if (r > 200 && g > 200 && b < 100) return "黄色";
    if (r < 50 && g < 50 && b < 50) return "黑色";
    if (r > 200 && g > 200 && b > 200) return "白色";
    
    return "未知颜色";
}
```

#### 传感器数据更新
```javascript
function updateColorSensor() {
    const robotCenterX = ROBOT.x + ROBOT.size / 2;
    const robotCenterY = ROBOT.y + ROBOT.size / 2;
    
    const color = getColorAtPosition(robotCenterX, robotCenterY);
    const colorName = identifyColor(color);
    
    // 更新UI显示
    updateColorSensorDisplay(colorName, color);
}
```

### 4.2 超声波距离传感器仿真

#### 距离检测算法
```javascript
function getUltrasonicDistance() {
    const startX = ROBOT.x + ROBOT.size / 2;
    const startY = ROBOT.y + ROBOT.size / 2;
    
    // 沿机器人朝向发射检测射线
    const stepSize = 0.5;
    let distance = 0;
    
    for (let i = 1; i <= 200; i++) {
        const checkX = startX + Math.sin(ROBOT.angle) * i * stepSize;
        const checkY = startY - Math.cos(ROBOT.angle) * i * stepSize;
        
        // 检测是否碰到障碍物或边界
        if (isObstacle(checkX, checkY) || isOutOfBounds(checkX, checkY)) {
            distance = i * stepSize;
            break;
        }
    }
    
    return Math.round(distance);
}
```

#### 障碍物检测
```javascript
function isObstacle(x, y) {
    // 检测红色方块障碍物
    const color = getColorAtPosition(x, y);
    return (color.r > 200 && color.g < 100 && color.b < 100);
}

function isOutOfBounds(x, y) {
    return x < 0 || x >= GRID_SIZE || y < 0 || y >= GRID_SIZE;
}
```

#### 距离数据可视化
```javascript
function drawUltrasonicBeam() {
    const ctx = document.getElementById('gridCanvas').getContext('2d');
    const distance = getUltrasonicDistance();
    
    // 绘制检测射线
    ctx.strokeStyle = 'rgba(255, 255, 0, 0.5)';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const startX = ROBOT.x + ROBOT.size / 2;
    const startY = ROBOT.y + ROBOT.size / 2;
    const endX = startX + Math.sin(ROBOT.angle) * distance;
    const endY = startY - Math.cos(ROBOT.angle) * distance;
    
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
}
```

## 5. 地图环境系统

### 5.1 地图元素定义
```javascript
const MAP_ELEMENTS = {
    obstacles: [        // 红色障碍物方块
        {x: 20, y: 30, width: 10, height: 10, color: 'red'}
    ],
    colorLines: [       // 彩色标记线
        {x1: 10, y1: 40, x2: 80, y2: 40, color: 'yellow', width: 3},
        {x1: 30, y1: 20, x2: 30, y2: 60, color: 'blue', width: 3}
    ]
};
```

### 5.2 地图渲染系统
```javascript
function drawMapElements() {
    const ctx = document.getElementById('gridCanvas').getContext('2d');
    
    // 绘制障碍物
    MAP_ELEMENTS.obstacles.forEach(obstacle => {
        ctx.fillStyle = obstacle.color;
        ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
    });
    
    // 绘制彩色线条
    MAP_ELEMENTS.colorLines.forEach(line => {
        ctx.strokeStyle = line.color;
        ctx.lineWidth = line.width;
        ctx.beginPath();
        ctx.moveTo(line.x1, line.y1);
        ctx.lineTo(line.x2, line.y2);
        ctx.stroke();
    });
}
```

## 6. 实时数据反馈系统

### 6.1 传感器数据更新机制
```javascript
function updateSensorReadings() {
    // 更新颜色传感器
    updateColorSensor();
    
    // 更新超声波传感器
    const distance = getUltrasonicDistance();
    updateDistanceSensorDisplay(distance);
    
    // 更新机器人位置显示
    updateRobotPositionDisplay();
}
```

### 6.2 UI数据显示
```javascript
function updateSensorDisplay() {
    // 颜色传感器显示
    const colorDisplay = document.querySelector('.color-sensor-display');
    colorDisplay.style.backgroundColor = currentColor;
    
    // 距离传感器显示
    const distanceDisplay = document.querySelector('.distance-display');
    distanceDisplay.textContent = `${currentDistance}cm`;
    
    // 坐标显示
    const positionDisplay = document.querySelector('.position-display');
    positionDisplay.textContent = `(${Math.round(ROBOT.x)}, ${Math.round(ROBOT.y)})`;
}
```

## 7. 关键技术特性

### 7.1 智能控制切换
- **仿真器优先**：当仿真器可见时，积木块只控制仿真器机器人
- **回退机制**：仿真器不可用时自动回退到控制Scratch精灵
- **状态检测**：实时检测iframe可见性和控制接口可用性

### 7.2 精确参数传递
- **速度控制**：影响移动距离计算（实际距离 = 距离 × 速度/4）
- **距离控制**：支持任意精度的移动距离
- **角度控制**：支持任意角度的精确旋转

### 7.3 碰撞检测系统
- **边界检测**：防止机器人移出地图边界
- **障碍物检测**：检测红色方块障碍物
- **安全移动**：只有在安全位置才允许移动

## 8. 使用示例

### 8.1 基础控制
```javascript
// 前进10个单位
robotControl.forward(4, 10);

// 左转90度
robotControl.turnLeft(90);

// 后退5个单位
robotControl.back(4, 5);
```

### 8.2 传感器读取
```javascript
// 获取当前颜色
const color = getCurrentColor();

// 获取前方距离
const distance = getUltrasonicDistance();

// 检查是否在线上
const onLine = isOnColorLine('black');
```

## 9. 总结

机器人传感器模块通过Canvas技术实现了一个功能完整的机器人仿真环境，具备精确的运动控制、真实的传感器模拟和直观的数据反馈。该模块与Scratch图形化编程完美集成，为机器人编程教育提供了强大的可视化学习工具。

### 主要优势
- **教育友好**：直观的可视化界面，易于理解
- **功能完整**：涵盖运动控制和传感器仿真的核心功能
- **技术先进**：采用现代Web技术，性能优异
- **扩展性强**：模块化设计，易于添加新功能

该模块为学习者提供了从图形化编程到机器人控制的完整学习路径，是机器人编程教育的理想工具。
