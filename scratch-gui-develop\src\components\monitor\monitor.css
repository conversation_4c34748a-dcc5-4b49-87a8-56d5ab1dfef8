@import "../../css/units.css";
@import "../../css/colors.css";
@import "../../css/z-index.css";

.monitor-container {
    position: absolute;
    background: $ui-primary;
    z-index: $z-index-monitor;
    border: 1px solid $ui-black-transparent;
    border-radius: calc($space / 2);
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 0.75rem;
    transition: box-shadow 0.2s;
    pointer-events: all;
    overflow: hidden;
}

.monitor:hover {
    cursor: pointer;
}

.dragging {
    z-index: $z-index-monitor-dragging;
    box-shadow: 3px 3px 5px #888888;
}

.default-monitor {
    display: flex;
    flex-direction: column;
    padding: 3px;
}

.label {
    font-weight: bold;
    text-align: center;
    margin: 0 5px;
}

.value {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 40px;
    text-align: center;
    margin: 0 5px;
    border-radius: calc($space / 2);
    padding: 0 2px;
    white-space: pre-wrap;
    transform: translateZ(0); /* Fixes flickering in Safari */
}

.large-value {
    min-height: 1.4rem;
    min-width: 3rem;
    padding: 0.1rem 0.25rem;
    text-align: center;
    font-size: 1rem;
    white-space: pre-wrap;
    transform: translateZ(0); /* Fixes flickering in Safari */
}

.row {
    display: flex;
    flex-direction: row;
}

.slider {
    width: 100%;
    transform: translateZ(0); /* Fixes flickering in Safari */
}

.list-monitor {
    display: flex;
    flex-direction: column;
}

.list-header {
    background: $ui-white;
    border-bottom: 1px solid $ui-black-transparent;
    text-align: center;
    padding: 3px;
    font-size: 0.75rem;
    font-weight: bold;
    color: $text-primary;
    width: 100%;
}

.list-body {
    background: $ui-primary;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    height: calc(100% - 44px);
}

.list-row {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    padding: 2px;
    flex-shrink: 0;
    transform: translateZ(0); /* Keep sharp when scaled */
}

.list-index {
    font-weight: bold;
    color: $text-primary;
    margin: 0 3px;
}

.list-value {
    min-width: 40px;
    text-align: left;
    margin: 0 3px;
    border-radius: calc($space / 2);
    border: 1px solid $ui-black-transparent;
    flex-grow: 1;
    height: 22px;
}

.list-footer {
    background: $ui-white;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 3px;
    font-size: 0.75rem;
    font-weight: bold;
    color: $text-primary;
}

.list-empty {
    text-align: center;
    width: 100%;
    padding: 5px;
}

.input-wrapper {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.1);
}

.value-inner {
    padding: 3px 5px;
    min-height: 22px;
    overflow: hidden; /* Don't let long values escape container */
    text-overflow: ellipsis;
    user-select: text; /* Allow selecting list values for 2.0 compatibility, only relevant in player */
    white-space: pre;
}

.list-input {
    padding: 3px 5px;
    border: 0;
    background: none;
    outline: none;
    font-size: 0.75rem;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    /* Workaround for Firefox */
    width: 0;
    flex: 1 1 auto;
}

.remove-button {
    padding: 0;
    padding-right: 5px;
    cursor: pointer;
}

.add-button {
    cursor: pointer;
    margin-right: 3px;
}

.resize-handle {
    cursor: nwse-resize;
    margin-left: 3px;
}

.footer-length {
    text-align: center;
}
