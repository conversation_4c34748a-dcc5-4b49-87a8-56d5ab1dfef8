// Intro
import introMove from './steps/intro-1-move.ar.gif';
import introSay from './steps/intro-2-say.ar.gif';
import introGreenFlag from './steps/intro-3-green-flag.ar.gif';

// Text to Speech
import speechAddExtension from './steps/speech-add-extension.ar.gif';
import speechSaySomething from './steps/speech-say-something.ar.png';
import speechSetVoice from './steps/speech-set-voice.ar.png';
import speechMoveAround from './steps/speech-move-around.ar.png';
import speechAddBackdrop from './steps/add-backdrop.RTL.png';
import speechAddSprite from './steps/speech-add-sprite.RTL.gif';
import speechSong from './steps/speech-song.ar.png';
import speechChangeColor from './steps/speech-change-color.ar.png';
import speechSpin from './steps/speech-spin.ar.png';
import speechGrowShrink from './steps/speech-grow-shrink.ar.png';

// Cartoon Network
import cnShowCharacter from './steps/cn-show-character.LTR.gif';
import cnSay from './steps/cn-say.ar.png';
import cnGlide from './steps/cn-glide.ar.png';
import cnPickSprite from './steps/cn-pick-sprite.RTL.gif';
import cnCollect from './steps/cn-collect.ar.png';
import cnVariable from './steps/add-variable.ar.gif';
import cnScore from './steps/cn-score.ar.png';
import cnBackdrop from './steps/cn-backdrop.ar.png';

// Add sprite
import addSprite from './steps/add-sprite.RTL.gif';

// Animate a name
import namePickLetter from './steps/name-pick-letter.RTL.gif';
import namePlaySound from './steps/name-play-sound.ar.png';
import namePickLetter2 from './steps/name-pick-letter2.RTL.gif';
import nameChangeColor from './steps/name-change-color.ar.png';
import nameSpin from './steps/name-spin.ar.png';
import nameGrow from './steps/name-grow.ar.png';

// Make Music
import musicPickInstrument from './steps/music-pick-instrument.RTL.gif';
import musicPlaySound from './steps/music-play-sound.ar.png';
import musicMakeSong from './steps/music-make-song.ar.png';
import musicMakeBeat from './steps/music-make-beat.ar.png';
import musicMakeBeatbox from './steps/music-make-beatbox.ar.png';

// Chase-Game
import chaseGameAddBackdrop from './steps/chase-game-add-backdrop.RTL.gif';
import chaseGameAddSprite1 from './steps/chase-game-add-sprite1.RTL.gif';
import chaseGameRightLeft from './steps/chase-game-right-left.ar.png';
import chaseGameUpDown from './steps/chase-game-up-down.ar.png';
import chaseGameAddSprite2 from './steps/chase-game-add-sprite2.RTL.gif';
import chaseGameMoveRandomly from './steps/chase-game-move-randomly.ar.png';
import chaseGamePlaySound from './steps/chase-game-play-sound.ar.png';
import chaseGameAddVariable from './steps/add-variable.ar.gif';
import chaseGameChangeScore from './steps/chase-game-change-score.ar.png';

// Clicker-Game (Pop Game)
import popGamePickSprite from './steps/pop-game-pick-sprite.RTL.gif';
import popGamePlaySound from './steps/pop-game-play-sound.ar.png';
import popGameAddScore from './steps/add-variable.ar.gif';
import popGameChangeScore from './steps/pop-game-change-score.ar.png';
import popGameRandomPosition from './steps/pop-game-random-position.ar.png';
import popGameChangeColor from './steps/pop-game-change-color.ar.png';
import popGameResetScore from './steps/pop-game-reset-score.ar.png';

// Animate A Character
import animateCharPickBackdrop from './steps/animate-char-pick-backdrop.RTL.png';
import animateCharPickSprite from './steps/animate-char-pick-sprite.RTL.gif';
import animateCharSaySomething from './steps/animate-char-say-something.ar.png';
import animateCharAddSound from './steps/animate-char-add-sound.ar.png';
import animateCharTalk from './steps/animate-char-talk.ar.png';
import animateCharMove from './steps/animate-char-move.ar.png';
import animateCharJump from './steps/animate-char-jump.ar.png';
import animateCharChangeColor from './steps/animate-char-change-color.ar.png';

// Tell A Story
import storyPickBackdrop from './steps/story-pick-backdrop.RTL.gif';
import storyPickSprite from './steps/story-pick-sprite.RTL.gif';
import storySaySomething from './steps/story-say-something.ar.png';
import storyPickSprite2 from './steps/story-pick-sprite2.RTL.gif';
import storyFlip from './steps/story-flip.ar.gif';
import storyConversation from './steps/story-conversation.ar.png';
import storyPickBackdrop2 from './steps/story-pick-backdrop2.RTL.gif';
import storySwitchBackdrop from './steps/story-switch-backdrop.ar.png';
import storyHideCharacter from './steps/story-hide-character.ar.png';
import storyShowCharacter from './steps/story-show-character.ar.png';

// Video Sensing
import videoAddExtension from './steps/video-add-extension.ar.gif';
import videoPet from './steps/video-pet.ar.png';
import videoAnimate from './steps/video-animate.ar.png';
import videoPop from './steps/video-pop.ar.png';

// Make it Fly
import flyChooseBackdrop from './steps/fly-choose-backdrop.RTL.gif';
import flyChooseCharacter from './steps/fly-choose-character.RTL.png';
import flySaySomething from './steps/fly-say-something.ar.png';
import flyMoveArrows from './steps/fly-make-interactive.ar.png';
import flyChooseObject from './steps/fly-object-to-collect.RTL.png';
import flyFlyingObject from './steps/fly-flying-heart.ar.png';
import flySelectFlyingSprite from './steps/fly-select-flyer.RTL.png';
import flyAddScore from './steps/add-variable.ar.gif';
import flyKeepScore from './steps/fly-keep-score.ar.png';
import flyAddScenery from './steps/fly-choose-scenery.RTL.gif';
import flyMoveScenery from './steps/fly-move-scenery.ar.png';
import flySwitchLooks from './steps/fly-switch-costume.ar.png';

// Pong
import pongAddBackdrop from './steps/pong-add-backdrop.RTL.png';
import pongAddBallSprite from './steps/pong-add-ball-sprite.RTL.png';
import pongBounceAround from './steps/pong-bounce-around.ar.png';
import pongAddPaddle from './steps/pong-add-a-paddle.RTL.gif';
import pongMoveThePaddle from './steps/pong-move-the-paddle.ar.png';
import pongSelectBallSprite from './steps/pong-select-ball.RTL.png';
import pongAddMoreCodeToBall from './steps/pong-add-code-to-ball.ar.png';
import pongAddAScore from './steps/add-variable.ar.gif';
import pongChooseScoreFromMenu from './steps/pong-choose-score.ar.png';
import pongInsertChangeScoreBlock from './steps/pong-insert-change-score.ar.png';
import pongResetScore from './steps/pong-reset-score.ar.png';
import pongAddLineSprite from './steps/pong-add-line.RTL.gif';
import pongGameOver from './steps/pong-game-over.ar.png';

// Imagine a World
import imagineTypeWhatYouWant from './steps/imagine-type-what-you-want.ar.png';
import imagineClickGreenFlag from './steps/imagine-click-green-flag.ar.png';
import imagineChooseBackdrop from './steps/imagine-choose-backdrop.RTL.png';
import imagineChooseSprite from './steps/imagine-choose-any-sprite.RTL.png';
import imagineFlyAround from './steps/imagine-fly-around.ar.png';
import imagineChooseAnotherSprite from './steps/imagine-choose-another-sprite.RTL.png';
import imagineLeftRight from './steps/imagine-left-right.ar.png';
import imagineUpDown from './steps/imagine-up-down.ar.png';
import imagineChangeCostumes from './steps/imagine-change-costumes.ar.png';
import imagineGlideToPoint from './steps/imagine-glide-to-point.ar.png';
import imagineGrowShrink from './steps/imagine-grow-shrink.ar.png';
import imagineChooseAnotherBackdrop from './steps/imagine-choose-another-backdrop.RTL.png';
import imagineSwitchBackdrops from './steps/imagine-switch-backdrops.ar.png';
import imagineRecordASound from './steps/imagine-record-a-sound.ar.gif';
import imagineChooseSound from './steps/imagine-choose-sound.ar.png';

// Add a Backdrop
import addBackdrop from './steps/add-backdrop.RTL.png';

// Add Effects
import addEffects from './steps/add-effects.ar.png';

// Hide and Show
import hideAndShow from './steps/hide-show.ar.png';

// Switch Costumes
import switchCostumes from './steps/switch-costumes.ar.png';

// Change Size
import changeSize from './steps/change-size.ar.png';

// Spin
import spinTurn from './steps/spin-turn.ar.png';
import spinPointInDirection from './steps/spin-point-in-direction.ar.png';

// Record a Sound
import recordASoundSoundsTab from './steps/record-a-sound-sounds-tab.ar.png';
import recordASoundClickRecord from './steps/record-a-sound-click-record.ar.png';
import recordASoundPressRecordButton from './steps/record-a-sound-press-record-button.ar.png';
import recordASoundChooseSound from './steps/record-a-sound-choose-sound.ar.png';
import recordASoundPlayYourSound from './steps/record-a-sound-play-your-sound.ar.png';

// Use Arrow Keys
import moveArrowKeysLeftRight from './steps/move-arrow-keys-left-right.ar.png';
import moveArrowKeysUpDown from './steps/move-arrow-keys-up-down.ar.png';

// Glide Around
import glideAroundBackAndForth from './steps/glide-around-back-and-forth.ar.png';
import glideAroundPoint from './steps/glide-around-point.ar.png';

// Code a Cartoon
import codeCartoonSaySomething from './steps/code-cartoon-01-say-something.ar.png';
import codeCartoonAnimate from './steps/code-cartoon-02-animate.ar.png';
import codeCartoonSelectDifferentCharacter from './steps/code-cartoon-03-select-different-character.RTL.png';
import codeCartoonUseMinusSign from './steps/code-cartoon-04-use-minus-sign.ar.png';
import codeCartoonGrowShrink from './steps/code-cartoon-05-grow-shrink.ar.png';
import codeCartoonSelectDifferentCharacter2 from './steps/code-cartoon-06-select-another-different-character.RTL.png';
import codeCartoonJump from './steps/code-cartoon-07-jump.ar.png';
import codeCartoonChangeScenes from './steps/code-cartoon-08-change-scenes.ar.png';
import codeCartoonGlideAround from './steps/code-cartoon-09-glide-around.ar.png';
import codeCartoonChangeCostumes from './steps/code-cartoon-10-change-costumes.ar.png';
import codeCartoonChooseMoreCharacters from './steps/code-cartoon-11-choose-more-characters.RTL.png';

// Talking Tales
import talesAddExtension from './steps/speech-add-extension.ar.gif';
import talesChooseSprite from './steps/talking-2-choose-sprite.RTL.png';
import talesSaySomething from './steps/talking-3-say-something.ar.png';
import talesChooseBackdrop from './steps/talking-4-choose-backdrop.RTL.png';
import talesSwitchBackdrop from './steps/talking-5-switch-backdrop.ar.png';
import talesChooseAnotherSprite from './steps/talking-6-choose-another-sprite.RTL.png';
import talesMoveAround from './steps/talking-7-move-around.ar.png';
import talesChooseAnotherBackdrop from './steps/talking-8-choose-another-backdrop.RTL.png';
import talesAnimateTalking from './steps/talking-9-animate.ar.png';
import talesChooseThirdBackdrop from './steps/talking-10-choose-third-backdrop.RTL.png';
import talesChooseSound from './steps/talking-11-choose-sound.ar.gif';
import talesDanceMoves from './steps/talking-12-dance-moves.ar.png';
import talesAskAnswer from './steps/talking-13-ask-and-answer.ar.png';

const arImages = {
    // Intro
    introMove: introMove,
    introSay: introSay,
    introGreenFlag: introGreenFlag,

    // Text to Speech
    speechAddExtension: speechAddExtension,
    speechSaySomething: speechSaySomething,
    speechSetVoice: speechSetVoice,
    speechMoveAround: speechMoveAround,
    speechAddBackdrop: speechAddBackdrop,
    speechAddSprite: speechAddSprite,
    speechSong: speechSong,
    speechChangeColor: speechChangeColor,
    speechSpin: speechSpin,
    speechGrowShrink: speechGrowShrink,

    // Cartoon Network
    cnShowCharacter: cnShowCharacter,
    cnSay: cnSay,
    cnGlide: cnGlide,
    cnPickSprite: cnPickSprite,
    cnCollect: cnCollect,
    cnVariable: cnVariable,
    cnScore: cnScore,
    cnBackdrop: cnBackdrop,

    // Add sprite
    addSprite: addSprite,

    // Animate a name
    namePickLetter: namePickLetter,
    namePlaySound: namePlaySound,
    namePickLetter2: namePickLetter2,
    nameChangeColor: nameChangeColor,
    nameSpin: nameSpin,
    nameGrow: nameGrow,

    // Make-Music
    musicPickInstrument: musicPickInstrument,
    musicPlaySound: musicPlaySound,
    musicMakeSong: musicMakeSong,
    musicMakeBeat: musicMakeBeat,
    musicMakeBeatbox: musicMakeBeatbox,

    // Chase-Game
    chaseGameAddBackdrop: chaseGameAddBackdrop,
    chaseGameAddSprite1: chaseGameAddSprite1,
    chaseGameRightLeft: chaseGameRightLeft,
    chaseGameUpDown: chaseGameUpDown,
    chaseGameAddSprite2: chaseGameAddSprite2,
    chaseGameMoveRandomly: chaseGameMoveRandomly,
    chaseGamePlaySound: chaseGamePlaySound,
    chaseGameAddVariable: chaseGameAddVariable,
    chaseGameChangeScore: chaseGameChangeScore,

    // Make-A-Pop/Clicker Game
    popGamePickSprite: popGamePickSprite,
    popGamePlaySound: popGamePlaySound,
    popGameAddScore: popGameAddScore,
    popGameChangeScore: popGameChangeScore,
    popGameRandomPosition: popGameRandomPosition,
    popGameChangeColor: popGameChangeColor,
    popGameResetScore: popGameResetScore,

    // Animate A Character
    animateCharPickBackdrop: animateCharPickBackdrop,
    animateCharPickSprite: animateCharPickSprite,
    animateCharSaySomething: animateCharSaySomething,
    animateCharAddSound: animateCharAddSound,
    animateCharTalk: animateCharTalk,
    animateCharMove: animateCharMove,
    animateCharJump: animateCharJump,
    animateCharChangeColor: animateCharChangeColor,

    // Tell A Story
    storyPickBackdrop: storyPickBackdrop,
    storyPickSprite: storyPickSprite,
    storySaySomething: storySaySomething,
    storyPickSprite2: storyPickSprite2,
    storyFlip: storyFlip,
    storyConversation: storyConversation,
    storyPickBackdrop2: storyPickBackdrop2,
    storySwitchBackdrop: storySwitchBackdrop,
    storyHideCharacter: storyHideCharacter,
    storyShowCharacter: storyShowCharacter,

    // Video Sensing
    videoAddExtension: videoAddExtension,
    videoPet: videoPet,
    videoAnimate: videoAnimate,
    videoPop: videoPop,

    // Make it Fly
    flyChooseBackdrop: flyChooseBackdrop,
    flyChooseCharacter: flyChooseCharacter,
    flySaySomething: flySaySomething,
    flyMoveArrows: flyMoveArrows,
    flyChooseObject: flyChooseObject,
    flyFlyingObject: flyFlyingObject,
    flySelectFlyingSprite: flySelectFlyingSprite,
    flyAddScore: flyAddScore,
    flyKeepScore: flyKeepScore,
    flyAddScenery: flyAddScenery,
    flyMoveScenery: flyMoveScenery,
    flySwitchLooks: flySwitchLooks,

    // Pong
    pongAddBackdrop: pongAddBackdrop,
    pongAddBallSprite: pongAddBallSprite,
    pongBounceAround: pongBounceAround,
    pongAddPaddle: pongAddPaddle,
    pongMoveThePaddle: pongMoveThePaddle,
    pongSelectBallSprite: pongSelectBallSprite,
    pongAddMoreCodeToBall: pongAddMoreCodeToBall,
    pongAddAScore: pongAddAScore,
    pongChooseScoreFromMenu: pongChooseScoreFromMenu,
    pongInsertChangeScoreBlock: pongInsertChangeScoreBlock,
    pongResetScore: pongResetScore,
    pongAddLineSprite: pongAddLineSprite,
    pongGameOver: pongGameOver,

    // Imagine a World
    imagineTypeWhatYouWant: imagineTypeWhatYouWant,
    imagineClickGreenFlag: imagineClickGreenFlag,
    imagineChooseBackdrop: imagineChooseBackdrop,
    imagineChooseSprite: imagineChooseSprite,
    imagineFlyAround: imagineFlyAround,
    imagineChooseAnotherSprite: imagineChooseAnotherSprite,
    imagineLeftRight: imagineLeftRight,
    imagineUpDown: imagineUpDown,
    imagineChangeCostumes: imagineChangeCostumes,
    imagineGlideToPoint: imagineGlideToPoint,
    imagineGrowShrink: imagineGrowShrink,
    imagineChooseAnotherBackdrop: imagineChooseAnotherBackdrop,
    imagineSwitchBackdrops: imagineSwitchBackdrops,
    imagineRecordASound: imagineRecordASound,
    imagineChooseSound: imagineChooseSound,

    // Add a Backdrop
    addBackdrop: addBackdrop,

    // Add Effects
    addEffects: addEffects,

    // Hide and Show
    hideAndShow: hideAndShow,

    // Switch Costumes
    switchCostumes: switchCostumes,

    // Change Size
    changeSize: changeSize,

    // Spin
    spinTurn: spinTurn,
    spinPointInDirection: spinPointInDirection,

    // Record a Sound
    recordASoundSoundsTab: recordASoundSoundsTab,
    recordASoundClickRecord: recordASoundClickRecord,
    recordASoundPressRecordButton: recordASoundPressRecordButton,
    recordASoundChooseSound: recordASoundChooseSound,
    recordASoundPlayYourSound: recordASoundPlayYourSound,

    // Use Arrow Keys
    moveArrowKeysLeftRight: moveArrowKeysLeftRight,
    moveArrowKeysUpDown: moveArrowKeysUpDown,

    // Glide Around
    glideAroundBackAndForth: glideAroundBackAndForth,
    glideAroundPoint: glideAroundPoint,

    // Code a Cartoon
    codeCartoonSaySomething: codeCartoonSaySomething,
    codeCartoonAnimate: codeCartoonAnimate,
    codeCartoonSelectDifferentCharacter: codeCartoonSelectDifferentCharacter,
    codeCartoonUseMinusSign: codeCartoonUseMinusSign,
    codeCartoonGrowShrink: codeCartoonGrowShrink,
    codeCartoonSelectDifferentCharacter2: codeCartoonSelectDifferentCharacter2,
    codeCartoonJump: codeCartoonJump,
    codeCartoonChangeScenes: codeCartoonChangeScenes,
    codeCartoonGlideAround: codeCartoonGlideAround,
    codeCartoonChangeCostumes: codeCartoonChangeCostumes,
    codeCartoonChooseMoreCharacters: codeCartoonChooseMoreCharacters,

    // Talking Tales
    talesAddExtension: talesAddExtension,
    talesChooseSprite: talesChooseSprite,
    talesSaySomething: talesSaySomething,
    talesAskAnswer: talesAskAnswer,
    talesChooseBackdrop: talesChooseBackdrop,
    talesSwitchBackdrop: talesSwitchBackdrop,
    talesChooseAnotherSprite: talesChooseAnotherSprite,
    talesMoveAround: talesMoveAround,
    talesChooseAnotherBackdrop: talesChooseAnotherBackdrop,
    talesAnimateTalking: talesAnimateTalking,
    talesChooseThirdBackdrop: talesChooseThirdBackdrop,
    talesChooseSound: talesChooseSound,
    talesDanceMoves: talesDanceMoves
};

export {arImages};
