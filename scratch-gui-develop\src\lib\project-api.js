/**
 * 项目相关的API服务
 * 这个文件包含了与项目相关的API调用
 */

import { getToken } from './auth-service';

// 后端API的基础URL
const API_BASE_URL = 'http://localhost:8080/api';

/**
 * 创建带有认证头的请求选项
 * @param {Object} options - 请求选项
 * @returns {Object} - 带有认证头的请求选项
 */
const createAuthHeaders = (options = {}) => {
    const token = getToken();
    const headers = options.headers || {};

    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    // 确保Content-Type头是正确的
    if (options.method === 'POST' || options.method === 'PUT') {
        headers['Content-Type'] = 'application/json';
    }

    console.log('创建请求头:', headers);

    return {
        ...options,
        headers: headers
    };
};

/**
 * 处理API响应
 * @param {Response} response - fetch API的响应对象
 * @returns {Promise} - 处理后的响应数据
 */
const handleResponse = (response) => {
    if (!response.ok) {
        // 对于非2xx响应，先尝试解析错误消息
        return response.json()
            .then(errorData => {
                throw new Error(errorData.message || `请求失败，状态码: ${response.status}`);
            })
            .catch(err => {
                if (err.message) {
                    throw err;
                }
                throw new Error(`请求失败，状态码: ${response.status}`);
            });
    }
    return response.json();
};

/**
 * 获取当前用户的项目列表
 * @returns {Promise} - 返回项目列表的Promise
 */
const fetchProjects = () => {
    console.log('API fetchProjects 函数被调用');

    const url = `${API_BASE_URL}/project/list`;
    const options = createAuthHeaders({
        method: 'GET'
    });

    console.log(`发送获取项目列表请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('获取项目列表成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('获取项目列表失败:', error);
            throw error;
        });
};

/**
 * 获取项目详情
 * @param {number} projectId - 项目ID
 * @returns {Promise} - 返回项目详情的Promise
 */
const fetchProjectDetail = (projectId) => {
    console.log('API fetchProjectDetail 函数被调用:', projectId);

    const url = `${API_BASE_URL}/project/${projectId}`;
    const options = createAuthHeaders({
        method: 'GET'
    });

    console.log(`发送获取项目详情请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('获取项目详情成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('获取项目详情失败:', error);
            throw error;
        });
};

/**
 * 保存项目到服务器
 * @param {string} projectName 项目名称
 * @param {Blob} projectBlob 项目二进制数据
 * @returns {Promise} 保存结果
 */
const saveProject = (projectName, projectBlob) => {
    console.log('API saveProject 函数被调用:', projectName);

    return new Promise((resolve, reject) => {
        // 将Blob转换为ArrayBuffer
        const reader = new FileReader();
        reader.onload = () => {
            const arrayBuffer = reader.result;
            const uint8Array = new Uint8Array(arrayBuffer);

            // 检查数据大小
            const dataSizeBytes = uint8Array.length;
            const dataSizeMB = dataSizeBytes / 1024 / 1024;
            console.log(`项目数据大小: ${dataSizeBytes} 字节 (${dataSizeMB.toFixed(2)} MB)`);

            // 警告数据大小
            if (dataSizeMB > 16) {
                console.warn(`警告：项目数据大小超过16MB，可能需要修改数据库表结构`);
            }

            // 创建请求对象
            const request = {
                projectName: projectName,
                projectBlocks: Array.from(uint8Array)
            };

            // 先尝试调用测试接口，确认后端服务正常
            const testUrl = `${API_BASE_URL}/project/test`;
            const testOptions = createAuthHeaders({
                method: 'GET'
            });

            console.log(`发送测试请求至: ${testUrl}`);

            fetch(testUrl, testOptions)
                .then(response => {
                    console.log('测试接口响应:', response.status, response.statusText);
                    if (!response.ok) {
                        throw new Error(`测试接口请求失败，状态码: ${response.status}`);
                    }
                    return response.json();
                })
                .then(() => {
                    // 测试接口正常，继续发送保存请求
                    const url = `${API_BASE_URL}/project/save`;
                    const options = createAuthHeaders({
                        method: 'POST',
                        body: JSON.stringify(request)
                    });

                    console.log(`发送保存项目请求至: ${url}`);
                    console.log('请求方法:', options.method);
                    console.log('请求头:', options.headers);

                    return fetch(url, options);
                })
                .then(response => {
                    console.log('保存接口响应:', response.status, response.statusText);
                    return handleResponse(response);
                })
                .then(data => {
                    console.log('保存项目成功，返回数据:', data);

                    // 如果保存成功且返回了项目ID，创建新版本
                    if (data && data.data) {
                        const projectId = data.data;
                        console.log('准备为项目创建版本，项目ID:', projectId);

                        // 确保projectId是数字
                        const numericProjectId = Number(projectId);
                        if (isNaN(numericProjectId)) {
                            console.error('项目ID不是有效数字:', projectId);
                            resolve(data);
                            return;
                        }

                        // 使用新的API端点创建版本
                        const versionUrl = `${API_BASE_URL}/project/create-version`;
                        const versionOptions = createAuthHeaders({
                            method: 'POST',
                            body: JSON.stringify({
                                projectId: numericProjectId
                            })
                        });

                        console.log(`发送创建版本请求至: ${versionUrl}`);

                        fetch(versionUrl, versionOptions)
                            .then(response => {
                                console.log('版本创建响应状态:', response.status);
                                return handleResponse(response);
                            })
                            .then(versionData => {
                                console.log('创建版本成功:', versionData);
                                // 合并版本信息到返回数据
                                data.versionInfo = versionData.data;
                                resolve(data);
                            })
                            .catch(versionError => {
                                console.error('创建版本失败，但项目已保存:', versionError);
                                console.error('错误详情:', versionError);
                                // 即使版本创建失败，也返回项目保存成功的结果
                                resolve(data);
                            });
                    } else {
                        console.warn('保存项目成功，但未返回项目ID，无法创建版本');
                        resolve(data);
                    }
                })
                .catch(error => {
                    console.error('保存项目失败:', error);
                    reject(error);
                });
        };

        reader.onerror = () => {
            const error = new Error('读取项目文件失败');
            console.error(error);
            reject(error);
        };

        reader.readAsArrayBuffer(projectBlob);
    });
};

/**
 * 为项目创建新版本
 * @param {number} projectId 项目ID
 * @returns {Promise} 创建结果
 */
const createProjectVersion = (projectId) => {
    console.log('API createProjectVersion 函数被调用:', projectId);

    if (!projectId) {
        console.error('创建版本失败: 项目ID为空');
        return Promise.reject(new Error('项目ID不能为空'));
    }

    // 使用新的API端点，不在URL中包含项目ID
    const url = `${API_BASE_URL}/project/create-version`;

    // 获取认证令牌
    const token = getToken();
    if (!token) {
        console.error('创建版本失败: 未找到认证令牌');
        return Promise.reject(new Error('未找到认证令牌'));
    }

    // 创建请求选项，将项目ID放在请求体中
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            projectId: projectId
        })
    };

    console.log(`发送创建版本请求至: ${url}`);
    console.log('请求选项:', options);

    // 使用fetch API
    return fetch(url, options)
        .then(response => {
            console.log('版本创建响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`请求失败，状态码: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('创建版本成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('创建版本失败:', error);
            throw error;
        });
};

/**
 * 获取项目的最新版本号
 * @param {number} projectId 项目ID
 * @returns {Promise} 版本号
 */
const getProjectVersion = (projectId) => {
    console.log('API getProjectVersion 函数被调用:', projectId);

    // 使用查询参数而不是路径参数
    const url = `${API_BASE_URL}/project/version?projectId=${projectId}`;
    const options = createAuthHeaders({
        method: 'GET'
    });

    console.log(`发送获取版本请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('获取版本成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('获取版本失败:', error);
            throw error;
        });
};

/**
 * 保存项目修改到服务器
 * @param {string} projectName 项目名称
 * @param {Blob} projectBlob 项目二进制数据
 * @param {string} projectId 项目ID
 * @returns {Promise} 保存结果
 */
const saveModification = (projectName, projectBlob, projectId) => {
    console.log('API saveModification 函数被调用:', projectName, '项目ID:', projectId);

    return new Promise((resolve, reject) => {
        // 将Blob转换为ArrayBuffer
        const reader = new FileReader();
        reader.onload = () => {
            const arrayBuffer = reader.result;
            const uint8Array = new Uint8Array(arrayBuffer);

            // 检查数据大小
            const dataSizeBytes = uint8Array.length;
            const dataSizeMB = dataSizeBytes / 1024 / 1024;
            console.log(`项目数据大小: ${dataSizeBytes} 字节 (${dataSizeMB.toFixed(2)} MB)`);

            // 创建请求对象
            const request = {
                projectId: projectId,
                projectName: projectName,
                projectBlocks: Array.from(uint8Array)
            };

            // 发送保存修改请求
            const url = `${API_BASE_URL}/project/save-modification`;
            const options = createAuthHeaders({
                method: 'POST',
                body: JSON.stringify(request)
            });

            console.log(`发送保存修改请求至: ${url}`);

            fetch(url, options)
                .then(response => {
                    console.log('保存修改响应状态:', response.status);
                    return handleResponse(response);
                })
                .then(data => {
                    console.log('保存修改成功，返回数据:', data);
                    resolve(data);
                })
                .catch(error => {
                    console.error('保存修改失败:', error);
                    reject(error);
                });
        };

        reader.onerror = () => {
            const error = new Error('读取项目文件失败');
            console.error(error);
            reject(error);
        };

        reader.readAsArrayBuffer(projectBlob);
    });
};

/**
 * 删除项目
 * @param {number} projectId 项目ID
 * @returns {Promise} 删除结果
 */
const deleteProject = (projectId) => {
    console.log('API deleteProject 函数被调用:', projectId);

    if (!projectId) {
        console.error('删除项目失败: 项目ID为空');
        return Promise.reject(new Error('项目ID不能为空'));
    }

    // 使用POST方法代替DELETE方法，因为某些环境可能不支持DELETE
    const url = `${API_BASE_URL}/project/delete`;
    const options = createAuthHeaders({
        method: 'POST',
        body: JSON.stringify({ projectId: projectId })
    });

    console.log(`发送删除项目请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('删除项目成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('删除项目失败:', error);
            throw error;
        });
};

export {
    fetchProjects,
    fetchProjectDetail,
    saveProject,
    createProjectVersion,
    getProjectVersion,
    saveModification,
    deleteProject
};
