<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无绿旗积木块测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .code-area {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            min-height: 200px;
            border: 1px solid #ddd;
        }
        .output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            min-height: 200px;
            margin: 10px 0;
            overflow-y: auto;
            max-height: 400px;
            line-height: 1.6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .two-column {
            display: flex;
            gap: 20px;
        }
        .column {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>无绿旗积木块测试</h1>
        <p>测试没有绿旗点击事件的循环积木块是否能正确执行指定次数</p>

        <div id="status" class="status info">正在初始化...</div>

        <div class="two-column">
            <div class="column">
                <h3>测试选项</h3>
                <button class="btn" onclick="testDirectLoop()" id="directBtn" disabled>直接循环测试</button>
                <button class="btn" onclick="testDirectMovement()" id="movementBtn" disabled>直接运动测试</button>
                <button class="btn" onclick="testComplexLoop()" id="complexBtn" disabled>复杂循环测试</button>
                
                <h4>生成的C代码：</h4>
                <div id="codeArea" class="code-area">等待选择测试...</div>
                
                <button class="btn" onclick="executeCode()" id="executeBtn" disabled>执行C代码</button>
            </div>
            
            <div class="column">
                <h3>执行结果</h3>
                <div id="output" class="output">等待执行...</div>
            </div>
        </div>
    </div>

    <script src="./static/picoc-test/bundle.umd.js"></script>
    <script>
        let picocjs = null;
        let currentCode = '';
        
        // 初始化
        function init() {
            const statusDiv = document.getElementById('status');
            
            if (window.picocjs) {
                picocjs = window.picocjs;
                statusDiv.textContent = 'PicoC加载成功！可以开始测试。';
                statusDiv.className = 'status success';
                
                document.getElementById('directBtn').disabled = false;
                document.getElementById('movementBtn').disabled = false;
                document.getElementById('complexBtn').disabled = false;
            } else {
                statusDiv.textContent = 'PicoC加载失败';
                statusDiv.className = 'status error';
            }
        }

        // 测试直接循环（无绿旗事件）
        function testDirectLoop() {
            currentCode = `#include <stdio.h>
#include <stdlib.h>
#include <unistd.h> // sleep函数

// 为使用K1机器人函数添加的函数声明
void forward(int speed, int destance);
void back(int speed, int destance);
void turn_left(int degree);
void turn_right(int degree);
void gpp_say(int mode, void *str);
int servo_open(void);
int servo_close(void);
int tracker_start(void);
int tracker_close(void);
void beep(int bound, int time);
void colorful_led(int mode, int rgb);
void Set_CScript_Mode(int mode);
void cexit(void);
int lightsensor(void);
int distsensor(void);
int mic1sensor(void);
int tracker(int id);
int Get_Ps2Value(void);

int main() {
    printf("程序开始执行\\n");
    // 重复执行 10 次
    printf("开始重复执行 10 次\\n");
    for (int i = 0; i < 10; i++) {
        printf("第 %d 次循环开始\\n", i + 1);
        // 移动 5 步
        printf("开始移动 5 步\\n");
        printf("FORWARD:4:5\\n");
        printf("移动完成\\n");
        // 向右旋转 36 度
        printf("开始向右旋转 36 度\\n");
        printf("TURN_RIGHT:36\\n");
        printf("右转完成\\n");
        printf("第 %d 次循环结束\\n", i + 1);
    }
    printf("重复执行完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            document.getElementById('codeArea').textContent = currentCode;
            document.getElementById('executeBtn').disabled = false;
        }

        // 测试直接运动（无绿旗事件）
        function testDirectMovement() {
            currentCode = `#include <stdio.h>
#include <stdlib.h>
#include <unistd.h> // sleep函数

// 为使用K1机器人函数添加的函数声明
void forward(int speed, int destance);
void back(int speed, int destance);
void turn_left(int degree);
void turn_right(int degree);
void gpp_say(int mode, void *str);
int servo_open(void);
int servo_close(void);
int tracker_start(void);
int tracker_close(void);
void beep(int bound, int time);
void colorful_led(int mode, int rgb);
void Set_CScript_Mode(int mode);
void cexit(void);
int lightsensor(void);
int distsensor(void);
int mic1sensor(void);
int tracker(int id);
int Get_Ps2Value(void);

int main() {
    printf("程序开始执行\\n");
    // 移动 20 步
    printf("开始移动 20 步\\n");
    printf("FORWARD:4:20\\n");
    printf("移动完成\\n");
    // 向右旋转 90 度
    printf("开始向右旋转 90 度\\n");
    printf("TURN_RIGHT:90\\n");
    printf("右转完成\\n");
    // 移动 15 步
    printf("开始移动 15 步\\n");
    printf("FORWARD:4:15\\n");
    printf("移动完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            document.getElementById('codeArea').textContent = currentCode;
            document.getElementById('executeBtn').disabled = false;
        }

        // 测试复杂循环（无绿旗事件）
        function testComplexLoop() {
            currentCode = `#include <stdio.h>
#include <stdlib.h>
#include <unistd.h> // sleep函数

// 为使用K1机器人函数添加的函数声明
void forward(int speed, int destance);
void back(int speed, int destance);
void turn_left(int degree);
void turn_right(int degree);
void gpp_say(int mode, void *str);
int servo_open(void);
int servo_close(void);
int tracker_start(void);
int tracker_close(void);
void beep(int bound, int time);
void colorful_led(int mode, int rgb);
void Set_CScript_Mode(int mode);
void cexit(void);
int lightsensor(void);
int distsensor(void);
int mic1sensor(void);
int tracker(int id);
int Get_Ps2Value(void);

int main() {
    printf("程序开始执行\\n");
    // 重复执行 5 次
    printf("开始重复执行 5 次\\n");
    for (int i = 0; i < 5; i++) {
        printf("第 %d 次循环开始\\n", i + 1);
        // 前进，速度: 4，距离: 8
        printf("机器人前进：速度=4，距离=8\\n");
        printf("FORWARD:4:8\\n");
        printf("前进动作完成\\n");
        // 右转，角度: 72
        printf("机器人右转：角度=72度\\n");
        printf("TURN_RIGHT:72\\n");
        printf("右转动作完成\\n");
        printf("第 %d 次循环结束\\n", i + 1);
    }
    printf("重复执行完成\\n");
    printf("GPP_SAY:1:五边形绘制完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            document.getElementById('codeArea').textContent = currentCode;
            document.getElementById('executeBtn').disabled = false;
        }

        // 执行C代码
        async function executeCode() {
            if (!picocjs || !currentCode) {
                alert('请先加载PicoC并选择测试');
                return;
            }

            const outputDiv = document.getElementById('output');
            outputDiv.textContent = '开始执行...\n';

            try {
                await picocjs.runC(currentCode, (output) => {
                    const processedOutput = output.replace(/\\n/g, '\n');
                    outputDiv.textContent += processedOutput;
                    outputDiv.scrollTop = outputDiv.scrollHeight;
                });

                outputDiv.textContent += '\n=== 执行完成 ===\n';
                
            } catch (error) {
                outputDiv.textContent += `\n执行错误: ${error.message}\n`;
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
