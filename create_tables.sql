create table users
(
    user_id          int auto_increment comment '用户id'
        primary key,
    username         varchar(50)                         not null comment '用户名',
    password         varchar(255)                        not null comment '密码',
    created_at       timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    email            varchar(100)                        null comment '邮箱',
    user_phonenumber varchar(20)                         null comment '手机号',
    updated_at       datetime(6)                         null,
    constraint username
        unique (username)
);

create table projects
(
    project_id         int auto_increment comment '项目id'
        primary key,
    user_id            int                                 not null comment '用户id',
    project_name       varchar(50)                         not null comment '项目名称',
    project_blocks     longblob                            null,
    project_created_at timestamp default CURRENT_TIMESTAMP null comment '项目创建时间',
    constraint projects_ibfk_1
        foreign key (user_id) references users (user_id)
);

create index user_id
    on projects (user_id);

create table versions
(
    version_id         int auto_increment comment '版本id'
        primary key,
    project_id         int         not null comment '项目id',
    version_number     varchar(10) not null comment '版本号，格式：从v1.0开始，每次修改版本号+0.1',
    user_id            int         not null comment '用户id',
    version_created_at varchar(50) not null comment '版本创建时间',
    constraint versions_ibfk_1
        foreign key (project_id) references projects (project_id),
    constraint versions_ibfk_2
        foreign key (user_id) references users (user_id)
);

create index project_id
    on versions (project_id);

create index user_id
    on versions (user_id);

