package com.zxy.scratchserver.service;

import com.zxy.scratchserver.dto.VersionResponse;

import java.util.List;

/**
 * 版本服务接口
 */
public interface VersionService {

    /**
     * 获取项目的版本列表
     * @param projectId 项目ID
     * @return 版本列表
     */
    List<VersionResponse> getProjectVersions(Long projectId);

    /**
     * 获取项目的最新版本
     * @param projectId 项目ID
     * @return 最新版本
     */
    VersionResponse getLatestVersion(Long projectId);

    /**
     * 创建新版本
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 新版本
     */
    VersionResponse createNewVersion(Long projectId, Long userId);

    /**
     * 更新项目版本号
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 更新后的版本
     */
    VersionResponse updateVersionNumber(Long projectId, Long userId);
}
