import React from 'react';
import PropTypes from 'prop-types';
import {defineMessages, FormattedMessage, injectIntl, intlShape} from 'react-intl';
import classNames from 'classnames';

import styles from './login-form.css'; // 复用登录表单的样式
import Button from '../button/button.jsx';

const messages = defineMessages({
    username: {
        defaultMessage: '用户名',
        description: '注册表单中的用户名标签',
        id: 'gui.register.username'
    },
    password: {
        defaultMessage: '密码',
        description: '注册表单中的密码标签',
        id: 'gui.register.password'
    },
    confirmPassword: {
        defaultMessage: '确认密码',
        description: '注册表单中的确认密码标签',
        id: 'gui.register.confirmPassword'
    },
    register: {
        defaultMessage: '注册',
        description: '注册按钮文本',
        id: 'gui.register.register'
    },
    goToLogin: {
        defaultMessage: '已有账号？去登录',
        description: '跳转到登录页面的链接文本',
        id: 'gui.register.goToLogin'
    },
    usernameRequired: {
        defaultMessage: '请输入用户名',
        description: '用户名必填提示',
        id: 'gui.register.usernameRequired'
    },
    usernameTooShort: {
        defaultMessage: '用户名长度不能少于3个字符',
        description: '用户名长度提示',
        id: 'gui.register.usernameTooShort'
    },
    passwordRequired: {
        defaultMessage: '请输入密码',
        description: '密码必填提示',
        id: 'gui.register.passwordRequired'
    },
    passwordTooShort: {
        defaultMessage: '密码长度不能少于6个字符',
        description: '密码长度提示',
        id: 'gui.register.passwordTooShort'
    },
    confirmPasswordRequired: {
        defaultMessage: '请确认密码',
        description: '确认密码必填提示',
        id: 'gui.register.confirmPasswordRequired'
    },
    passwordMismatch: {
        defaultMessage: '两次输入的密码不一致',
        description: '密码不匹配错误信息',
        id: 'gui.register.passwordMismatch'
    }
});

class RegisterForm extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            username: '',
            password: '',
            confirmPassword: '',
            formErrors: {
                username: '',
                password: '',
                confirmPassword: ''
            },
            formValid: false,
            formTouched: false
        };
        this.handleUsernameChange = this.handleUsernameChange.bind(this);
        this.handlePasswordChange = this.handlePasswordChange.bind(this);
        this.handleConfirmPasswordChange = this.handleConfirmPasswordChange.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.validateField = this.validateField.bind(this);
        this.validateForm = this.validateForm.bind(this);
    }

    validateField (fieldName, value) {
        const {intl} = this.props;
        const {password} = this.state;
        const formErrors = this.state.formErrors;

        switch (fieldName) {
        case 'username':
            if (value.trim() === '') {
                formErrors.username = intl.formatMessage(messages.usernameRequired);
            } else if (value.length < 3) {
                formErrors.username = intl.formatMessage(messages.usernameTooShort);
            } else {
                formErrors.username = '';
            }
            break;
        case 'password':
            if (value === '') {
                formErrors.password = intl.formatMessage(messages.passwordRequired);
            } else if (value.length < 6) {
                formErrors.password = intl.formatMessage(messages.passwordTooShort);
            } else {
                formErrors.password = '';
            }
            // 当密码改变时，同时验证确认密码
            if (this.state.confirmPassword) {
                if (this.state.confirmPassword !== value) {
                    formErrors.confirmPassword = intl.formatMessage(messages.passwordMismatch);
                } else {
                    formErrors.confirmPassword = '';
                }
            }
            break;
        case 'confirmPassword':
            if (value === '') {
                formErrors.confirmPassword = intl.formatMessage(messages.confirmPasswordRequired);
            } else if (value !== password) {
                formErrors.confirmPassword = intl.formatMessage(messages.passwordMismatch);
            } else {
                formErrors.confirmPassword = '';
            }
            break;
        default:
            break;
        }

        this.setState({formErrors, formTouched: true}, this.validateForm);
    }

    validateForm () {
        const {username, password, confirmPassword, formErrors} = this.state;
        const isValid = username.trim() !== '' &&
                      username.length >= 3 &&
                      password !== '' &&
                      password.length >= 6 &&
                      confirmPassword !== '' &&
                      password === confirmPassword &&
                      formErrors.username === '' &&
                      formErrors.password === '' &&
                      formErrors.confirmPassword === '';

        this.setState({formValid: isValid});
    }

    handleUsernameChange (e) {
        const name = e.target.name || 'username';
        const value = e.target.value;
        this.setState({username: value, formTouched: true},
            () => this.validateField(name, value));
    }

    handlePasswordChange (e) {
        const name = e.target.name || 'password';
        const value = e.target.value;
        this.setState({password: value, formTouched: true},
            () => this.validateField(name, value));
    }

    handleConfirmPasswordChange (e) {
        const name = e.target.name || 'confirmPassword';
        const value = e.target.value;
        this.setState({confirmPassword: value, formTouched: true},
            () => this.validateField(name, value));
    }

    handleSubmit (e) {
        e.preventDefault();
        console.log('注册表单提交事件触发');
        const {username, password, confirmPassword} = this.state;
        
        // 再次验证所有字段
        this.validateField('username', username);
        this.validateField('password', password);
        this.validateField('confirmPassword', confirmPassword);

        // 标记表单已触发提交
        this.setState({formTouched: true});
        
        // 如果表单有效，则提交
        if (this.state.formValid) {
            console.log('注册表单验证通过，准备调用onRegister', username, password);
            try {
                this.props.onRegister(username, password);
                console.log('onRegister调用成功');
            } catch (error) {
                console.error('注册调用失败:', error);
                alert(`注册失败: ${error.message || '未知错误'}`);
            }
        } else {
            // 显示错误信息
            console.log('注册表单验证失败');
            const errorMessages = [];
            
            const {formErrors} = this.state;
            if (formErrors.username) errorMessages.push(formErrors.username);
            if (formErrors.password) errorMessages.push(formErrors.password);
            if (formErrors.confirmPassword) errorMessages.push(formErrors.confirmPassword);
            
            if (errorMessages.length > 0) {
                alert(errorMessages.join('\n'));
            } else {
                alert('请正确填写所有必填字段');
            }
        }
    }

    render () {
        const {
            intl,
            error,
            onSwitchToLogin
        } = this.props;

        const {formErrors, formTouched} = this.state;

        return (
            <form 
                className={styles.loginForm}
                onSubmit={this.handleSubmit}
            >
                <h2 className={styles.title}>
                    <FormattedMessage {...messages.register} />
                </h2>
                
                {error && (
                    <div className={styles.errorMessage}>
                        {error}
                    </div>
                )}
                
                <div className={styles.formGroup}>
                    <label className={styles.label}>
                        <FormattedMessage {...messages.username} />
                    </label>
                    <input
                        className={classNames(styles.input, {
                            [styles.inputError]: formTouched && formErrors.username
                        })}
                        type="text"
                        name="username"
                        value={this.state.username}
                        onChange={this.handleUsernameChange}
                        onBlur={() => this.validateField('username', this.state.username)}
                        placeholder={intl.formatMessage(messages.username)}
                    />
                    {formTouched && formErrors.username && (
                        <div className={styles.fieldError}>
                            {formErrors.username}
                        </div>
                    )}
                </div>
                
                <div className={styles.formGroup}>
                    <label className={styles.label}>
                        <FormattedMessage {...messages.password} />
                    </label>
                    <input
                        className={classNames(styles.input, {
                            [styles.inputError]: formTouched && formErrors.password
                        })}
                        type="password"
                        name="password"
                        value={this.state.password}
                        onChange={this.handlePasswordChange}
                        onBlur={() => this.validateField('password', this.state.password)}
                        placeholder={intl.formatMessage(messages.password)}
                    />
                    {formTouched && formErrors.password && (
                        <div className={styles.fieldError}>
                            {formErrors.password}
                        </div>
                    )}
                </div>
                
                <div className={styles.formGroup}>
                    <label className={styles.label}>
                        <FormattedMessage {...messages.confirmPassword} />
                    </label>
                    <input
                        className={classNames(styles.input, {
                            [styles.inputError]: formTouched && formErrors.confirmPassword
                        })}
                        type="password"
                        name="confirmPassword"
                        value={this.state.confirmPassword}
                        onChange={this.handleConfirmPasswordChange}
                        onBlur={() => this.validateField('confirmPassword', this.state.confirmPassword)}
                        placeholder={intl.formatMessage(messages.confirmPassword)}
                    />
                    {formTouched && formErrors.confirmPassword && (
                        <div className={styles.fieldError}>
                            {formErrors.confirmPassword}
                        </div>
                    )}
                </div>
                
                <div className={styles.buttonRow}>
                    <Button
                        className={styles.loginButton}
                        type="submit"
                        onClick={(e) => {
                            console.log('注册按钮被点击');
                            // 点击按钮时不做特殊处理，让表单的onSubmit事件处理程序处理
                        }}
                    >
                        <FormattedMessage {...messages.register} />
                    </Button>
                </div>
                
                <div className={styles.switchForm}>
                    <a
                        href="#"
                        onClick={e => {
                            e.preventDefault();
                            onSwitchToLogin();
                        }}
                    >
                        <FormattedMessage {...messages.goToLogin} />
                    </a>
                </div>
            </form>
        );
    }
}

RegisterForm.propTypes = {
    intl: intlShape.isRequired,
    error: PropTypes.string,
    onRegister: PropTypes.func.isRequired,
    onSwitchToLogin: PropTypes.func.isRequired
};

export default injectIntl(RegisterForm);
