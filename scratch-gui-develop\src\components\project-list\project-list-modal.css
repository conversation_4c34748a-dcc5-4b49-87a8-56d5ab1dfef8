@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.modalContent {
    width: 750px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(76, 151, 255, 0.2);
    animation: fadeIn 0.3s ease-out;
    max-height: 85vh; /* 设置最大高度为视口高度的85% */
    display: flex;
    flex-direction: column;
    margin: 7.5vh auto !important; /* 调整上下边距，确保居中 */
}

.container {
    padding: 1.5rem;
    background-color: #fff;
    border-radius: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出 */
}

.header {
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.header::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #4C97FF, #3373CC);
    border-radius: 3px;
}

.header h2 {
    margin: 0;
    color: #4C97FF;
    font-size: 1.5rem;
    font-weight: 600;
}

.body {
    padding: 0 1rem;
    flex: 1;
    overflow-y: auto; /* 添加垂直滚动条 */
    max-height: calc(85vh - 8rem); /* 调整最大高度，为头部和边距留出空间 */
    padding-bottom: 1rem; /* 确保底部有足够的空间 */
    min-height: 200px; /* 设置最小高度 */
}

.spinnerWrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 250px;
}

.emptyMessage {
    text-align: center;
    padding: 3rem;
    color: #575e75;
    font-style: italic;
    background-color: rgba(76, 151, 255, 0.05);
    border-radius: 8px;
    border: 1px dashed rgba(76, 151, 255, 0.3);
}

.projectList {
    /* 确保项目列表可以滚动 */
    overflow-y: auto;
    max-height: 100%;
}

.projectTable {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.projectTable th,
.projectTable td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(76, 151, 255, 0.1);
}

.projectTable th {
    background: linear-gradient(to right, #4C97FF, #3373CC);
    font-weight: 600;
    color: white;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.headerCell {
    padding: 1rem !important;
}

.headerContent {
    display: flex;
    align-items: center;
}

.headerIcon {
    margin-right: 8px;
    font-size: 1rem;
}

.projectTable tr:last-child td {
    border-bottom: none;
}

.projectRow {
    transition: all 0.2s ease;
    animation: slideIn 0.3s ease-out;
    animation-fill-mode: both;
}

.projectRow:nth-child(1) { animation-delay: 0.05s; }
.projectRow:nth-child(2) { animation-delay: 0.1s; }
.projectRow:nth-child(3) { animation-delay: 0.15s; }
.projectRow:nth-child(4) { animation-delay: 0.2s; }
.projectRow:nth-child(5) { animation-delay: 0.25s; }
.projectRow:nth-child(6) { animation-delay: 0.3s; }
.projectRow:nth-child(7) { animation-delay: 0.35s; }
.projectRow:nth-child(8) { animation-delay: 0.4s; }
.projectRow:nth-child(9) { animation-delay: 0.45s; }
.projectRow:nth-child(10) { animation-delay: 0.5s; }

.projectRow:hover {
    background-color: rgba(76, 151, 255, 0.05);
}

.projectName {
    font-weight: 500;
    color: #4C97FF;
}

.projectDate {
    color: #666;
    font-size: 0.9rem;
}

.projectVersion {
    color: #4C97FF;
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
}

.projectActions {
    text-align: center;
}

.actionButtonGroup {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.actionButton {
    background-color: #4C97FF;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.actionButton::before {
    content: "📂";
    margin-right: 5px;
}

.deleteButton {
    background-color: #FF6680;
}

.deleteButton::before {
    content: "🗑️";
    margin-right: 5px;
}

.actionButton:hover {
    background-color: #3373CC;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.deleteButton:hover {
    background-color: #E64A6A;
}

/* 自定义滚动条样式 */
.body::-webkit-scrollbar {
    width: 12px;
}

.body::-webkit-scrollbar-track {
    background: rgba(76, 151, 255, 0.1);
    border-radius: 6px;
    margin: 4px 0;
}

.body::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #4C97FF, #3373CC);
    border-radius: 6px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
}

.body::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #3373CC, #2A5AA0);
    border: 2px solid rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

/* Firefox滚动条样式 */
.body {
    scrollbar-width: thin;
    scrollbar-color: #4C97FF rgba(76, 151, 255, 0.1);
}

/* 为项目列表容器也添加滚动条样式 */
.projectList::-webkit-scrollbar {
    width: 12px;
}

.projectList::-webkit-scrollbar-track {
    background: rgba(76, 151, 255, 0.1);
    border-radius: 6px;
    margin: 4px 0;
}

.projectList::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #4C97FF, #3373CC);
    border-radius: 6px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s ease;
}

.projectList::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #3373CC, #2A5AA0);
    border: 2px solid rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

/* 确保模态框底部按钮完全显示 */
.ReactModal__Content {
    overflow: visible !important;
}

/* 确保模态框内容可以滚动 */
.ReactModal__Overlay {
    overflow-y: auto;
    padding-bottom: 20px;
}
