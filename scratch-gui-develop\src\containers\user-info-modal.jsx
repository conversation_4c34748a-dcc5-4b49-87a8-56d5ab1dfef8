import {connect} from 'react-redux';
import React from 'react';
import PropTypes from 'prop-types';

import {
    closeUserInfoModal
} from '../reducers/modals';
import {
    getCurrentUser,
    updateUserInfo,
    updatePassword
} from '../lib/user-api';

import UserInfoModalComponent from '../components/user-info/user-info-modal.jsx';

class UserInfoModal extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            userId: null,
            username: '',
            email: '',
            phoneNumber: '',
            createdAt: '',
            loading: true,
            error: null
        };
        this.handleUpdateUserInfo = this.handleUpdateUserInfo.bind(this);
        this.handleUpdatePassword = this.handleUpdatePassword.bind(this);
        this.handleClose = this.handleClose.bind(this);
    }

    componentDidMount () {
        // 当组件挂载时，获取用户信息
        this.fetchUserInfo();
    }

    componentDidUpdate (prevProps) {
        // 当模态框打开时，获取用户信息
        if (!prevProps.isOpen && this.props.isOpen) {
            this.fetchUserInfo();
        }
    }

    fetchUserInfo () {
        this.setState({loading: true, error: null});

        getCurrentUser()
            .then(response => {
                const userData = response.data || {};
                console.log('获取到的用户信息:', userData);
                this.setState({
                    userId: userData.id,
                    username: userData.username || '',
                    email: userData.email || '',
                    phoneNumber: userData.phoneNumber || '',
                    createdAt: userData.createdAt || '',
                    loading: false
                });
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
                this.setState({
                    loading: false,
                    error: error.message || '获取用户信息失败'
                });
            });
    }

    handleUpdateUserInfo (userInfo) {
        this.setState({loading: true, error: null});

        updateUserInfo(userInfo)
            .then(response => {
                const userData = response.data || {};
                this.setState({
                    username: userData.username || userInfo.username,
                    email: userData.email || userInfo.email,
                    phoneNumber: userData.phoneNumber || userInfo.phoneNumber,
                    loading: false
                });
                alert('用户信息更新成功');
            })
            .catch(error => {
                console.error('更新用户信息失败:', error);
                this.setState({
                    loading: false,
                    error: error.message || '更新用户信息失败'
                });
                alert('更新用户信息失败: ' + (error.message || '未知错误'));
            });
    }

    handleUpdatePassword (oldPassword, newPassword) {
        this.setState({loading: true, error: null});

        return updatePassword(oldPassword, newPassword)
            .then(response => {
                this.setState({loading: false});
                return response;
            })
            .catch(error => {
                console.error('修改密码失败:', error);
                this.setState({loading: false});
                throw error;
            });
    }

    handleClose () {
        this.props.onClose();
    }

    render () {
        const {
            isOpen
        } = this.props;

        const {
            userId,
            username,
            email,
            phoneNumber,
            createdAt,
            loading
        } = this.state;

        return (
            <UserInfoModalComponent
                isOpen={isOpen}
                onClose={this.handleClose}
                onUpdateUserInfo={this.handleUpdateUserInfo}
                onUpdatePassword={this.handleUpdatePassword}
                userId={userId}
                username={username}
                email={email}
                phoneNumber={phoneNumber}
                createdAt={createdAt}
                loading={loading}
            />
        );
    }
}

UserInfoModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired
};

const mapStateToProps = state => ({
    isOpen: state.scratchGui.modals.userInfoModal
});

const mapDispatchToProps = dispatch => ({
    onClose: () => dispatch(closeUserInfoModal())
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(UserInfoModal);
