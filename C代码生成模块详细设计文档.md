# C代码生成模块详细设计文档

## 1. 模块概述

C代码生成模块是机器人仿真运行系统的核心组件之一，负责将Scratch图形化积木块转换为标准C语言代码。该模块通过深度优先遍历积木块树结构，将可视化编程转换为文本代码，为后续的C代码执行模块提供输入。

## 2. 功能描述

### 2.1 核心功能
- **积木块解析**：解析Scratch工作区中的积木块结构和连接关系
- **代码映射**：将不同类型的积木块映射为对应的C语言语句
- **代码生成**：生成完整的、可编译执行的C语言程序
- **语法优化**：对生成的代码进行格式化和注释增强

### 2.2 支持的积木块类型
- **事件类积木**：绿旗点击事件、按键事件等
- **控制类积木**：循环、条件判断、等待等
- **运动类积木**：前进、后退、转向等机器人控制
- **外观类积木**：说话、思考、显示文本等
- **传感器类积木**：K1传感器系列（光线、距离、声音、循迹等）
- **运算类积木**：数学运算、逻辑运算、比较运算
- **变量类积木**：变量定义、赋值、读取

## 3. 业务流程

### 3.1 主要流程步骤
```
用户编辑积木程序 → 触发代码生成 → 解析积木块结构 → 
处理变量声明 → 遍历积木块树 → 生成C代码片段 → 
组装完整程序 → 格式化输出 → 显示在C代码面板
```

### 3.2 详细业务流程

#### 步骤1：积木块结构解析
- 获取当前编辑目标的积木块集合
- 识别顶层积木块（无父块的积木）
- 构建积木块的树形结构关系

#### 步骤2：变量处理
- 扫描所有变量定义
- 生成C语言变量声明代码
- 处理变量的初始值设置

#### 步骤3：代码生成
- 深度优先遍历积木块树
- 根据积木块类型调用相应的代码生成函数
- 处理积木块的输入参数和连接关系

#### 步骤4：代码组装
- 添加C语言头文件声明
- 插入函数声明和全局变量
- 生成main函数框架
- 组装用户自定义函数

## 4. 关键函数调用

### 4.1 主要函数架构

#### generateCCodeFromBlocks(target, vm)
**功能**：主入口函数，协调整个代码生成过程
**参数**：
- target: 当前编辑目标对象
- vm: Scratch虚拟机实例
**返回值**：完整的C语言程序字符串

#### processBlock(blockId, blocks, indentLevel)
**功能**：递归处理单个积木块，生成对应的C代码
**参数**：
- blockId: 积木块唯一标识符
- blocks: 积木块集合对象
- indentLevel: 代码缩进级别
**返回值**：该积木块对应的C代码字符串

#### getInputValue(block, blocks)
**功能**：获取积木块输入参数的值
**参数**：
- block: 输入积木块对象
- blocks: 积木块集合
**返回值**：参数值的字符串表示

#### getInputBlock(block, blocks, inputName)
**功能**：获取指定输入端口连接的积木块
**参数**：
- block: 当前积木块
- blocks: 积木块集合
- inputName: 输入端口名称
**返回值**：连接的积木块对象

### 4.2 积木块映射函数

#### 事件类积木处理
```javascript
case 'event_whenflagclicked':
    code += `${indent}// 程序开始执行\n`;
    break;
```

#### 控制类积木处理
```javascript
case 'control_repeat':
    const times = getInputBlock(block, blocks, 'TIMES');
    let timesValue = times ? getInputValue(times, blocks) : '10';
    code += `${indent}for (int i = 0; i < ${timesValue}; i++) {\n`;
    // 处理循环体
    break;
```

#### 运动类积木处理
```javascript
case 'motion_movesteps':
    const steps = getInputBlock(block, blocks, 'STEPS');
    const stepsValue = steps ? getInputValue(steps, blocks) : '10';
    code += `${indent}// 前进 ${stepsValue} 步\n`;
    code += `${indent}forward(${stepsValue});\n`;
    break;
```

## 5. 关键代码实现

### 5.1 核心生成逻辑
```javascript
// 主代码生成函数
const generateCCodeFromBlocks = (target, vm) => {
    if (!target || !vm) {
        return '// 没有积木块可以转换';
    }

    let cCode = '#include <stdio.h>\n#include <stdlib.h>\n#include <unistd.h>\n\n';
    
    // 处理变量声明
    const variables = [];
    if (target.variables) {
        Object.keys(target.variables).forEach(id => {
            const variable = target.variables[id];
            if (variable) {
                variables.push({
                    id: id,
                    name: variable.name,
                    value: variable.value
                });
            }
        });
    }

    // 添加变量声明到代码
    variables.forEach(variable => {
        if (typeof variable.value === 'number') {
            cCode += `int ${variable.name} = ${variable.value};\n`;
        } else {
            cCode += `char ${variable.name}[] = "${variable.value}";\n`;
        }
    });

    // 添加函数声明
    cCode += 'void forward(int steps);\n';
    cCode += 'void turnleft(int angle);\n';
    cCode += 'void turnright(int angle);\n';
    // ... 其他函数声明

    // 获取积木块脚本
    const blocks = target.blocks;
    let scripts = [];
    
    if (blocks._scripts && blocks._scripts.length > 0) {
        scripts = blocks._scripts;
    } else {
        // 查找顶层积木块
        const topBlocks = [];
        Object.keys(blocks._blocks || {}).forEach(id => {
            const block = blocks._blocks[id];
            if (block && !block.parent) {
                topBlocks.push(id);
            }
        });
        scripts = topBlocks;
    }

    // 生成main函数
    cCode += 'int main() {\n';
    
    // 处理每个脚本
    let mainCode = '';
    for (let i = 0; i < scripts.length; i++) {
        const topBlockId = scripts[i];
        mainCode += processBlock(topBlockId, blocks, 0);
    }

    // 添加缩进
    if (mainCode.trim()) {
        const indentedMainCode = mainCode.split('\n').map(line =>
            line.trim() ? '    ' + line : line
        ).join('\n');
        cCode += indentedMainCode;
    }

    cCode += '    return 0;\n';
    cCode += '}\n';

    return cCode;
};
```

### 5.2 积木块处理核心逻辑
```javascript
const processBlock = (blockId, blocks, indentLevel) => {
    if (!blockId || !blocks) return '';

    // 获取积木块对象
    let block = null;
    if (blocks.getBlock) {
        block = blocks.getBlock(blockId);
    } else if (blocks._blocks && blocks._blocks[blockId]) {
        block = blocks._blocks[blockId];
    }

    if (!block) return '';

    const indent = '    '.repeat(indentLevel);
    let code = '';

    // 根据积木块类型生成代码
    switch (block.opcode) {
        case 'control_repeat':
            const times = getInputBlock(block, blocks, 'TIMES');
            let timesValue = times ? getInputValue(times, blocks) : '10';
            code += `${indent}for (int i = 0; i < ${timesValue}; i++) {\n`;
            
            const repeatBlockId = getInputBlock(block, blocks, 'SUBSTACK');
            if (repeatBlockId) {
                code += processBlock(repeatBlockId, blocks, indentLevel + 1);
            }
            code += `${indent}}\n`;
            break;

        case 'motion_movesteps':
            const steps = getInputBlock(block, blocks, 'STEPS');
            const stepsValue = steps ? getInputValue(steps, blocks) : '10';
            code += `${indent}forward(${stepsValue});\n`;
            break;

        case 'looks_say':
            const message = getInputBlock(block, blocks, 'MESSAGE');
            const messageValue = message ? getInputValue(message, blocks) : '"Hello"';
            code += `${indent}printf("说: %s\\n", ${messageValue});\n`;
            break;

        // K1传感器积木
        case 'k1_lightsensor':
            code += `${indent}printf("光线传感器值: %d\\n", lightsensor());\n`;
            break;

        case 'k1_distsensor':
            code += `${indent}printf("距离传感器值: %d\\n", distsensor());\n`;
            break;

        default:
            code += `${indent}printf("执行 %s 操作\\n", "${block.opcode}");\n`;
    }

    // 处理下一个连接的积木块
    const nextBlockId = block.next;
    if (nextBlockId) {
        code += processBlock(nextBlockId, blocks, indentLevel);
    }

    return code;
};
```

## 6. 技术特点

### 6.1 设计优势
- **模块化设计**：每种积木块类型都有独立的处理函数
- **递归处理**：支持复杂的嵌套积木块结构
- **类型安全**：对输入参数进行类型检查和默认值处理
- **代码优化**：生成的C代码具有良好的可读性和格式

### 6.2 扩展性
- **新积木块支持**：通过添加新的case分支轻松支持新积木块
- **自定义函数**：支持用户自定义积木块的代码生成
- **多语言支持**：架构设计支持扩展到其他编程语言

### 6.3 错误处理
- **异常捕获**：对代码生成过程中的异常进行捕获和处理
- **默认值机制**：为缺失的参数提供合理的默认值
- **调试信息**：提供详细的调试日志便于问题排查

## 7. 与后端系统集成

### 7.1 项目保存集成
C代码生成模块与后端项目管理系统紧密集成，生成的C代码作为项目的一部分被保存到数据库中。

#### 相关后端接口
- **ProjectController.saveProject()** - 保存包含C代码的项目
- **ProjectController.getProjectDetail()** - 获取项目详情，包括生成的C代码
- **ProjectService.saveProject()** - 项目保存业务逻辑

#### 数据库存储
```sql
-- projects表结构
CREATE TABLE projects (
    project_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    project_name VARCHAR(50) NOT NULL,
    project_blocks LONGBLOB, -- 存储积木块数据，包含C代码生成信息
    project_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7.2 用户权限集成
- 通过JWT认证确保只有登录用户可以生成和保存C代码
- 用户只能访问自己的项目和生成的C代码
- 支持项目版本管理，每次修改都会更新版本号

### 7.3 API调用示例
```javascript
// 前端保存项目时包含C代码
const saveProjectWithCCode = async (projectData, cCode) => {
    const response = await fetch('/api/project/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            projectName: projectData.name,
            projectBlocks: projectData.blocks,
            generatedCCode: cCode // C代码作为项目数据的一部分
        })
    });
    return response.json();
};
```

## 8. 系统架构图

### 8.1 模块关系图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scratch GUI   │    │  C代码生成模块   │    │  C代码执行模块   │
│   (积木编辑器)   │───▶│ (c-code-generator)│───▶│   (c-code-panel) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scratch VM    │    │   后端API接口    │    │   picoc-js      │
│   (虚拟机)      │    │ (ProjectController)│    │   (解释器)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 8.2 数据流图
```
用户编辑积木 → 积木块数据 → C代码生成器 → C代码文本 →
项目保存API → 数据库存储 → 项目加载API → C代码显示 →
C代码执行 → 精灵控制
```

## 9. 测试和验证

### 9.1 单元测试
- **积木块解析测试**：验证各种积木块类型的正确解析
- **代码生成测试**：验证生成的C代码语法正确性
- **参数处理测试**：验证输入参数的正确处理和默认值

### 9.2 集成测试
- **端到端测试**：从积木编辑到C代码生成的完整流程测试
- **数据库集成测试**：验证C代码的正确保存和加载
- **用户权限测试**：验证用户权限控制的正确性

### 9.3 性能测试
- **大型项目测试**：测试包含大量积木块的项目的代码生成性能
- **并发测试**：测试多用户同时进行代码生成的系统性能
- **内存使用测试**：监控代码生成过程的内存使用情况
