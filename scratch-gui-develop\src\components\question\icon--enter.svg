<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 51.2 (57519) - http://www.bohemiancoding.com/sketch -->
    <title>General/Check</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M7.86144059,15.4028776 C7.43526859,15.4028776 7.00909658,15.2397336 6.68447338,14.9151104 L3.48818336,11.7188204 C2.83727221,11.0679093 2.83727221,10.0157971 3.48818336,9.364886 C4.1390945,8.71397485 5.19120664,8.71397485 5.84211778,9.364886 L7.86144059,11.3842088 L14.1591308,5.08818336 C14.8083772,4.43727221 15.862154,4.43727221 16.5130652,5.08818336 C17.1623116,5.73742977 17.1623116,6.79120664 16.5130652,7.44211778 L9.0384078,14.9151104 C8.7137846,15.2397336 8.28761259,15.4028776 7.86144059,15.4028776" id="path-1"></path>
    </defs>
    <g id="General/Check" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <mask id="mask-2" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <use id="Check" fill="#575E75" xlink:href="#path-1"></use>
        <g id="Color/White" mask="url(#mask-2)" fill="#FFFFFF">
            <rect id="Color" x="0" y="0" width="20" height="20"></rect>
        </g>
    </g>
</svg>