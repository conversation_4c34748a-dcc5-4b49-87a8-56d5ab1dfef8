import React from 'react';
import PropTypes from 'prop-types';
import {FormattedMessage} from 'react-intl';
import bindAll from 'lodash.bindall';
import Button from '../button/button.jsx';
import styles from './export-button.css';
import {saveProject} from '../../lib/project-api';

class ExportButton extends React.Component {
    constructor (props) {
        super(props);
        bindAll(this, [
            'handleClick',
            'handleExportSuccess',
            'handleExportError'
        ]);
        this.state = {
            exporting: false,
            error: null,
            success: false
        };
    }

    handleClick () {
        this.setState({exporting: true, error: null, success: false});

        // 使用VM的saveProjectSb3方法导出项目
        this.props.vm.saveProjectSb3()
            .then(content => {
                // 创建Blob对象
                const blob = new Blob([content], {type: 'application/zip'});

                // 获取项目名称
                const projectName = this.props.projectName || '未命名项目';

                // 保存到服务器
                return saveProject(projectName, blob);
            })
            .then(this.handleExportSuccess)
            .catch(this.handleExportError);
    }

    handleExportSuccess (response) {
        console.log('导出成功:', response);

        this.setState({
            exporting: false,
            success: true,
            error: null
        });

        // 如果提供了成功回调，则调用
        if (this.props.onExportSuccess) {
            try {
                this.props.onExportSuccess();
            } catch (callbackError) {
                console.error('成功回调执行失败:', callbackError);
            }
        }
    }

    handleExportError (error) {
        console.error('导出错误:', error);

        this.setState({
            exporting: false,
            success: false,
            error: error && error.message ? error.message : '导出失败'
        });

        // 如果提供了错误回调，则调用
        if (this.props.onExportError) {
            try {
                this.props.onExportError();
            } catch (callbackError) {
                console.error('错误回调执行失败:', callbackError);
            }
        }
    }

    render () {
        return (
            <Button
                className={styles.exportButton}
                onClick={this.handleClick}
                disabled={this.state.exporting}
            >
                {this.state.exporting ? (
                    <FormattedMessage
                        defaultMessage="保存中..."
                        description="保存项目按钮文本 - 保存中"
                        id="gui.exportButton.exporting"
                    />
                ) : (
                    <FormattedMessage
                        defaultMessage="保存项目"
                        description="保存项目按钮文本"
                        id="gui.exportButton.export"
                    />
                )}
            </Button>
        );
    }
}

ExportButton.propTypes = {
    vm: PropTypes.shape({
        saveProjectSb3: PropTypes.func
    }),
    projectName: PropTypes.string,
    onExportSuccess: PropTypes.func,
    onExportError: PropTypes.func
};

export default ExportButton;
