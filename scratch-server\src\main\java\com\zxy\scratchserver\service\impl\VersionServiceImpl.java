package com.zxy.scratchserver.service.impl;

import com.zxy.scratchserver.dto.VersionResponse;
import com.zxy.scratchserver.model.Version;
import com.zxy.scratchserver.repository.VersionRepository;
import com.zxy.scratchserver.service.VersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 版本服务实现类
 */
@Service
public class VersionServiceImpl implements VersionService {

    @Autowired
    private VersionRepository versionRepository;

    @Override
    public List<VersionResponse> getProjectVersions(Long projectId) {
        List<Version> versions = versionRepository.findByProjectId(projectId);
        return versions.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public VersionResponse getLatestVersion(Long projectId) {
        Optional<Version> latestVersion = versionRepository.findFirstByProjectIdOrderByIdDesc(projectId);
        return latestVersion.map(this::convertToDto).orElse(null);
    }

    @Override
    public VersionResponse createNewVersion(Long projectId, Long userId) {
        System.out.println("开始创建新版本，项目ID: " + projectId + ", 用户ID: " + userId);

        if (projectId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {
            // 获取项目的最新版本
            Optional<Version> latestVersionOpt = versionRepository.findFirstByProjectIdOrderByIdDesc(projectId);

            // 检查是否存在最新版本
            if (latestVersionOpt.isPresent()) {
                Version latestVersion = latestVersionOpt.get();
                System.out.println("找到最新版本: " + latestVersion.getVersionNumber());

                // 检查最新版本的创建时间
                LocalDateTime latestVersionTime = LocalDateTime.parse(
                    latestVersion.getCreatedAt(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                );
                LocalDateTime now = LocalDateTime.now();

                // 计算时间差（秒）
                long secondsDiff = ChronoUnit.SECONDS.between(latestVersionTime, now);
                System.out.println("距离上次创建版本的时间（秒）: " + secondsDiff);

                // 如果最新版本是在1秒内创建的，直接返回该版本，不创建新版本
                // 这可以防止短时间内重复创建版本
                if (secondsDiff < 1) {
                    System.out.println("距离上次创建版本时间太短（小于1秒），返回现有版本");
                    return convertToDto(latestVersion);
                }

                // 生成新版本号
                String newVersionNumber = generateNextVersionNumber(latestVersion.getVersionNumber());
                System.out.println("新版本号: " + newVersionNumber);

                // 创建新版本
                Version newVersion = new Version();
                newVersion.setProjectId(projectId);
                newVersion.setUserId(userId);
                newVersion.setVersionNumber(newVersionNumber);
                newVersion.setCreatedAt(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 保存新版本
                System.out.println("保存新版本到数据库");
                Version savedVersion = versionRepository.save(newVersion);
                System.out.println("版本保存成功，ID: " + savedVersion.getId());

                return convertToDto(savedVersion);
            } else {
                // 如果没有版本，从v1.0开始
                System.out.println("未找到现有版本，使用初始版本号v1.0");
                String newVersionNumber = "v1.0";
                System.out.println("新版本号: " + newVersionNumber);

                // 创建新版本
                Version newVersion = new Version();
                newVersion.setProjectId(projectId);
                newVersion.setUserId(userId);
                newVersion.setVersionNumber(newVersionNumber);
                newVersion.setCreatedAt(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 保存新版本
                System.out.println("保存新版本到数据库");
                Version savedVersion = versionRepository.save(newVersion);
                System.out.println("版本保存成功，ID: " + savedVersion.getId());

                VersionResponse response = convertToDto(savedVersion);
                System.out.println("返回版本响应: " + response);
                return response;
            }
        } catch (Exception e) {
            System.err.println("创建新版本失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 生成下一个版本号
     * @param currentVersion 当前版本号
     * @return 下一个版本号
     */
    private String generateNextVersionNumber(String currentVersion) {
        // 移除"v"前缀
        String versionNumber = currentVersion.substring(1);

        try {
            // 解析版本号
            double version = Double.parseDouble(versionNumber);
            // 版本号+0.1
            double nextVersion = version + 0.1;
            // 格式化为一位小数
            return String.format("v%.1f", nextVersion);
        } catch (NumberFormatException e) {
            // 如果解析失败，返回默认版本号
            return "v1.0";
        }
    }

    /**
     * 将实体转换为DTO
     * @param version 版本实体
     * @return 版本DTO
     */
    private VersionResponse convertToDto(Version version) {
        return VersionResponse.builder()
                .versionId(version.getId())
                .projectId(version.getProjectId())
                .userId(version.getUserId())
                .versionNumber(version.getVersionNumber())
                .createdAt(version.getCreatedAt())
                .build();
    }

    @Override
    @Transactional
    public VersionResponse updateVersionNumber(Long projectId, Long userId) {
        System.out.println("开始更新项目版本号，项目ID: " + projectId + ", 用户ID: " + userId);

        if (projectId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {
            // 获取项目的最新版本
            Optional<Version> latestVersionOpt = versionRepository.findFirstByProjectIdOrderByIdDesc(projectId);

            // 检查是否存在最新版本
            if (latestVersionOpt.isPresent()) {
                Version latestVersion = latestVersionOpt.get();
                System.out.println("找到最新版本: " + latestVersion.getVersionNumber());

                // 检查最新版本的创建时间
                LocalDateTime latestVersionTime = LocalDateTime.parse(
                    latestVersion.getCreatedAt(),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                );
                LocalDateTime now = LocalDateTime.now();

                // 计算时间差（秒）
                long secondsDiff = ChronoUnit.SECONDS.between(latestVersionTime, now);
                System.out.println("距离上次更新版本的时间（秒）: " + secondsDiff);

                // 如果最新版本是在1秒内更新的，直接返回该版本，不更新版本号
                // 这可以防止短时间内重复更新版本
                if (secondsDiff < 1) {
                    System.out.println("距离上次更新版本时间太短（小于1秒），返回现有版本");
                    return convertToDto(latestVersion);
                }

                // 生成新版本号
                String newVersionNumber = generateNextVersionNumber(latestVersion.getVersionNumber());
                System.out.println("新版本号: " + newVersionNumber);

                // 更新现有版本的版本号和创建时间
                latestVersion.setVersionNumber(newVersionNumber);
                latestVersion.setCreatedAt(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 保存更新后的版本
                System.out.println("保存更新后的版本到数据库");
                Version savedVersion = versionRepository.save(latestVersion);
                System.out.println("版本更新成功，ID: " + savedVersion.getId());

                return convertToDto(savedVersion);
            } else {
                // 如果没有版本，从v1.0开始创建新版本
                System.out.println("未找到现有版本，使用初始版本号v1.0");
                String newVersionNumber = "v1.0";
                System.out.println("新版本号: " + newVersionNumber);

                // 创建新版本
                Version newVersion = new Version();
                newVersion.setProjectId(projectId);
                newVersion.setUserId(userId);
                newVersion.setVersionNumber(newVersionNumber);
                newVersion.setCreatedAt(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 保存新版本
                System.out.println("保存新版本到数据库");
                Version savedVersion = versionRepository.save(newVersion);
                System.out.println("版本保存成功，ID: " + savedVersion.getId());

                VersionResponse response = convertToDto(savedVersion);
                System.out.println("返回版本响应: " + response);
                return response;
            }
        } catch (Exception e) {
            System.err.println("更新版本号失败: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }
}
