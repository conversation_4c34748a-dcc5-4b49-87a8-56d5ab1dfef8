<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输出格式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            min-height: 200px;
            margin: 10px 0;
            overflow-y: auto;
            max-height: 400px;
            line-height: 1.6;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>输出格式测试</h1>
        <p>测试C代码输出的换行和格式化效果</p>

        <div id="status" class="status info">正在初始化...</div>

        <button class="btn" onclick="testBasicOutput()" id="basicBtn" disabled>基础输出测试</button>
        <button class="btn" onclick="testLoopOutput()" id="loopBtn" disabled>循环输出测试</button>
        <button class="btn" onclick="testRobotOutput()" id="robotBtn" disabled>机器人命令测试</button>
        <button class="btn" onclick="clearOutput()" id="clearBtn">清空输出</button>

        <div id="output" class="output">等待测试...</div>
    </div>

    <script>
        let picocjs = null;
        
        // 初始化
        function init() {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = '正在加载PicoC...';
            
            const script = document.createElement('script');
            script.src = './static/picoc-test/bundle.umd.js';
            
            script.onload = () => {
                if (window.picocjs) {
                    picocjs = window.picocjs;
                    statusDiv.textContent = 'PicoC加载成功！可以开始测试。';
                    statusDiv.className = 'status success';
                    
                    document.getElementById('basicBtn').disabled = false;
                    document.getElementById('loopBtn').disabled = false;
                    document.getElementById('robotBtn').disabled = false;
                } else {
                    statusDiv.textContent = 'PicoC加载失败：未找到picocjs对象';
                    statusDiv.className = 'status error';
                }
            };
            
            script.onerror = () => {
                statusDiv.textContent = 'PicoC加载失败：无法加载脚本文件';
                statusDiv.className = 'status error';
            };
            
            document.head.appendChild(script);
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
        }

        function appendOutput(text) {
            const outputDiv = document.getElementById('output');
            outputDiv.textContent += text;
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }

        async function runTest(code, description) {
            if (!picocjs) {
                appendOutput('PicoC 未加载\n');
                return;
            }

            appendOutput(`\n=== ${description} ===\n`);
            
            try {
                await picocjs.runC(code, (output) => {
                    // 处理输出，确保换行符正确
                    const processedOutput = output.replace(/\\n/g, '\n');
                    appendOutput(processedOutput);
                });
                appendOutput('\n=== 测试完成 ===\n\n');
            } catch (error) {
                appendOutput(`\n错误: ${error.message}\n\n`);
            }
        }

        function testBasicOutput() {
            const code = `#include <stdio.h>

int main() {
    printf("第一行输出\\n");
    printf("第二行输出\\n");
    printf("第三行输出\\n");
    printf("这是一行很长的输出，用来测试换行效果和文本显示是否正常\\n");
    return 0;
}`;
            runTest(code, '基础输出测试');
        }

        function testLoopOutput() {
            const code = `#include <stdio.h>

int main() {
    printf("开始循环测试\\n");
    int i;
    for(i = 0; i < 5; i++) {
        printf("循环第 %d 次开始\\n", i + 1);
        printf("  执行动作 A\\n");
        printf("  执行动作 B\\n");
        printf("循环第 %d 次结束\\n", i + 1);
        printf("\\n");
    }
    printf("循环测试完成\\n");
    return 0;
}`;
            runTest(code, '循环输出测试');
        }

        function testRobotOutput() {
            const code = `#include <stdio.h>

int main() {
    printf("机器人控制程序开始\\n");
    printf("\\n");
    
    printf("步骤1: 前进\\n");
    printf("机器人前进：速度=4，距离=10\\n");
    printf("FORWARD:4:10\\n");
    printf("前进动作完成\\n");
    printf("\\n");
    
    printf("步骤2: 右转\\n");
    printf("机器人右转：角度=90度\\n");
    printf("TURN_RIGHT:90\\n");
    printf("右转动作完成\\n");
    printf("\\n");
    
    printf("步骤3: 说话\\n");
    printf("GPP_SAY:1:机器人测试完成\\n");
    printf("\\n");
    
    printf("机器人控制程序结束\\n");
    return 0;
}`;
            runTest(code, '机器人命令测试');
        }

        // 页面加载时初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
