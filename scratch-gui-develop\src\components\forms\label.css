@import "../../css/units.css";
@import "../../css/colors.css";

.input-group {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
}

.input-group-column {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
}

.input-group-column span {
    margin-bottom: .25rem;
}

.input-label, .input-label-secondary {
    font-size: 0.625rem;
    user-select: none;
    cursor: default;

    white-space: nowrap;
}

[dir="ltr"] .input-label, [dir="ltr"] .input-label-secondary {
    margin-right: .5rem;
}

[dir="rtl"] .input-label, [dir="rtl"] .input-label-secondary {
    margin-left: .5rem;
}

.input-label {
    font-weight: bold;
}
