# 机器人传感器仿真与Scratch积木块交互实现文档

## 1. 系统架构概述

机器人传感器仿真系统是一个完整的嵌入式仿真解决方案，它通过iframe技术嵌入到Scratch GUI的精灵展示区域，实现了与左侧积木块的实时交互。整个系统采用分层架构设计，确保了仿真器与Scratch编程环境的无缝集成。

### 1.1 核心组件架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Scratch GUI 主界面                        │
├─────────────────┬─────────────────┬─────────────────────────┤
│   积木块面板     │   舞台区域       │      精灵控制区域        │
│                │                │                        │
│  ┌─────────────┐│  ┌─────────────┐│  ┌─────────────────────┐│
│  │ K1机器人    ││  │ Scratch VM  ││  │ RobotSensorSimulator││
│  │ 积木块      ││  │ 运行时      ││  │                    ││
│  │ - 前进      ││  │             ││  │ ┌─────────────────┐ ││
│  │ - 左转      ││  │             ││  │ │  iframe         │ ││
│  │ - 右转      ││  │             ││  │ │ robot_sensor.html│ ││
│  └─────────────┘│  └─────────────┘│  │ │                │ ││
│                │                │  │ │ 机器人仿真环境   │ ││
│                │                │  │ │ - 传感器仿真    │ ││
│                │                │  │ │ - 物理仿真      │ ││
│                │                │  │ │ - 可视化显示    │ ││
│                │                │  │ └─────────────────┘ ││
│                │                │  └─────────────────────┘│
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 2. 页面嵌入机制实现

### 2.1 React组件封装
机器人传感器仿真器通过React组件的形式集成到Scratch GUI中：

```javascript
// RobotSensorSimulator.jsx - 仿真器容器组件
const RobotSensorSimulator = props => {
    const { className, onClose, vm, ...componentProps } = props;
    const iframeRef = useRef(null);

    // VM实例暴露机制
    useEffect(() => {
        if (vm) {
            window.vm = vm;  // 将VM实例暴露给iframe
            console.log('VM实例已暴露给robot_sensor.html');
        }

        return () => {
            if (window.vm) {
                delete window.vm;  // 清理全局VM引用
            }
        };
    }, [vm]);

    return (
        <Box className={classNames(styles.robotSensorSimulator, className)}>
            <div className={styles.header}>
                <h2>机器人传感器仿真模拟</h2>
                <button onClick={onClose}>×</button>
            </div>
            <div className={styles.content}>
                <iframe
                    ref={iframeRef}
                    className={styles.iframe}
                    src="/robot_sensor.html"
                    title="机器人传感器仿真模拟"
                />
            </div>
        </Box>
    );
};
```

**关键实现要点**：
- **VM实例传递**：通过`window.vm`将Scratch虚拟机实例暴露给iframe
- **生命周期管理**：使用useEffect确保VM实例的正确传递和清理
- **iframe封装**：将HTML仿真器封装在React组件中，便于集成

### 2.2 StageWrapper集成
仿真器被集成到舞台包装器中，替代原有的精灵控制区域：

```javascript
// stage-wrapper.jsx - 舞台包装器组件
import RobotSensorSimulator from '../robot-sensor-simulator/robot-sensor-simulator.jsx';

const StageWrapper = props => {
    const {
        isRobotSensorVisible,  // 控制仿真器显示状态
        onRobotSensorClose,    // 仿真器关闭回调
        vm,                    // Scratch VM实例
        ...otherProps
    } = props;

    return (
        <Box className={styles.stageWrapper}>
            <StageHeader />
            <Stage />
            
            {/* 条件渲染：仿真器或原有精灵控制区域 */}
            {isRobotSensorVisible ? (
                <RobotSensorSimulator
                    vm={vm}
                    onClose={onRobotSensorClose}
                />
            ) : (
                <OriginalSpriteControls />
            )}
        </Box>
    );
};
```

**集成特点**：
- **条件渲染**：根据状态动态切换显示内容
- **VM传递**：确保仿真器能够访问Scratch虚拟机
- **事件处理**：提供关闭和切换机制

## 3. 机器人传感器仿真实现

### 3.1 多传感器仿真架构
仿真器实现了完整的机器人传感器套件，包括颜色传感器、超声波传感器等：

```javascript
// robot_sensor.html - 传感器仿真核心实现
class RobotSensorSimulator {
    constructor() {
        this.sensors = {
            colorSensors: new Array(5).fill(null),  // 5个颜色传感器
            ultrasonicSensor: null,                 // 超声波距离传感器
            positionSensor: { x: 50, y: 50 },      // 位置传感器
            directionSensor: 0                      // 方向传感器
        };
        
        this.robot = {
            x: 50, y: 50,           // 机器人位置
            angle: 0,               // 机器人角度
            size: 5,                // 机器人尺寸
            moveSpeed: 1,           // 移动速度
            rotationSpeed: 5 * Math.PI / 180  // 旋转速度
        };
    }

    // 颜色传感器仿真实现
    updateColorSensors() {
        const robotCenterX = this.robot.x + this.robot.size / 2;
        const robotCenterY = this.robot.y + this.robot.size / 2;
        
        // 计算5个颜色传感器的位置（机器人头部外侧）
        for (let i = 0; i < 5; i++) {
            const localX = (i - 2);  // 传感器相对位置
            const localY = -(this.robot.size / 2 + 0.5);
            
            // 根据机器人角度旋转传感器位置
            const rotatedX = localX * Math.cos(this.robot.angle) - 
                           localY * Math.sin(this.robot.angle);
            const rotatedY = localX * Math.sin(this.robot.angle) + 
                           localY * Math.cos(this.robot.angle);
            
            const sensorX = robotCenterX + rotatedX;
            const sensorY = robotCenterY + rotatedY;
            const gridX = Math.round(sensorX);
            const gridY = Math.round(sensorY);
            
            // 检测颜色
            const detectedColor = this.detectColor(gridX, gridY);
            this.updateColorSensorDisplay(i, detectedColor);
        }
    }

    // 颜色检测算法
    detectColor(x, y) {
        const mapValue = MAP.getCell(x, y);
        const colorMap = {
            1: 'black',    // 黑色路径线
            3: 'blue',     // 蓝色线条
            4: 'green',    // 绿色线条
            5: 'yellow',   // 黄色线条
            6: 'purple',   // 紫色线条
            7: 'orange'    // 橙色线条
        };
        return colorMap[mapValue] || null;
    }
}
```

### 3.2 超声波传感器仿真
超声波传感器采用扇形探测算法，模拟真实传感器的工作原理：

```javascript
// 超声波距离检测实现
calculateUltrasonicDistance() {
    const robotCenterX = this.robot.x + this.robot.size / 2;
    const robotCenterY = this.robot.y + this.robot.size / 2;
    
    // 超声波传感器位置（机器人头部中心）
    const sensorX = robotCenterX + 0 * Math.cos(this.robot.angle) - 
                   (this.robot.size / 2 + 1) * Math.sin(this.robot.angle);
    const sensorY = robotCenterY + 0 * Math.sin(this.robot.angle) + 
                   (this.robot.size / 2 + 1) * Math.cos(this.robot.angle);
    
    const maxDistance = 20;
    const halfFov = (35 / 2) * Math.PI / 180; // 17.5度扇形范围
    let minDistance = maxDistance;
    
    // 在35度扇形范围内发射60条射线
    const numRays = 60;
    for (let i = 0; i <= numRays; i++) {
        const rayAngle = this.robot.angle - halfFov + (halfFov * 2 * i) / numRays;
        
        // 沿射线方向探测障碍物
        for (let d = 0.2; d <= maxDistance; d += 0.2) {
            const checkX = sensorX + d * Math.sin(rayAngle);
            const checkY = sensorY - d * Math.cos(rayAngle);
            const gridX = Math.round(checkX);
            const gridY = Math.round(checkY);
            
            // 只检测红色正方形障碍物
            if (MAP.getCell(gridX, gridY) === -1) {
                const distToEdge = Math.max(0, 
                    Math.sqrt(Math.pow(sensorX - (gridX + 0.5), 2) + 
                             Math.pow(sensorY - (gridY + 0.5), 2)) - 0.5);
                minDistance = Math.min(minDistance, distToEdge);
                break;
            }
        }
    }
    
    return Math.round(minDistance);
}
```

**仿真特点**：
- **物理准确性**：模拟真实超声波传感器的35度探测范围
- **多射线算法**：使用60条射线提高探测精度
- **障碍物识别**：只对红色障碍物响应，忽略彩色线条

## 4. 与Scratch积木块的交互机制

### 4.1 K1积木块扩展实现
通过扩展Scratch VM的原语系统，实现了K1机器人积木块：

```javascript
// k1-vm-extension.js - K1积木块VM扩展
const implementK1BlockFunctions = () => {
    // 前进积木块实现
    runtime._primitives['k1_forward'] = (args, util) => {
        const speed = toNumber(args.SPEED || 4);
        const distance = toNumber(args.DISTANCE || 10);
        
        // 获取当前精灵
        const target = util.target;
        if (!target) return;
        
        // 计算移动距离和方向
        const direction = target.direction;
        const radians = direction * Math.PI / 180;
        const moveDistance = distance * (speed / 4);
        
        // 更新Scratch精灵位置
        const dx = moveDistance * Math.sin(radians);
        const dy = moveDistance * Math.cos(radians);
        target.setXY(target.x + dx, target.y + dy);
        
        // 同时控制机器人仿真器中的机器人
        controlRobotSimulator('forward', { distance: moveDistance });
    };
    
    // 左转积木块实现
    runtime._primitives['k1_turn_left'] = (args, util) => {
        const degree = toNumber(args.DEGREE || 15);
        const target = util.target;
        if (!target) return;
        
        // 更新Scratch精灵方向
        target.setDirection(target.direction - degree);
        
        // 同时控制机器人仿真器
        controlRobotSimulator('turnLeft', { angle: degree });
    };
    
    // 右转积木块实现
    runtime._primitives['k1_turn_right'] = (args, util) => {
        const degree = toNumber(args.DEGREE || 15);
        const target = util.target;
        if (!target) return;
        
        // 更新Scratch精灵方向
        target.setDirection(target.direction + degree);
        
        // 同时控制机器人仿真器
        controlRobotSimulator('turnRight', { angle: degree });
    };
};
```

### 4.2 跨iframe通信机制
实现了从Scratch主界面到iframe仿真器的控制通信：

```javascript
// 控制机器人仿真器的核心函数
const controlRobotSimulator = (action, params = {}) => {
    try {
        // 查找机器人传感器仿真的iframe
        let robotSensorFrame = document.querySelector('iframe[src*="robot_sensor.html"]');
        if (!robotSensorFrame) {
            robotSensorFrame = document.querySelector('iframe[src="/robot_sensor.html"]');
        }
        if (!robotSensorFrame) {
            robotSensorFrame = document.querySelector('iframe[title*="机器人传感器仿真"]');
        }
        
        if (!robotSensorFrame || !robotSensorFrame.contentWindow) {
            console.log('机器人传感器仿真器未找到');
            return;
        }

        const frameWindow = robotSensorFrame.contentWindow;
        
        // 检查仿真器控制接口
        if (!frameWindow.robotControl) {
            console.log('机器人仿真器控制接口未就绪');
            return;
        }

        // 调用相应的控制函数
        switch (action) {
            case 'forward':
                frameWindow.robotControl.forward();
                break;
            case 'turnLeft':
                frameWindow.robotControl.turnLeft();
                break;
            case 'turnRight':
                frameWindow.robotControl.turnRight();
                break;
        }
    } catch (error) {
        console.error('控制机器人仿真器失败:', error);
    }
};
```

**通信特点**：
- **多重选择器**：使用多种CSS选择器确保能找到iframe
- **接口检查**：验证仿真器控制接口的可用性
- **错误处理**：完善的异常捕获和日志记录

### 4.3 仿真器控制接口暴露
在robot_sensor.html中暴露控制接口供外部调用：

```javascript
// robot_sensor.html - 控制接口暴露
window.robotControl = {
    forward: controlRobotForward,
    back: controlRobotBack,
    turnLeft: controlRobotTurnLeft,
    turnRight: controlRobotTurnRight,
    syncToSprite: syncCanvasToSprite,
    syncToCanvas: syncSpriteToCanvas
};

// 机器人控制函数实现
function controlRobotForward() {
    console.log('控制机器人前进');

    // 同时控制Scratch角色和画布机器人
    if (robotSprite) {
        robotForward(20); // 控制Scratch角色
    }

    // 控制画布机器人
    moveRobot('ArrowUp');
}

function controlRobotTurnLeft() {
    console.log('控制机器人左转');

    if (robotSprite) {
        robotTurnLeft(15); // 控制Scratch角色
    }

    moveRobot('ArrowLeft'); // 控制画布机器人
}
```

## 5. 双向同步机制实现

### 5.1 Scratch角色与仿真器同步
实现了Scratch舞台精灵与仿真器机器人的双向位置同步：

```javascript
// 同步画布机器人位置到Scratch角色
function syncCanvasToSprite() {
    if (!robotSprite) return;

    // 坐标系转换：画布坐标 → Scratch坐标
    const scratchX = (ROBOT.x - GRID_SIZE/2) * 10;
    const scratchY = (GRID_SIZE/2 - ROBOT.y) * 10;

    robotSprite.setXY(scratchX, scratchY);

    // 方向同步：画布弧度 → Scratch度数
    const scratchDirection = (ROBOT.angle * 180 / Math.PI + 90) % 360;
    robotSprite.setDirection(scratchDirection);

    // 触发Scratch重绘
    if (vm && vm.runtime) {
        vm.runtime.requestRedraw();
    }
}

// 同步Scratch角色位置到画布机器人
function syncSpriteToCanvas() {
    if (!robotSprite) return;

    // 坐标系转换：Scratch坐标 → 画布坐标
    const canvasX = robotSprite.x / 10 + GRID_SIZE/2;
    const canvasY = GRID_SIZE/2 - robotSprite.y / 10;

    ROBOT.x = canvasX - ROBOT.size/2;
    ROBOT.y = canvasY - ROBOT.size/2;

    // 方向同步：Scratch度数 → 画布弧度
    ROBOT.angle = (robotSprite.direction - 90) * Math.PI / 180;

    drawGrid(); // 重绘画布
}
```

### 5.2 VM实例获取与角色管理
仿真器通过父窗口获取Scratch VM实例并管理机器人角色：

```javascript
// 初始化函数，从父窗口获取VM实例
function initializeRobotControl() {
    try {
        // 从父窗口获取VM实例
        if (window.parent && window.parent.vm) {
            vm = window.parent.vm;
            console.log('成功获取VM实例');

            // 创建或获取机器人角色
            createRobotSprite();
        } else {
            console.warn('无法获取VM实例，将在1秒后重试');
            setTimeout(initializeRobotControl, 1000);
        }
    } catch (error) {
        console.error('初始化机器人控制失败:', error);
        setTimeout(initializeRobotControl, 1000);
    }
}

// 创建机器人角色
function createRobotSprite() {
    if (!vm) return;

    try {
        // 检查是否已存在机器人角色
        const existingRobot = vm.runtime.targets.find(target =>
            target.sprite && target.sprite.name === '机器人'
        );

        if (existingRobot) {
            robotSprite = existingRobot;
            robotSpriteId = existingRobot.id;
            setupRobotSprite();
            return;
        }

        // 创建新的机器人角色
        const robotSpriteData = {
            name: '机器人',
            isStage: false,
            x: 0, y: 0,
            visible: true,
            size: 100,
            rotationStyle: 'all around',
            direction: 90,
            draggable: true,
            currentCostume: 0,
            blocks: {},
            variables: {},
            costumes: [{
                costumeName: '机器人造型',
                baseLayerID: -1,
                baseLayerMD5: 'cd21514d0531fdffb22204e0ec5ed84a.svg',
                bitmapResolution: 1,
                rotationCenterX: 0,
                rotationCenterY: 0
            }]
        };

        // 添加角色到VM
        vm.addSprite(JSON.stringify(robotSpriteData)).then(() => {
            robotSprite = vm.runtime.targets.find(target =>
                target.sprite && target.sprite.name === '机器人'
            );

            if (robotSprite) {
                robotSpriteId = robotSprite.id;
                setupRobotSprite();
            }
        });

    } catch (error) {
        console.error('创建机器人角色时出错:', error);
    }
}
```

## 6. 实时状态监控系统

### 6.1 传感器状态实时更新
仿真器提供了完整的传感器状态监控界面：

```javascript
// 实时更新机器人状态显示
function checkSensors() {
    // 更新方向指示器
    const arrow = document.getElementById('directionArrow');
    arrow.style.transform = `rotate(${ROBOT.angle}rad)`;

    // 更新角度数值显示
    const angleInDegrees = (ROBOT.angle * 180 / Math.PI) % 360;
    const displayAngle = angleInDegrees < 0 ? angleInDegrees + 360 : angleInDegrees;
    document.getElementById('directionAngle').textContent = `${Math.round(displayAngle)}°`;

    // 更新超声波距离显示
    const ultrasonicDistance = calculateUltrasonicDistance();
    const ultrasonicValueDiv = document.getElementById('ultrasonicValue');
    const noDetectLine = document.getElementById('ultrasonicNoDetect');

    if (ultrasonicDistance >= 20) {
        ultrasonicValueDiv.style.display = 'none';
        noDetectLine.style.display = 'block';
    } else {
        ultrasonicValueDiv.style.display = 'block';
        ultrasonicValueDiv.textContent = ultrasonicDistance;
        noDetectLine.style.display = 'none';
    }

    // 更新机器人坐标显示
    updateRobotCoordinates();
}

// 更新机器人坐标显示
function updateRobotCoordinates() {
    const x = Math.floor(ROBOT.x);
    const y = Math.floor(ROBOT.y);
    document.getElementById('robotCoordinates').textContent = `(${x}, ${y})`;
}
```

### 6.2 可视化界面设计
仿真器采用网格布局展示多维度状态信息：

```html
<!-- 状态显示网格布局 -->
<div class="status-grid">
    <!-- 机器人方向显示 -->
    <div class="status-item">
        <div class="status-title">机器人方向</div>
        <div class="direction-indicator">
            <div class="direction-arrow" id="directionArrow"></div>
        </div>
        <div class="direction-angle" id="directionAngle">0°</div>
    </div>

    <!-- 颜色传感器状态 -->
    <div class="status-item">
        <div class="status-title">颜色传感器</div>
        <div class="color-sensors" id="colorSensors">
            <div class="color-sensor"></div>
            <div class="color-sensor"></div>
            <div class="color-sensor"></div>
            <div class="color-sensor"></div>
            <div class="color-sensor"></div>
        </div>
    </div>

    <!-- 超声波距离显示 -->
    <div class="status-item">
        <div class="status-title">超声波距离</div>
        <div class="ultrasonic-value" id="ultrasonicValue">0</div>
        <div id="ultrasonicNoDetect" style="display:none;"></div>
    </div>

    <!-- 机器人坐标显示 -->
    <div class="status-item">
        <div class="status-title">机器人坐标</div>
        <div class="coordinates" id="robotCoordinates">(50, 50)</div>
    </div>
</div>
```

## 7. 积木块执行流程

### 7.1 完整的执行链路
从用户拖拽积木块到仿真器响应的完整流程：

```
用户操作积木块 → Scratch VM执行 → K1扩展处理 → 跨iframe通信 →
仿真器响应 → 机器人运动 → 传感器更新 → 状态显示 → Scratch同步
```

### 7.2 具体执行步骤
1. **用户交互**：用户在积木块面板拖拽K1机器人积木块
2. **VM执行**：Scratch VM调用对应的原语函数
3. **双重控制**：同时更新Scratch精灵和调用仿真器控制函数
4. **跨域通信**：通过iframe.contentWindow调用仿真器接口
5. **仿真响应**：仿真器执行机器人运动和传感器更新
6. **状态同步**：仿真器状态同步回Scratch精灵
7. **界面更新**：两个环境的界面同时更新

## 8. 技术创新点

### 8.1 无缝集成技术
- **iframe嵌入**：将完整的HTML5仿真器嵌入React组件
- **VM实例共享**：通过window对象共享Scratch VM实例
- **双向同步**：实现仿真器与Scratch环境的实时同步

### 8.2 跨框架通信
- **消息传递**：通过iframe.contentWindow实现跨域通信
- **接口暴露**：在仿真器中暴露标准化控制接口
- **状态管理**：维护两个环境间的状态一致性

### 8.3 教育价值
- **可视化学习**：提供直观的机器人行为反馈
- **实时调试**：支持积木块程序的实时调试
- **渐进式学习**：从图形化编程过渡到机器人控制

## 9. 总结

机器人传感器仿真系统通过创新的技术架构，实现了Scratch图形化编程与机器人仿真的深度融合。系统的核心价值在于：

1. **技术融合**：将Web技术、React组件、iframe通信完美结合
2. **教育导向**：为编程教育提供了直观、互动的学习环境
3. **扩展性强**：架构设计支持更多传感器和功能的扩展
4. **用户体验**：提供了流畅、直观的编程和仿真体验

这种设计不仅解决了传统编程教育中抽象概念难以理解的问题，还为学生提供了一个安全、可控的机器人编程实验环境。
