import React from 'react';
import PropTypes from 'prop-types';
import {FormattedMessage} from 'react-intl';
import classNames from 'classnames';
import Box from '../box/box.jsx';
import Modal from '../../containers/modal.jsx';
import Button from '../button/button.jsx';

import styles from './user-info-modal.css';

class UserInfoModal extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            username: props.username || '',
            email: props.email || '',
            phoneNumber: props.phoneNumber || '',
            isEditing: false,
            showPasswordForm: false,
            oldPassword: '',
            newPassword: '',
            confirmPassword: '',
            passwordError: '',
            isSubmitting: false // 添加提交状态
        };
        this.handleInputChange = this.handleInputChange.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.handleEdit = this.handleEdit.bind(this);
        this.handleCancel = this.handleCancel.bind(this);
        this.togglePasswordForm = this.togglePasswordForm.bind(this);
        this.handlePasswordSubmit = this.handlePasswordSubmit.bind(this);
        this.resetPasswordForm = this.resetPasswordForm.bind(this);
    }

    componentDidUpdate (prevProps) {
        // 当props更新时，更新state
        if (prevProps.username !== this.props.username ||
            prevProps.email !== this.props.email ||
            prevProps.phoneNumber !== this.props.phoneNumber) {
            this.setState({
                username: this.props.username || '',
                email: this.props.email || '',
                phoneNumber: this.props.phoneNumber || ''
            });
        }
    }

    handleInputChange (e) {
        const {name, value} = e.target;
        this.setState({[name]: value});
    }

    handleSubmit (e) {
        e.preventDefault();
        const {username, email, phoneNumber} = this.state;
        this.props.onUpdateUserInfo({username, email, phoneNumber});
        this.setState({isEditing: false});
    }

    handleEdit () {
        this.setState({isEditing: true});
    }

    handleCancel () {
        // 取消编辑，恢复原始数据
        this.setState({
            username: this.props.username || '',
            email: this.props.email || '',
            phoneNumber: this.props.phoneNumber || '',
            isEditing: false
        });
    }

    togglePasswordForm () {
        this.setState(prevState => ({
            showPasswordForm: !prevState.showPasswordForm,
            oldPassword: '',
            newPassword: '',
            confirmPassword: '',
            passwordError: ''
        }));
    }

    resetPasswordForm () {
        this.setState({
            oldPassword: '',
            newPassword: '',
            confirmPassword: '',
            passwordError: '',
            showPasswordForm: false
        });
    }

    handlePasswordSubmit (e) {
        e.preventDefault(); // 阻止表单默认提交行为

        // 清除之前的错误信息
        this.setState({passwordError: ''});

        const {oldPassword, newPassword, confirmPassword} = this.state;

        // 验证输入
        if (!oldPassword || !newPassword || !confirmPassword) {
            this.setState({passwordError: '所有密码字段都不能为空'});
            return;
        }

        if (newPassword.length < 6) {
            this.setState({passwordError: '新密码长度不能少于6个字符'});
            return;
        }

        if (newPassword !== confirmPassword) {
            this.setState({passwordError: '两次输入的新密码不一致'});
            return;
        }

        // 显示加载状态
        this.setState({isSubmitting: true});

        // 调用修改密码API
        this.props.onUpdatePassword(oldPassword, newPassword)
            .then(() => {
                alert('密码修改成功');
                this.resetPasswordForm();
            })
            .catch(error => {
                this.setState({
                    passwordError: error.message || '修改密码失败',
                    isSubmitting: false
                });
            });
    }

    render () {
        const {
            isOpen,
            onClose,
            userId,
            createdAt
        } = this.props;

        const {
            username,
            email,
            phoneNumber,
            isEditing
        } = this.state;

        // 格式化创建时间
        const formattedDate = createdAt ? new Date(createdAt).toLocaleString() : '';

        return (
            <Modal
                className={styles.modalContent}
                contentLabel={<FormattedMessage
                    defaultMessage="个人信息"
                    description="个人信息模态框标题"
                    id="gui.userInfoModal.title"
                />}
                isOpen={isOpen}
                onRequestClose={onClose}
            >
                <Box className={styles.container}>
                    <div className={styles.header}>
                        <h2>
                            <FormattedMessage
                                defaultMessage="个人信息"
                                description="个人信息模态框标题"
                                id="gui.userInfoModal.title"
                            />
                        </h2>
                    </div>
                    <div className={styles.body}>
                        <form onSubmit={this.handleSubmit}>
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <FormattedMessage
                                        defaultMessage="用户ID"
                                        description="用户ID标签"
                                        id="gui.userInfoModal.userId"
                                    />
                                </label>
                                <input
                                    className={styles.input}
                                    type="text"
                                    value={userId || ''}
                                    disabled
                                />
                            </div>
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <FormattedMessage
                                        defaultMessage="用户名"
                                        description="用户名标签"
                                        id="gui.userInfoModal.username"
                                    />
                                </label>
                                <input
                                    className={styles.input}
                                    type="text"
                                    name="username"
                                    value={username}
                                    onChange={this.handleInputChange}
                                    disabled={!isEditing}
                                />
                            </div>
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <FormattedMessage
                                        defaultMessage="邮箱"
                                        description="邮箱标签"
                                        id="gui.userInfoModal.email"
                                    />
                                </label>
                                <input
                                    className={styles.input}
                                    type="email"
                                    name="email"
                                    value={email}
                                    onChange={this.handleInputChange}
                                    disabled={!isEditing}
                                />
                            </div>
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <FormattedMessage
                                        defaultMessage="手机号"
                                        description="手机号标签"
                                        id="gui.userInfoModal.phoneNumber"
                                    />
                                </label>
                                <input
                                    className={styles.input}
                                    type="text"
                                    name="phoneNumber"
                                    value={phoneNumber}
                                    onChange={this.handleInputChange}
                                    disabled={!isEditing}
                                />
                            </div>
                            <div className={styles.formGroup}>
                                <label className={styles.label}>
                                    <FormattedMessage
                                        defaultMessage="注册时间"
                                        description="注册时间标签"
                                        id="gui.userInfoModal.createdAt"
                                    />
                                </label>
                                <input
                                    className={styles.input}
                                    type="text"
                                    value={formattedDate}
                                    disabled
                                />
                            </div>
                            <div className={styles.buttonGroup}>
                                {isEditing ? (
                                    <React.Fragment>
                                        <Button
                                            className={styles.button}
                                            onClick={this.handleCancel}
                                        >
                                            <span className={styles.buttonIcon}>✖</span>
                                            <FormattedMessage
                                                defaultMessage="取消"
                                                description="取消按钮"
                                                id="gui.userInfoModal.cancel"
                                            />
                                        </Button>
                                        <Button
                                            className={classNames(styles.button, styles.submitButton)}
                                            type="submit"
                                        >
                                            <span className={styles.buttonIcon}>✓</span>
                                            <FormattedMessage
                                                defaultMessage="保存"
                                                description="保存按钮"
                                                id="gui.userInfoModal.save"
                                            />
                                        </Button>
                                    </React.Fragment>
                                ) : (
                                    <Button
                                        className={classNames(styles.button, styles.editButton)}
                                        onClick={this.handleEdit}
                                    >
                                        <span className={styles.buttonIcon}>✏️</span>
                                        <FormattedMessage
                                            defaultMessage="编辑"
                                            description="编辑按钮"
                                            id="gui.userInfoModal.edit"
                                        />
                                    </Button>
                                )}
                            </div>
                        </form>

                        {/* 修改密码部分 */}
                        <div className={styles.passwordSection}>
                            <div className={styles.passwordHeader}>
                                <h3>密码管理</h3>
                                {!this.state.showPasswordForm && (
                                    <Button
                                        className={classNames(styles.button, styles.passwordButton)}
                                        onClick={this.togglePasswordForm}
                                    >
                                        修改密码
                                    </Button>
                                )}
                            </div>

                            {this.state.showPasswordForm && (
                                <form onSubmit={this.handlePasswordSubmit}>
                                    {this.state.passwordError && (
                                        <div className={styles.errorMessage}>
                                            {this.state.passwordError}
                                        </div>
                                    )}
                                    <div className={styles.formGroup}>
                                        <label className={styles.label}>
                                            <FormattedMessage
                                                defaultMessage="旧密码"
                                                description="旧密码标签"
                                                id="gui.userInfoModal.oldPassword"
                                            />
                                        </label>
                                        <input
                                            className={styles.input}
                                            type="password"
                                            name="oldPassword"
                                            value={this.state.oldPassword}
                                            onChange={this.handleInputChange}
                                        />
                                    </div>
                                    <div className={styles.formGroup}>
                                        <label className={styles.label}>
                                            <FormattedMessage
                                                defaultMessage="新密码"
                                                description="新密码标签"
                                                id="gui.userInfoModal.newPassword"
                                            />
                                        </label>
                                        <input
                                            className={styles.input}
                                            type="password"
                                            name="newPassword"
                                            value={this.state.newPassword}
                                            onChange={this.handleInputChange}
                                        />
                                    </div>
                                    <div className={styles.formGroup}>
                                        <label className={styles.label}>
                                            <FormattedMessage
                                                defaultMessage="确认新密码"
                                                description="确认新密码标签"
                                                id="gui.userInfoModal.confirmPassword"
                                            />
                                        </label>
                                        <input
                                            className={styles.input}
                                            type="password"
                                            name="confirmPassword"
                                            value={this.state.confirmPassword}
                                            onChange={this.handleInputChange}
                                        />
                                    </div>
                                    <div className={styles.buttonGroup}>
                                        <Button
                                            className={classNames(styles.button, styles.cancelButton)}
                                            onClick={(e) => {
                                                e.preventDefault(); // 防止表单提交
                                                this.togglePasswordForm();
                                            }}
                                            type="button" // 明确指定为普通按钮，不是提交按钮
                                        >
                                            <span className={styles.buttonIcon}>✖</span>
                                            取消
                                        </Button>
                                        <Button
                                            className={classNames(styles.button, styles.submitButton)}
                                            type="submit"
                                            disabled={this.state.isSubmitting}
                                        >
                                            <span className={styles.buttonIcon}>
                                                {this.state.isSubmitting ? '⌛' : '✓'}
                                            </span>
                                            {this.state.isSubmitting ? '提交中' : '确认'}
                                        </Button>
                                    </div>
                                </form>
                            )}
                        </div>
                    </div>
                </Box>
            </Modal>
        );
    }
}

UserInfoModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onUpdateUserInfo: PropTypes.func.isRequired,
    onUpdatePassword: PropTypes.func.isRequired,
    userId: PropTypes.number,
    username: PropTypes.string,
    email: PropTypes.string,
    phoneNumber: PropTypes.string,
    createdAt: PropTypes.string
};

export default UserInfoModal;
