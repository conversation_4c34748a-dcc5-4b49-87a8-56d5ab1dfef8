@import "../../css/units.css";
@import "../../css/colors.css";

.question-container {
    margin: $space;
    border: 1px solid $ui-black-transparent;
    border-radius: $space;
    border-width: 2px;
    padding: 1rem;
    background: white;
}

.question-label {
    font-size: 0.75rem;
    font-weight: bold;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: $text-primary;
    padding-bottom: $space;
}

.question-input {
    display: flex; /* Keeps the input from going outside this container */
    position: relative;
}

.question-submit-button {
    position: absolute;
    top: calc($space / 2);

    width: calc(2rem - $space);
    height: calc(2rem - $space);

    border: none;
    border-radius: 100%;

    color: white;
    background: $looks-secondary;
}

[dir="ltr"] .question-submit-button {
    right: calc($space / 2);
}

[dir="rtl"] .question-submit-button {
    left: calc($space / 2);
}

/* Input overrides: width, font-weight, focus outline and padding */
.question-input > input {
    width: 100%;
    font-weight: normal;
}

[dir="ltr"] .question-input > input {
    padding: 0 2rem 0 .75rem; /* To make room for the submit button */
}

[dir="rtl"] .question-input > input {
    padding: 0 .75rem 0 2rem; /* To make room for the submit button */
}

.question-input > input:focus {
    box-shadow: 0px 0px 0px 3px $looks-transparent;
}

.question-submit-button-icon {
    width: calc(2rem - $space);
    height: calc(2rem - $space);
    position: relative;
    right: -7px;
    left: -7px;
}
