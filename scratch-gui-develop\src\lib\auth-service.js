/**
 * 认证服务
 * 处理token的存储和验证
 */

// Token存储的键名
const TOKEN_KEY = 'scratch_token';
const USER_KEY = 'scratch_user';

/**
 * 保存token到localStorage
 * @param {string} token - JWT token
 */
const setToken = (token) => {
    localStorage.setItem(TOKEN_KEY, token);
};

/**
 * 从localStorage获取token
 * @returns {string|null} - JWT token或null
 */
const getToken = () => {
    return localStorage.getItem(TOKEN_KEY);
};

/**
 * 从localStorage移除token
 */
const removeToken = () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
};

/**
 * 保存用户信息到localStorage
 * @param {Object} user - 用户信息对象
 */
const setUser = (user) => {
    localStorage.setItem(USER_KEY, JSON.stringify(user));
};

/**
 * 从localStorage获取用户信息
 * @returns {Object|null} - 用户信息对象或null
 */
const getUser = () => {
    const userStr = localStorage.getItem(USER_KEY);
    if (userStr) {
        try {
            return JSON.parse(userStr);
        } catch (e) {
            console.error('解析用户信息失败', e);
            return null;
        }
    }
    return null;
};

/**
 * 检查用户是否已登录
 * @returns {boolean} - 是否已登录
 */
const isAuthenticated = () => {
    const token = getToken();
    const user = getUser();
    const hasToken = !!token;
    const hasUser = !!user;
    
    console.log('检查认证状态 - Token存在:', hasToken, '用户信息存在:', hasUser);
    
    // 同时有token和用户信息才算登录
    return hasToken && hasUser;
};

export {
    setToken,
    getToken,
    removeToken,
    setUser,
    getUser,
    isAuthenticated
};
