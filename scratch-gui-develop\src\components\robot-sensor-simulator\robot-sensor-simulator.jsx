import PropTypes from 'prop-types';
import React, {useEffect, useRef} from 'react';
import classNames from 'classnames';
import VM from 'scratch-vm';
import Box from '../box/box.jsx';
import styles from './robot-sensor-simulator.css';

const RobotSensorSimulator = props => {
    const {
        className,
        onClose,
        vm,
        ...componentProps
    } = props;

    const iframeRef = useRef(null);

    // 当组件挂载时，将VM实例暴露给全局
    useEffect(() => {
        if (vm) {
            window.vm = vm;
            console.log('VM实例已暴露给robot_sensor.html');
        }

        // 清理函数
        return () => {
            if (window.vm) {
                delete window.vm;
                console.log('VM实例已从全局清理');
            }
        };
    }, [vm]);

    return (
        <Box
            className={classNames(styles.robotSensorSimulator, className)}
            {...componentProps}
        >
            <div className={styles.header}>
                <h2 className={styles.title}>机器人传感器仿真模拟</h2>
                <button
                    className={styles.closeButton}
                    onClick={onClose}
                >
                    ×
                </button>
            </div>
            <div className={styles.content}>
                <iframe
                    ref={iframeRef}
                    className={styles.iframe}
                    src="/robot_sensor.html"
                    title="机器人传感器仿真模拟"
                />
            </div>
        </Box>
    );
};

RobotSensorSimulator.propTypes = {
    className: PropTypes.string,
    onClose: PropTypes.func.isRequired,
    vm: PropTypes.instanceOf(VM)
};

export default RobotSensorSimulator;
