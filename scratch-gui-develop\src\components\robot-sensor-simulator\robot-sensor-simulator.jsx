import PropTypes from 'prop-types';
import React from 'react';
import classNames from 'classnames';
import Box from '../box/box.jsx';
import styles from './robot-sensor-simulator.css';

const RobotSensorSimulator = props => {
    const {
        className,
        onClose,
        ...componentProps
    } = props;

    return (
        <Box
            className={classNames(styles.robotSensorSimulator, className)}
            {...componentProps}
        >
            <div className={styles.header}>
                <h2 className={styles.title}>机器人传感器仿真模拟</h2>
                <button
                    className={styles.closeButton}
                    onClick={onClose}
                >
                    ×
                </button>
            </div>
            <div className={styles.content}>
                <iframe
                    className={styles.iframe}
                    src="/robot_sensor.html"
                    title="机器人传感器仿真模拟"
                />
            </div>
        </Box>
    );
};

RobotSensorSimulator.propTypes = {
    className: PropTypes.string,
    onClose: PropTypes.func.isRequired
};

export default RobotSensorSimulator;
