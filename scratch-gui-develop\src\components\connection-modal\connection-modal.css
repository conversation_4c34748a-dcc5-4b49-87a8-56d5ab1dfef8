@import "../../css/colors.css";
@import "../../css/units.css";

.modal-content {
    width: 480px;
    line-height: 1.75;
}

.header {
    background-color: $pen-tertiary;
}

.body {
    background: $ui-white;
}

.label {
    font-weight: 500;
    margin: 0 0 0.75rem;
}

.centered-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.peripheral-tile-pane {
    overflow-y: auto;
    width: 100%;
    height: 100%;
}

.peripheral-tile {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    background-color: $ui-white;
    border-radius: 0.25rem;
    padding: 10px;
    width: 100%;
    height: 55px;
    margin-bottom: 0.5rem;
}

.peripheral-tile-name {
    display: flex;
    align-items: center;
}

[dir="ltr"] .peripheral-tile-image {
    margin-right: 0.5rem;
}

[dir="rtl"] .peripheral-tile-image {
    margin-left: 0.5rem;
}

.peripheral-tile-name-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}

.peripheral-tile-name-label {
    font-weight: bold;
    font-size: 0.625rem;
}

.peripheral-tile-name-text {
    font-size: 0.875rem;
}

.peripheral-tile button {
    padding: 0.6rem 0.75rem;
    border: none;
    border-radius: 0.25rem;
    font-weight: 600;
    font-size: 0.85rem;
    background: $looks-secondary;
    border: $looks-secondary;
    color: white;
    cursor: pointer;
}

.signal-strength-meter {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    width: 22px;
    height: 16px;
}

[dir="ltr"] .signal-strength-meter {
    margin-right: 1rem;
}

[dir="rtl"] .signal-strength-meter {
    margin-left: 1rem;
}

.signal-bar {
    width: 4px;
    border-radius: 4px;
    background-color: #DBDBDB;
}

.signal-bar:nth-of-type(1) { height: 25%; }
.signal-bar:nth-of-type(2) { height: 50%; }
.signal-bar:nth-of-type(3) { height: 75%; }
.signal-bar:nth-of-type(4) { height: 100%; }

.green-bar {
    background-color: $pen-primary;
}

.radar-small {
    width: 40px;
    height: 40px;
}

[dir="ltr"] .radar-small {
    margin-right: 0.5rem;
}

[dir="rtl"] .radar-small {
    margin-left: 0.5rem;
}

.radar-big {
    width: 120px;
    height: 120px;
}

.radar-spin {
    animation: spin 4s linear infinite;
}

[dir="ltr"] .radar {
    margin-right: .5rem;
}

[dir="rtl"] .radar {
    margin-left: .5rem;
}

@keyframes spin {
    100% {
        transform: rotate(360deg);
    }
}

.peripheral-activity {
    position: relative;
}

.peripheral-activity-icon {
    /* width: 80px;
    height: 80px; */
}

.connection-tip-icon {
    position: absolute;
}

.bluetooth-connecting-icon {
    position: absolute;
    top: -5px;
    right: -15px;
    left: -15px;
    padding: 5px 5px;
    background-color: $looks-secondary;
    border-radius: 100%;
    box-shadow: 0px 0px 0px 4px $looks-transparent;
    /* animation: pulse-blue-ring 1s infinite ease-in-out alternate; */
    animation: wiggle 0.5s infinite ease-in-out alternate;

}

@keyframes pulse-blue-ring {
    100% {
        box-shadow: 0px 0px 0px 8px $looks-light-transparent;
    }
}

.bluetooth-connected-icon {
    position: absolute;
    top: -5px;
    right: -15px;
    left: -15px;
    padding: 5px 5px;
    background-color: $pen-primary;
    border-radius: 100%;
    box-shadow: 0px 0px 0px 4px $pen-transparent;
}

@keyframes wiggle {
    0% {transform: rotate(3deg) scale(1.05);}
    25% {transform: rotate(-3deg) scale(1.05);}
    50% {transform: rotate(5deg) scale(1.05);}
    75% {transform: rotate(-2deg) scale(1.05);}
    100% {transform: rotate(0deg) scale(1.05);}
}

.bluetooth-centered-icon {
    position: absolute;
    padding: 5px 5px;
    background-color: $looks-secondary;
    border-radius: 100%;
    box-shadow: 0px 0px 0px 2px $looks-transparent;
}

.peripheral-tile-widgets {
    display: flex;
    align-items: center;
}

.activityArea {
    height: 165px;
    background-color: $looks-light-transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: .5rem;
}

.scratch-link-help {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 100%;
    padding-top: .5rem;
    padding-bottom: .5rem;
}

.scratch-link-help-step {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

[dir="ltr"] .scratch-link-help-step {
    margin-left: 2.5rem;
}

[dir="rtl"] .scratch-link-help-step {
    margin-right: 2.5rem;
}

.scratch-link-icon {
    max-width: 50px;
}

.help-step-image {
    max-width: 40px;
}

[dir="ltr"] .help-step-image {
    margin-right: 0.5rem;
}

[dir="rtl"] .help-step-image {
    margin-left: 0.5rem;
}

.help-step-number {
    background: $pen-tertiary;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: $ui-white;
    font-weight: bold;
    min-width: 2rem;
    height: 2rem;
}

[dir="ltr"] .help-step-number {
    margin-right: 0.5rem;
}

[dir="rtl"] .help-step-number {
    margin-left: 0.5rem;
}

.button-row {
    font-weight: bolder;
    text-align: center;
    display: flex;
}

.abort-connecting-icon {
    width: 10px;
    transform: rotate(45deg);
}

.connection-button {
    padding: 0.6rem 0.75rem;
    border-radius: 0.5rem;
    background: $looks-secondary;
    color: white;
    font-weight: 600;
    font-size: 0.85rem;
    margin: 0.25rem;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.connection-button:disabled {
    background: $looks-transparent;
}

.segmented-button {
    display: flex;
}

.segmented-button .connection-button:first-of-type {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    margin-right: 0;
}

.segmented-button .connection-button:last-of-type {
    margin-left: 1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

[dir="ltr"] .button-icon-right {
    margin-left: 0.5rem;
}
[dir="rtl"] .button-icon-right {
    margin-right: 0.5rem;
}

[dir="ltr"] .button-icon-left {
    margin-right: 0.5rem;
}

[dir="rtl"] .button-icon-left {
    margin-left: 0.5rem;
}

/* reverse back arrow icon for RTL, don't reverse other connection icons */
[dir="rtl"] .button-icon-back {
    transform: scaleX(-1);
}

.red-button {
    background: $red-primary;
}

.corner-buttons {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 1rem;
}

.bottom-area {
    background-color: $ui-white;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 1rem;
    padding-bottom: .75rem;
    padding-left: .75rem;
    padding-right: .75rem;
}

.bottom-area .bottom-area-item+.bottom-area-item {
    margin-top: 1rem;
}

.instructions {
    text-align: center;
}

.dots-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.dots-holder {
    display: flex;
    padding: 0.25rem 0.1rem;
    border-radius: 1rem;
    background: $looks-light-transparent;
}

.dots-holder-success {
    background: $pen-transparent;
}

.dots-holder-error {
    background: $error-transparent;
}

.dot {
    width: 0.5rem;
    height: 0.5rem;
    margin: 0 0.3rem;
    border-radius: 100%;
}

.inactive-step-dot {
    background: $looks-transparent;
}

.active-step-dot {
    background: $looks-secondary;
}

.success-dot {
    background: $pen-primary;
}

.error-dot {
    background: $error-primary;
}

.scratch-link-error {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 100%;
    padding-top: .5rem;
    padding-bottom: .5rem;
    width: 90%;
}

.scratch-link-error-details {
    display: flex;
    resize: none;
    height: 60%;
    width: inherit;
}
