@import "../../css/units.css";
@import "../../css/colors.css";
@import "../../css/z-index.css";

.stage-wrapper * {
    box-sizing: border-box;
}

.stage-canvas-wrapper {
    /* Hides negative space between edge of rounded corners + container, when selected */
    user-select: none;
    position: relative;
}

.robot-sensor-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: -200px; /* 向右扩展200px到角色区域 */
    bottom: -150px; /* 向下扩展150px到角色区域 */
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stage-wrapper.full-screen {
    position: fixed;
    top: $stage-menu-height;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $z-index-stage-wrapper-overlay;
    background-color: $ui-white;
    /* spacing between stage and control bar (on the top), or between
    stage and window edges (on left/right/bottom) */
    padding: $stage-full-screen-stage-padding;

    /* this centers the stage */
    display: flex;
    flex-direction: column;
    align-items: center;
}
