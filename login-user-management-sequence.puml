@startuml 登录与用户管理模块时序图

' 定义参与者
actor 用户
participant "前端" as Frontend
participant "后端" as Backend
participant "JWT工具" as JwtUtils

' 核心流程
== 用户登录 ==
用户 -> Frontend: 输入用户名和密码
Frontend -> Backend: UserController.login(LoginRequest)
Backend -> Backend: 验证用户凭证
Backend -> JwtUtils: 生成JWT令牌
JwtUtils --> Backend: 返回令牌
Backend --> Frontend: 返回令牌和用户信息
Frontend -> Frontend: 存储令牌
Frontend --> 用户: 登录成功

== 请求认证 ==
用户 -> Frontend: 发起需要认证的请求
Frontend -> Backend: 请求头携带JWT令牌
Backend -> JwtUtils: 验证令牌
JwtUtils --> Backend: 验证结果

alt 令牌有效
    Backend -> Backend: 设置安全上下文
    Backend --> Frontend: 返回请求数据
    Frontend --> 用户: 显示结果
else 令牌无效
    Backend --> Frontend: 返回401错误
    Frontend --> 用户: 提示重新登录
end

@enduml
