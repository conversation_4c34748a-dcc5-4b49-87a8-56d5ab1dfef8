import React from 'react';
import PropTypes from 'prop-types';

/**
 * 高阶组件，为VM加载行为提供补丁，处理K1扩展加载错误
 * @param {React.Component} WrappedComponent 要包装的组件
 * @returns {React.Component} 增强的组件
 */
const VMLoaderHOC = function (WrappedComponent) {
    class VMLoaderComponent extends React.Component {
        constructor (props) {
            super(props);
            this.patchVM = this.patchVM.bind(this);
        }
        
        componentDidMount () {
            // 在组件挂载后应用VM补丁
            if (this.props.vm) {
                this.patchVM(this.props.vm);
            }
        }
        
        componentDidUpdate (prevProps) {
            // 如果VM属性变化，重新应用补丁
            if (this.props.vm && this.props.vm !== prevProps.vm) {
                this.patchVM(this.props.vm);
            }
        }
        
        /**
         * 给VM应用补丁，处理K1扩展加载错误
         * @param {VM} vm - Scratch VM实例
         */
        patchVM (vm) {
            if (!vm || vm._k1PatchApplied) return;
            
            try {
                console.log('应用VM加载器补丁...');
                
                // 标记VM已应用补丁
                vm._k1PatchApplied = true;
                
                // 如果VM使用Worker并且有扩展管理器
                if (vm.extensionManager) {
                    // 保存原始的扩展加载方法
                    const origLoadExtensionURL = vm.extensionManager._loadExtensionURL;
                    
                    // 重写扩展加载方法
                    vm.extensionManager._loadExtensionURL = function (url) {
                        // 检查是否是K1扩展
                        if (url && typeof url === 'string' && (url.includes('/k1') || url.includes('k1.js'))) {
                            console.log('拦截K1扩展加载请求:', url);
                            
                            // 创建假的K1扩展
                            const fakeK1Extension = {
                                id: 'k1',
                                name: 'K1机器人',
                                blocks: []
                            };
                            
                            // 注册假的K1扩展
                            if (this._loadedExtensions) {
                                this._loadedExtensions.k1 = fakeK1Extension;
                            }
                            
                            // 返回成功Promise
                            return Promise.resolve();
                        }
                        
                        // 对于其他扩展，使用原始方法
                        return origLoadExtensionURL.call(this, url);
                    };
                    
                    console.log('VM扩展管理器补丁已应用');
                }
                
                // 如果VM有原始loadProject方法
                if (vm.loadProject && !vm._originalLoadProject) {
                    // 保存原始的loadProject方法
                    vm._originalLoadProject = vm.loadProject;
                    
                    // 重写loadProject方法
                    vm.loadProject = function (projectData) {
                        return new Promise((resolve, reject) => {
                            this._originalLoadProject(projectData)
                                .then(resolve)
                                .catch(error => {
                                    // 检查是否是K1扩展加载错误
                                    if (error && error.message && (
                                        error.message.includes('k1') || 
                                        error.message.includes('importScripts') ||
                                        error.message.includes('extension')
                                    )) {
                                        console.error('VM加载项目出错(K1相关):', error);
                                        
                                        // 创建假的K1扩展
                                        const fakeK1Extension = {
                                            id: 'k1',
                                            name: 'K1机器人',
                                            blocks: []
                                        };
                                        
                                        // 注册假的K1扩展
                                        if (this.extensionManager && this.extensionManager._loadedExtensions) {
                                            this.extensionManager._loadedExtensions.k1 = fakeK1Extension;
                                            console.log('已注册假的K1扩展');
                                        }
                                        
                                        // 重试加载
                                        this._originalLoadProject(projectData)
                                            .then(resolve)
                                            .catch(secondError => {
                                                console.error('应用K1补丁后加载项目仍然失败:', secondError);
                                                reject(secondError);
                                            });
                                    } else {
                                        reject(error);
                                    }
                                });
                        });
                    };
                    
                    console.log('VM loadProject补丁已应用');
                }
                
                console.log('VM加载器补丁应用完成');
            } catch (e) {
                console.error('应用VM加载器补丁失败:', e);
            }
        }
        
        render () {
            return (
                <WrappedComponent
                    {...this.props}
                />
            );
        }
    }
    
    VMLoaderComponent.propTypes = {
        vm: PropTypes.shape({
            loadProject: PropTypes.func,
            extensionManager: PropTypes.shape({
                _loadExtensionURL: PropTypes.func
            })
        })
    };
    
    return VMLoaderComponent;
};

export default VMLoaderHOC; 