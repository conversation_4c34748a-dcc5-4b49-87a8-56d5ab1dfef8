# K1积木块参数传递功能测试指南

## 功能概述

现在K1机器人积木块已经支持参数传递功能，左侧积木块的参数（速度、距离、角度）能够正确传递到右侧robot_sensor.html页面的机器人上。

## 实现的功能

### 1. 前进积木块参数传递
- **速度参数**：影响实际移动距离的计算
- **距离参数**：控制机器人移动的具体距离
- **计算公式**：实际距离 = 距离 × (速度 / 4)

### 2. 左转积木块参数传递
- **角度参数**：精确控制机器人左转的角度
- **实现方式**：直接使用传入的角度值进行旋转

### 3. 右转积木块参数传递
- **角度参数**：精确控制机器人右转的角度
- **实现方式**：直接使用传入的角度值进行旋转

## 测试步骤

### 测试前进功能

1. **启动系统**
   ```bash
   cd scratch-gui-develop
   npm start
   ```

2. **打开机器人传感器仿真器**
   - 在Scratch界面中点击打开机器人传感器仿真器
   - 确保右侧显示robot_sensor.html页面

3. **测试不同距离参数**
   - 拖拽"前进 速度 4 距离 10"积木块
   - 修改距离参数为20，观察机器人移动距离是否加倍
   - 修改距离参数为5，观察机器人移动距离是否减半

4. **测试不同速度参数**
   - 拖拽"前进 速度 8 距离 10"积木块
   - 观察机器人移动距离是否比速度4时更远
   - 速度8时实际距离 = 10 × (8/4) = 20

### 测试转向功能

1. **测试左转角度参数**
   - 拖拽"左转 90"积木块
   - 观察机器人是否精确左转90度
   - 尝试其他角度值：30度、45度、180度

2. **测试右转角度参数**
   - 拖拽"右转 90"积木块
   - 观察机器人是否精确右转90度
   - 尝试其他角度值：30度、45度、180度

### 测试组合动作

1. **创建测试程序**
   ```
   前进 速度 4 距离 20
   右转 90
   前进 速度 6 距离 15
   左转 45
   前进 速度 2 距离 10
   ```

2. **观察预期行为**
   - 第一步：前进20个单位
   - 第二步：右转90度
   - 第三步：前进22.5个单位（15 × 6/4）
   - 第四步：左转45度
   - 第五步：前进5个单位（10 × 2/4）

## 技术实现细节

### 参数传递链路

```
积木块参数 → K1扩展处理 → controlRobotSimulator() → 
iframe通信 → robot_sensor.html → 精确控制函数
```

### 关键代码修改

1. **K1扩展中的参数传递**
   ```javascript
   // 前进积木块
   controlRobotSimulator('forward', { 
       speed: speed, 
       distance: distance 
   });
   
   // 转向积木块
   controlRobotSimulator('turnLeft', { angle: degree });
   ```

2. **robot_sensor.html中的精确控制**
   ```javascript
   // 精确移动
   function moveRobotByDistance(distance) {
       const newX = ROBOT.x + Math.sin(ROBOT.angle) * distance;
       const newY = ROBOT.y - Math.cos(ROBOT.angle) * distance;
       // ... 碰撞检测和位置更新
   }
   
   // 精确旋转
   function rotateRobotByAngle(angleDegrees) {
       const angleRadians = angleDegrees * Math.PI / 180;
       ROBOT.angle += angleRadians;
   }
   ```

## 调试信息

### 控制台日志
在浏览器开发者工具的控制台中，您可以看到以下调试信息：

1. **K1扩展日志**
   ```
   执行K1前进: 速度=4, 距离=10
   控制仿真器机器人前进，速度: 4, 距离: 10
   ```

2. **仿真器日志**
   ```
   控制机器人前进，速度: 4, 距离: 10
   机器人移动到新位置: 50, 40
   ```

3. **旋转日志**
   ```
   控制机器人左转，角度: 90度
   机器人旋转-90度，当前角度: 270.0度
   ```

## 预期效果

### 视觉反馈
- **Scratch舞台**：精灵按照参数精确移动和旋转
- **仿真器画布**：机器人按照参数精确移动和旋转
- **状态显示**：实时更新机器人坐标和方向角度

### 参数响应
- **距离参数**：机器人移动距离与积木块参数一致
- **速度参数**：影响实际移动距离（速度越高，移动越远）
- **角度参数**：机器人旋转角度与积木块参数精确匹配

## 故障排除

### 常见问题

1. **参数不生效**
   - 检查浏览器控制台是否有错误信息
   - 确认iframe已正确加载robot_sensor.html
   - 验证robotControl接口是否正确暴露

2. **移动距离不准确**
   - 检查速度和距离参数的计算公式
   - 确认moveRobotByDistance函数正确执行

3. **旋转角度不准确**
   - 检查角度转换（度数转弧度）
   - 确认rotateRobotByAngle函数正确执行

### 调试方法

1. **开启控制台日志**
   - 打开浏览器开发者工具
   - 查看Console标签页的日志输出

2. **检查iframe通信**
   - 确认iframe.contentWindow.robotControl存在
   - 验证参数正确传递到仿真器

3. **验证计算结果**
   - 手动计算预期的移动距离和角度
   - 对比实际执行结果

## 总结

通过这次修改，K1积木块现在能够：
- ✅ 精确传递速度和距离参数到仿真器
- ✅ 精确传递角度参数到仿真器
- ✅ 在仿真器中精确执行移动和旋转
- ✅ 同步更新Scratch精灵和仿真器机器人
- ✅ 提供详细的调试日志信息

这使得用户在左侧积木块中设置的参数能够在右侧仿真器中得到准确的响应，大大提升了编程体验的真实性和教育价值。
