import React from 'react';
import ReactDOM from 'react-dom';

// 直接导入组件而不是容器
import LoginPage from '../components/login/login-page.jsx';

import styles from './login.css';

/**
 * 渲染登录页面
 * @param {HTMLElement} appTarget - 渲染目标DOM元素
 */
const renderLogin = appTarget => {
    if (process.env.NODE_ENV === 'production' && typeof window === 'object') {
        // 禁用离开页面的警告
        window.onbeforeunload = () => {};
    }

    ReactDOM.render((
        <LoginPage />
    ), appTarget);
};

export default renderLogin;
