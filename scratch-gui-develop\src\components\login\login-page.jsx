import React, {useState, useEffect, useCallback} from 'react';
import PropTypes from 'prop-types';
import axios from 'axios';
import styles from './login-page.css';
import {getToken, setToken, setUser, removeToken} from '../../lib/auth-service';

// 后端API的基础URL
const API_BASE_URL = 'http://localhost:8080/api';

// 创建axios实例
const apiClient = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    withCredentials: true
});

const LoginPage = () => {
    // 状态管理
    const [activeView, setActiveView] = useState('login'); // 'login' 或 'register'
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const [isLoggedIn, setIsLoggedIn] = useState(false);

    // 初始化时检查登录状态
    useEffect(() => {
        const token = getToken();
        if (token) {
            setIsLoggedIn(true);
        }
        
        // 捕获所有点击事件，用于调试
        const handleDocumentClick = (e) => {
            console.log('Document clicked:', e.target);
        };
        
        // 添加全局点击事件监听器
        document.addEventListener('click', handleDocumentClick);
        
        // 清理函数
        return () => {
            document.removeEventListener('click', handleDocumentClick);
        };
    }, []);

    // 处理登录 - 使用useCallback记忆函数
    const handleLogin = useCallback(async (e) => {
        console.log('Login form submitted');
        if (e) e.preventDefault();
        
        // 验证输入
        if (!username || !password) {
            setError('用户名和密码不能为空');
            return;
        }
        
        setLoading(true);
        setError('');
        
        try {
            console.log('Sending login request...');
            const response = await apiClient.post('/user/login', {
                username,
                password
            });
            
            console.log('登录成功:', response.data);
            
            // 保存token和用户信息
            setToken(response.data.token);
            setUser(response.data.user);
            setIsLoggedIn(true);
            
        } catch (error) {
            console.error('登录失败:', error);
            if (error.response) {
                setError(error.response.data.message || '登录失败');
            } else {
                setError('网络错误，请检查服务器连接');
            }
        } finally {
            setLoading(false);
        }
    }, [username, password]);

    // 处理注册 - 使用useCallback记忆函数
    const handleRegister = useCallback(async (e) => {
        console.log('Register form submitted');
        if (e) e.preventDefault();
        
        // 验证输入
        if (!username || !password) {
            setError('用户名和密码不能为空');
            return;
        }
        
        if (username.length < 3) {
            setError('用户名长度不能少于3个字符');
            return;
        }
        
        if (password.length < 6) {
            setError('密码长度不能少于6个字符');
            return;
        }
        
        setLoading(true);
        setError('');
        
        try {
            console.log('Sending register request...');
            const response = await apiClient.post('/user/register', {
                username,
                password
            });
            
            console.log('注册成功:', response.data);
            
            // 保存token和用户信息
            setToken(response.data.token);
            setUser(response.data.user);
            setIsLoggedIn(true);
            
        } catch (error) {
            console.error('注册失败:', error);
            if (error.response) {
                setError(error.response.data.message || '注册失败');
            } else {
                setError('网络错误，请检查服务器连接');
            }
        } finally {
            setLoading(false);
        }
    }, [username, password]);

    // 处理登出 - 使用useCallback记忆函数
    const handleLogout = useCallback(() => {
        console.log('Logout button clicked');
        // 清除token和用户信息
        removeToken();
        setIsLoggedIn(false);
    }, []);

    // 进入系统 - 使用useCallback记忆函数
    const enterSystem = useCallback(() => {
        console.log('Enter system button clicked');
        window.location.href = '/';
    }, []);

    // 切换到注册视图
    const switchToRegister = useCallback(() => {
        console.log('Switching to register view');
        setActiveView('register');
        setError('');
    }, []);

    // 切换到登录视图
    const switchToLogin = useCallback(() => {
        console.log('Switching to login view');
        setActiveView('login');
        setError('');
    }, []);

    // 登录表单 - 使用直接返回JSX而不是定义组件
    const renderLoginForm = () => (
        <form onSubmit={handleLogin} className={styles.form}>
            <h2 className={styles.formTitle}>登录</h2>
            
            <div className={styles.formGroup}>
                <label htmlFor="username" className={styles.label}>用户名</label>
                <input
                    type="text"
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className={styles.input}
                    placeholder="请输入用户名"
                    required
                />
            </div>
            
            <div className={styles.formGroup}>
                <label htmlFor="password" className={styles.label}>密码</label>
                <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={styles.input}
                    placeholder="请输入密码"
                    required
                />
            </div>
            
            <button 
                type="submit" 
                className={styles.submitButton} 
                disabled={loading}
                onClick={() => console.log('Login button clicked')}
            >
                {loading ? '登录中...' : '登录'}
            </button>
            
            <div className={styles.switchForm}>
                <span>没有账号？</span>
                <button
                    type="button"
                    onClick={switchToRegister}
                    className={styles.switchButton}
                >
                    去注册
                </button>
            </div>
        </form>
    );

    // 注册表单 - 使用直接返回JSX而不是定义组件
    const renderRegisterForm = () => (
        <form onSubmit={handleRegister} className={styles.form}>
            <h2 className={styles.formTitle}>注册</h2>
            
            <div className={styles.formGroup}>
                <label htmlFor="username" className={styles.label}>用户名</label>
                <input
                    type="text"
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className={styles.input}
                    placeholder="请输入用户名(至少3个字符)"
                    required
                    minLength={3}
                />
            </div>
            
            <div className={styles.formGroup}>
                <label htmlFor="password" className={styles.label}>密码</label>
                <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={styles.input}
                    placeholder="请输入密码(至少6个字符)"
                    required
                    minLength={6}
                />
            </div>
            
            <button 
                type="submit" 
                className={styles.submitButton} 
                disabled={loading}
                onClick={() => console.log('Register button clicked')}
            >
                {loading ? '注册中...' : '注册'}
            </button>
            
            <div className={styles.switchForm}>
                <span>已有账号？</span>
                <button
                    type="button"
                    onClick={switchToLogin}
                    className={styles.switchButton}
                >
                    去登录
                </button>
            </div>
        </form>
    );

    // 已登录视图 - 使用直接返回JSX而不是定义组件
    const renderLoggedInView = () => (
        <div className={styles.loggedInContainer}>
            <h2 className={styles.welcomeTitle}>欢迎回来！</h2>
            <div className={styles.buttonGroup}>
                <button 
                    onClick={enterSystem} 
                    className={styles.enterButton}
                >
                    进入系统
                </button>
                <button 
                    onClick={handleLogout} 
                    className={styles.logoutButton}
                >
                    退出登录
                </button>
            </div>
        </div>
    );

    return (
        <div className={styles.loginPageWrapper}>
            <div className={styles.loginContainer}>
                <div className={styles.logoContainer}>
                    <h1 className={styles.systemTitle}>机器人仿真运行系统</h1>
                </div>
                
                {error && (
                    <div className={styles.errorMessage}>
                        {error}
                    </div>
                )}
                
                {isLoggedIn ? (
                    renderLoggedInView()
                ) : (
                    activeView === 'login' ? renderLoginForm() : renderRegisterForm()
                )}
            </div>
        </div>
    );
};

export default LoginPage;
