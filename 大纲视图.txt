图形化编程界面模块

表示层（前端）
gui.jsx - 主界面组件，包含菜单栏、工具栏、积木面板、编辑区、舞台区等
blocks.jsx - 积木编辑区组件
stage.jsx - 舞台区组件
stage-wrapper.jsx - 舞台包装组件
menu-bar.jsx - 菜单栏组件
c-code-panel.jsx - C代码面板组件
target-pane.jsx - 精灵列表组件

控制层类（后端）
ProjectController - 项目控制器，处理项目的创建、保存、加载等操作
3. 控制层类主要包含的函数（后端）
ProjectController：
getUserProjects() - 获取用户的项目列表
getProjectDetail() - 获取项目详情
saveProject() - 保存项目
createProjectVersion() - 创建项目版本

业务逻辑层类（后端）
ProjectService - 项目服务接口
ProjectServiceImpl - 项目服务实现类
5. 业务逻辑层类主要包含的函数（后端）
ProjectService/ProjectServiceImpl：
getUserProjects() - 获取用户的项目列表
getProjectDetail() - 获取项目详情
saveProject() - 保存项目
getLatestVersionNumber() - 获取项目的最新版本号
createProjectVersion() - 为项目创建新版本

数据持久层类（后端）
ProjectRepository - 项目数据访问接口
VersionRepository - 版本数据访问接口
UserRepository - 用户数据访问接口
数据持久层类主要包含的函数（后端）
ProjectRepository：
findByUserId() - 根据用户ID查找项目列表
findById() - 根据ID查找项目
save() - 保存项目
VersionRepository：
findByProjectId() - 根据项目ID查找版本列表
findFirstByProjectIdOrderByIdDesc() - 根据项目ID查找最新版本
UserRepository：
findByUsername() - 根据用户名查找用户
existsByUsername() - 检查用户名是否存在

---

3.4.2传感器仿真模块

表示层（前端）
积木面板中的传感器积木块，包括：
Scratch内置侦测类积木（如sensing_touchingcolor、sensing_coloristouchingcolor等）
K1传感器积木（如k1_lightsensor、k1_distsensor、k1_mic1sensor等）
这些积木块在blocks.jsx和相关组件中呈现
2. 控制层类（后端）
ProjectController - 项目控制器，处理项目的保存和加载，包含传感器积木块的数据
3. 控制层类主要包含的函数（后端）
ProjectController：
saveProject() - 保存包含传感器积木块的项目
getProjectDetail() - 获取包含传感器积木块的项目详情
4. 业务逻辑层类（后端）
ProjectService - 项目服务接口
ProjectServiceImpl - 项目服务实现类
5. 业务逻辑层类主要包含的函数（后端）
ProjectService/ProjectServiceImpl：
saveProject() - 保存包含传感器积木块的项目数据
getProjectDetail() - 获取包含传感器积木块的项目详情
6. 数据持久层类（后端）
ProjectRepository - 项目数据访问接口，存储包含传感器积木块的项目数据
7. 数据持久层类主要包含的函数（后端）
ProjectRepository：
findById() - 根据ID查找包含传感器积木块的项目
save() - 保存包含传感器积木块的项目

---

虚拟机执行模块分析
1、表示层：
vm-listener-hoc.jsx - 负责监听VM事件并更新UI
vm-manager-hoc.jsx - 负责管理VM生命周期和项目加载
blocks.jsx - 积木块编辑器组件
controls.jsx - 控制面板组件（包含绿旗、停止按钮等）
2、控制层类：
ProjectController - 项目控制器，处理项目相关请求
3、控制层类主要包含的函数：
getUserProjects() - 获取用户项目列表
getProjectDetail() - 获取项目详情
saveProject() - 保存项目
saveModification() - 保存项目修改
getProjectVersion() - 获取项目版本
createProjectVersionByPath() - 创建项目新版本
4、业务逻辑层类：
ProjectServiceImpl - 项目服务实现类
VersionServiceImpl - 版本服务实现类
5、业务逻辑层类主要包含的函数：
ProjectServiceImpl.getUserProjects() - 获取用户项目列表
ProjectServiceImpl.getProjectDetail() - 获取项目详情
ProjectServiceImpl.saveProject() - 保存项目
ProjectServiceImpl.getLatestVersionNumber() - 获取最新版本号
ProjectServiceImpl.createProjectVersion() - 创建项目版本
VersionServiceImpl.getProjectVersions() - 获取项目版本列表
VersionServiceImpl.getLatestVersion() - 获取最新版本
VersionServiceImpl.createNewVersion() - 创建新版本
VersionServiceImpl.updateVersionNumber() - 更新版本号
6、数据持久层类：
ProjectRepository - 项目数据访问接口
VersionRepository - 版本数据访问接口
7、数据持久层类主要包含的函数：
ProjectRepository.findById() - 根据ID查找项目
ProjectRepository.findByUserId() - 根据用户ID查找项目
ProjectRepository.save() - 保存项目
VersionRepository.findByProjectId() - 根据项目ID查找版本
VersionRepository.findFirstByProjectIdOrderByIdDesc() - 查找项目最新版本
VersionRepository.save() - 保存版本

----

C代码生成器模块分析
1、表示层：
c-code-panel.jsx - C代码面板容器组件
c-code-panel/c-code-panel.jsx - C代码面板展示组件
2、控制层类：
ProjectController - 项目控制器，处理项目相关请求
3、控制层类主要包含的函数：
saveProject() - 保存包含积木块的项目
getProjectDetail() - 获取项目详情，包括积木块数据
4、业务逻辑层类：
ProjectServiceImpl - 项目服务实现类
5、业务逻辑层类主要包含的函数：
saveProject() - 保存项目数据，包括积木块信息
getProjectDetail() - 获取项目详情，包括积木块数据
6、数据持久层类：
ProjectRepository - 项目数据访问接口
7、数据持久层类主要包含的函数：
findById() - 根据ID查找项目
save() - 保存项目数据

---

项目管理模块分析
1、表示层：
project-list-modal.jsx - 项目列表模态框组件
menu-bar.jsx - 菜单栏组件，包含项目操作按钮
save-modification-button.jsx - 保存修改按钮组件
project-saver-hoc.jsx - 项目保存高阶组件
2、控制层类：
ProjectController - 项目控制器，处理项目相关请求
3、控制层类主要包含的函数：
getUserProjects() - 获取用户项目列表
getProjectDetail() - 获取项目详情
saveProject() - 保存项目
saveModification() - 保存项目修改
getProjectVersion() - 获取项目版本
createProjectVersion() - 创建项目新版本
createProjectVersionByPath() - 通过路径参数创建项目新版本
4、业务逻辑层类：
ProjectServiceImpl - 项目服务实现类
VersionServiceImpl - 版本服务实现类
5、业务逻辑层类主要包含的函数：
ProjectServiceImpl.getUserProjects() - 获取用户项目列表
ProjectServiceImpl.getProjectDetail() - 获取项目详情
ProjectServiceImpl.saveProject() - 保存项目
ProjectServiceImpl.getLatestVersionNumber() - 获取最新版本号
ProjectServiceImpl.createProjectVersion() - 创建项目版本
VersionServiceImpl.getProjectVersions() - 获取项目版本列表
VersionServiceImpl.getLatestVersion() - 获取最新版本
VersionServiceImpl.createNewVersion() - 创建新版本
VersionServiceImpl.updateVersionNumber() - 更新版本号
VersionServiceImpl.generateNextVersionNumber() - 生成下一个版本号
6、数据持久层类：
ProjectRepository - 项目数据访问接口
VersionRepository - 版本数据访问接口
UserRepository - 用户数据访问接口
7、数据持久层类主要包含的函数：
ProjectRepository.findById() - 根据ID查找项目
ProjectRepository.findByUserId() - 根据用户ID查找项目
ProjectRepository.save() - 保存项目
VersionRepository.findByProjectId() - 根据项目ID查找版本
VersionRepository.findFirstByProjectIdOrderByIdDesc() - 查找项目最新版本
VersionRepository.save() - 保存版本
UserRepository.findByUsername() - 根据用户名查找用户
UserRepository.existsByUsername() - 检查用户名是否存在