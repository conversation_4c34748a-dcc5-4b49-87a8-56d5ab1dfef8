spring.application.name=scratch-server
server.port=8080

# 数据库配置
spring.datasource.url=***************************************************************************************************
spring.datasource.username=root
spring.datasource.password=qjj765..
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# JWT配置
jwt.expiration=86400000
jwt.secret=scratchSecretKey123456789012345678901234567890123456789012345678901234567890

# 跨域配置
cors.allowed-origins=http://localhost:8899
