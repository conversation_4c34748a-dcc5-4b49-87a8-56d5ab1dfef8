import PropTypes from 'prop-types';
import React from 'react';
import bindAll from 'lodash.bindall';
import VM from 'scratch-vm';
import {connect} from 'react-redux';

import CCodePanelComponent from '../components/c-code-panel/c-code-panel.jsx';
import generateCCodeFromBlocks from '../lib/c-code-generator.js';

class CCodePanel extends React.Component {
    constructor (props) {
        super(props);
        bindAll(this, [
            'handleWorkspaceUpdate',
            'handleTargetsUpdate',
            'handleBlockDragUpdate',
            'handleRunClick',
            'createCustomCCode',
            'loadPicoC'
        ]);
        this.state = {
            codeContent: '// C代码将在这里显示',
            runStatus: null,
            runMessage: '',
            isCompiling: false,
            isRunning: false,
            picocLoaded: false
        };

        // PicoC 实例
        this.picocjs = null;

        // 动态加载 PicoC
        this.loadPicoC();
    }

    componentDidMount () {
        if (this.props.vm) {
            // 监听所有可能导致代码需要更新的事件
            this.props.vm.addListener('workspaceUpdate', this.handleWorkspaceUpdate);
            this.props.vm.addListener('targetsUpdate', this.handleTargetsUpdate);
            this.props.vm.addListener('BLOCK_DRAG_UPDATE', this.handleBlockDragUpdate);
            this.props.vm.addListener('BLOCKS_CHANGED', this.handleWorkspaceUpdate);

            // 初始化时生成一次代码
            this.updateCCode();
        }
    }

    componentDidUpdate (prevProps) {
        // 如果VM或编辑目标改变，更新代码
        if (this.props.vm !== prevProps.vm ||
            (this.props.vm && prevProps.vm &&
             this.props.vm.editingTarget !== prevProps.vm.editingTarget)) {
            this.updateCCode();
        }
    }

    componentWillUnmount () {
        if (this.props.vm) {
            this.props.vm.removeListener('workspaceUpdate', this.handleWorkspaceUpdate);
            this.props.vm.removeListener('targetsUpdate', this.handleTargetsUpdate);
            this.props.vm.removeListener('BLOCK_DRAG_UPDATE', this.handleBlockDragUpdate);
            this.props.vm.removeListener('BLOCKS_CHANGED', this.handleWorkspaceUpdate);
        }
    }

    /**
     * 动态加载 PicoC
     */
    async loadPicoC () {
        try {
            // 检查是否已经加载
            if (window.picocjs) {
                this.picocjs = window.picocjs;
                this.setState({picocLoaded: true});
                console.log('PicoC 已经加载');
                return;
            }

            console.log('开始加载 PicoC...');

            // 动态加载本地 PicoC 脚本
            const script = document.createElement('script');
            script.src = './static/picoc-test/bundle.umd.js';

            script.onload = () => {
                console.log('PicoC 脚本加载成功');
                if (window.picocjs) {
                    this.picocjs = window.picocjs;
                    this.setState({picocLoaded: true});
                    console.log('PicoC 初始化完成');
                } else {
                    console.error('PicoC 脚本加载后未找到 picocjs 对象');
                    this.setState({
                        runStatus: 'error',
                        runMessage: 'PicoC 初始化失败'
                    });
                }
            };

            script.onerror = (error) => {
                console.error('PicoC 脚本加载失败:', error);
                this.setState({
                    runStatus: 'error',
                    runMessage: 'PicoC 加载失败，请检查文件是否存在'
                });
            };

            document.head.appendChild(script);
        } catch (error) {
            console.error('PicoC 加载异常:', error);
            this.setState({
                runStatus: 'error',
                runMessage: `PicoC 加载异常: ${error.message}`
            });
        }
    }

    /**
     * 创建可执行的 C 代码，将 Scratch VM 操作嵌入到 C 代码中
     */
    createCustomCCode (originalCode) {
        if (!this.props.vm) return originalCode;

        const vm = this.props.vm;

        // 创建一个全局的 VM 控制器对象
        window.scratchVMController = {
            // 基础移动函数
            moveForward: (steps) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setXY(target.x + steps, target.y);
                    vm.runtime.requestRedraw();
                }
            },
            moveBackward: (steps) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setXY(target.x - steps, target.y);
                    vm.runtime.requestRedraw();
                }
            },
            turnLeft: (degrees) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setDirection(target.direction - degrees);
                    vm.runtime.requestRedraw();
                }
            },
            turnRight: (degrees) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setDirection(target.direction + degrees);
                    vm.runtime.requestRedraw();
                }
            },
            setPosition: (x, y) => {
                const target = vm.editingTarget;
                if (target) {
                    target.setXY(x, y);
                    vm.runtime.requestRedraw();
                }
            },

            // K1 机器人函数
            // 1. 说话函数
            say: (mode, text) => {
                console.log(`gpp_say: mode=${mode}, text=${text}`);
                const target = vm.editingTarget;
                if (target) {
                    // 清理文本中的引号
                    const cleanText = text.replace(/['"]/g, '');
                    target.say(cleanText);

                    // 如果是模式0，2秒后清除对话框
                    if (mode === 0) {
                        setTimeout(() => {
                            if (target) target.say(null);
                        }, 2000);
                    }
                }
            },

            // 2. 电机控制函数
            motorRun: (motor, speed) => {
                console.log(`Motor_Run: motor=${motor}, speed=${speed}`);
                const target = vm.editingTarget;
                if (target) {
                    if (motor === 1) {
                        // 电机1控制X轴移动
                        target.setXY(target.x + speed / 5, target.y);
                    } else if (motor === 2) {
                        // 电机2控制Y轴移动
                        target.setXY(target.x, target.y + speed / 5);
                    }
                    vm.runtime.requestRedraw();
                }
            },
            motorStop: (motor) => {
                console.log(`Motor_Stop: motor=${motor}`);
                // 停止电机，这里可以停止移动动画
            },

            // 3. 移动函数
            forward: (speed, distance) => {
                console.log(`forward: speed=${speed}, distance=${distance}`);
                const target = vm.editingTarget;
                if (target) {
                    const moveDistance = distance * (speed / 4);
                    const direction = target.direction;
                    const radians = direction * Math.PI / 180;
                    const dx = moveDistance * Math.sin(radians);
                    const dy = moveDistance * Math.cos(radians);
                    target.setXY(target.x + dx, target.y + dy);
                    vm.runtime.requestRedraw();
                }
            },
            back: (speed, distance) => {
                console.log(`back: speed=${speed}, distance=${distance}`);
                const target = vm.editingTarget;
                if (target) {
                    const moveDistance = distance * (speed / 4);
                    const direction = target.direction;
                    const radians = direction * Math.PI / 180;
                    const dx = -moveDistance * Math.sin(radians);
                    const dy = -moveDistance * Math.cos(radians);
                    target.setXY(target.x + dx, target.y + dy);
                    vm.runtime.requestRedraw();
                }
            },

            // 4. 机械手函数
            servoOpen: () => {
                console.log('servo_open: 打开机械手');
                const target = vm.editingTarget;
                if (target) {
                    // 可以在这里添加视觉效果，比如改变造型
                    target.say('机械手打开');
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                }
            },
            servoClose: () => {
                console.log('servo_close: 关闭机械手');
                const target = vm.editingTarget;
                if (target) {
                    target.say('机械手关闭');
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                }
            },

            // 5. 音效和LED函数
            beep: (bound, time) => {
                console.log(`beep: bound=${bound}, time=${time}`);
                const target = vm.editingTarget;
                if (target) {
                    target.say(`蜂鸣器响${time}ms`);
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                }
            },
            led: (mode, rgb) => {
                console.log(`colorful_led: mode=${mode}, rgb=${rgb}`);
                const target = vm.editingTarget;
                if (target) {
                    target.say(`LED灯模式${mode}`);
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                }
            },

            // 6. 延时函数
            delay: async (ms) => {
                return new Promise(resolve => {
                    setTimeout(resolve, ms);
                });
            }
        };

        // 创建自定义函数实现，这些函数会通过printf输出特定格式的命令
        // 然后我们解析这些输出来控制Scratch VM
        const customFunctions = `
// 自定义函数实现 - 通过printf输出命令，然后解析控制Scratch
void forward(int speed, int distance) {
    printf("FORWARD:%d:%d\\n", speed, distance);
}

void back(int speed, int distance) {
    printf("BACK:%d:%d\\n", speed, distance);
}

void turn_left(int degree) {
    printf("TURN_LEFT:%d\\n", degree);
}

void turn_right(int degree) {
    printf("TURN_RIGHT:%d\\n", degree);
}

void turnleft(int degree) {
    printf("TURN_LEFT:%d\\n", degree);
}

void turnright(int degree) {
    printf("TURN_RIGHT:%d\\n", degree);
}

void gpp_say(int mode, void *str) {
    printf("GPP_SAY:%d:%s\\n", mode, (char*)str);
}

int servo_open(void) {
    printf("SERVO_OPEN\\n");
    return 1;
}

int servo_close(void) {
    printf("SERVO_CLOSE\\n");
    return 1;
}

int tracker_start(void) {
    printf("TRACKER_START\\n");
    return 1;
}

int tracker_close(void) {
    printf("TRACKER_CLOSE\\n");
    return 1;
}

void beep(int bound, int time) {
    printf("BEEP:%d:%d\\n", bound, time);
}

void colorful_led(int mode, int rgb) {
    printf("LED:%d:%d\\n", mode, rgb);
}

void Set_CScript_Mode(int mode) {
    printf("SET_CSCRIPT_MODE:%d\\n", mode);
}

void cexit(void) {
    printf("CEXIT\\n");
}

int lightsensor(void) {
    printf("LIGHTSENSOR\\n");
    return 50; // 模拟返回值
}

int distsensor(void) {
    printf("DISTSENSOR\\n");
    return 30; // 模拟返回值
}

int mic1sensor(void) {
    printf("MIC1SENSOR\\n");
    return 40; // 模拟返回值
}

int tracker(int id) {
    printf("TRACKER:%d\\n", id);
    return 1; // 模拟返回值
}

int Get_Ps2Value(void) {
    printf("GET_PS2VALUE\\n");
    return 0; // 模拟返回值
}

void Motor_Run(int motor, int speed) {
    printf("MOTOR_RUN:%d:%d\\n", motor, speed);
}

void Motor_Stop(int motor) {
    printf("MOTOR_STOP:%d\\n", motor);
}

`;

        // 在原始代码的#include之后、main函数之前插入自定义函数
        let modifiedCode = originalCode;

        // 找到最后一个#include的位置
        const includeRegex = /#include\s*<[^>]+>/g;
        let lastIncludeIndex = -1;
        let match;
        while ((match = includeRegex.exec(originalCode)) !== null) {
            lastIncludeIndex = match.index + match[0].length;
        }

        if (lastIncludeIndex !== -1) {
            // 在最后一个#include之后插入自定义函数
            modifiedCode = originalCode.slice(0, lastIncludeIndex) + '\n\n' + customFunctions + '\n' + originalCode.slice(lastIncludeIndex);
        } else {
            // 如果没有找到#include，在代码开头插入
            modifiedCode = customFunctions + '\n' + originalCode;
        }

        // 移除原始代码中的函数声明（因为我们已经提供了实现）
        modifiedCode = modifiedCode.replace(/void forward\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/void back\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/void turn_left\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/void turn_right\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/void gpp_say\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int servo_open\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int servo_close\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int tracker_start\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int tracker_close\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/void beep\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/void colorful_led\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/void Set_CScript_Mode\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/void cexit\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int lightsensor\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int distsensor\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int mic1sensor\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int tracker\s*\([^)]*\)\s*;/g, '');
        modifiedCode = modifiedCode.replace(/int Get_Ps2Value\s*\([^)]*\)\s*;/g, '');

        // 移除重复的函数声明注释
        modifiedCode = modifiedCode.replace(/\/\/ 为使用K1机器人函数添加的函数声明[\s\S]*?int Get_Ps2Value\(void\);\s*/g, '');

        console.log('修改后的C代码:', modifiedCode);
        return modifiedCode;
    }

    handleWorkspaceUpdate () {
        console.log('工作区更新，重新生成C代码');
        this.updateCCode();
    }

    handleTargetsUpdate () {
        // 当目标（角色）更新时更新代码
        this.updateCCode();
    }

    handleBlockDragUpdate () {
        // 当块被拖动时更新代码
        this.updateCCode();
    }

    updateCCode () {
        if (!this.props.vm || !this.props.vm.editingTarget) {
            this.setState({
                codeContent: '// 没有选择角色',
                runStatus: null,
                runMessage: ''
            });
            return;
        }

        try {
            const target = this.props.vm.editingTarget;
            console.log('正在为目标生成C代码:', target.getName());
            const cCode = generateCCodeFromBlocks(target, this.props.vm);
            this.setState({
                codeContent: cCode,
                runStatus: null,
                runMessage: ''
            });
        } catch (e) {
            console.error('生成C代码时出错:', e);
            this.setState({
                codeContent: `// 生成代码时出错: ${e.message}`,
                runStatus: 'error',
                runMessage: `生成代码时出错: ${e.message}`
            });
        }
    }

    /**
     * 处理运行按钮点击事件
     */
    async handleRunClick () {
        const code = this.state.codeContent;

        // 检查代码是否为空
        if (!code || code.trim() === '' || code.includes('// 没有选择角色') || code.includes('// 没有积木块可以转换')) {
            this.setState({
                runStatus: 'error',
                runMessage: '没有可运行的代码，请先添加积木块'
            });
            return;
        }

        // 检查 PicoC 是否已加载
        if (!this.state.picocLoaded || !this.picocjs) {
            this.setState({
                runStatus: 'error',
                runMessage: 'PicoC 尚未加载完成，请稍后再试'
            });
            return;
        }

        this.setState({
            isCompiling: true,
            isRunning: false,
            runStatus: null,
            runMessage: '正在编译C代码...'
        });

        try {
            // 触发绿旗事件（启动Scratch VM）
            if (this.props.vm) {
                this.props.vm.start();
                this.props.vm.greenFlag();
            }

            // 创建可执行的 C 代码
            const executableCode = this.createCustomCCode(code);
            console.log('修改后的C代码:', executableCode);

            this.setState({
                isCompiling: false,
                isRunning: true,
                runMessage: '正在执行C代码...'
            });

            // 收集输出用于解析命令和显示
            let outputBuffer = '';
            let displayOutput = '';

            // 创建输出显示弹窗
            this.createOutputModal();

            // 使用动态加载的 picoc-js 执行代码
            console.log('开始执行C代码');

            await this.picocjs.runC(executableCode, (output) => {
                console.log('C程序输出:', output);
                outputBuffer += output;

                // 处理输出，确保换行符正确
                const processedOutput = output.replace(/\\n/g, '\n');
                displayOutput += processedOutput;

                // 更新弹窗显示
                this.updateOutputModal(displayOutput);

                // 解析输出并执行相应的 Scratch 操作
                this.parseAndExecuteCommands(output);
            });

            console.log('C代码执行完成');

            // 执行完成后更新弹窗状态
            this.finalizeOutputModal(displayOutput);

            // 触发绿旗动画
            this.triggerGreenFlag();

            this.setState({
                isRunning: false,
                runStatus: 'success',
                runMessage: 'C代码执行成功！精灵已按照程序运行。'
            });

        } catch (error) {
            console.error('C代码执行失败:', error);

            let errorMessage = error.message || '未知错误';

            // 根据错误类型提供更友好的错误信息
            if (errorMessage.includes('syntax')) {
                errorMessage = `语法错误: ${errorMessage}`;
            } else if (errorMessage.includes('undefined')) {
                errorMessage = `未定义的函数或变量: ${errorMessage}`;
            } else if (errorMessage.includes('compile')) {
                errorMessage = `编译错误: ${errorMessage}`;
            }

            this.setState({
                isCompiling: false,
                isRunning: false,
                runStatus: 'error',
                runMessage: `执行失败: ${errorMessage}`
            });
        }
    }

    /**
     * 创建C代码输出显示弹窗
     */
    createOutputModal() {
        // 移除已存在的弹窗
        const existingModal = document.getElementById('c-output-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // 创建弹窗HTML
        const modalHTML = `
            <div id="c-output-modal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            ">
                <div style="
                    background-color: white;
                    border-radius: 8px;
                    padding: 20px;
                    max-width: 600px;
                    max-height: 500px;
                    width: 90%;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                ">
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 15px;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 10px;
                    ">
                        <h3 style="margin: 0; color: #333;">C代码执行输出</h3>
                        <button id="close-output-modal" style="
                            background: none;
                            border: none;
                            font-size: 20px;
                            cursor: pointer;
                            color: #666;
                        ">×</button>
                    </div>
                    <div id="output-content" style="
                        background-color: #000;
                        color: #00ff00;
                        padding: 15px;
                        border-radius: 4px;
                        font-family: 'Courier New', monospace;
                        font-size: 14px;
                        white-space: pre-wrap;
                        max-height: 350px;
                        overflow-y: auto;
                        line-height: 1.6;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                    ">正在执行C代码...</div>
                    <div style="
                        margin-top: 15px;
                        text-align: right;
                    ">
                        <span id="execution-status" style="
                            color: #666;
                            font-size: 12px;
                        ">执行中...</span>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // 添加关闭事件
        const closeBtn = document.getElementById('close-output-modal');
        const modal = document.getElementById('c-output-modal');

        closeBtn.onclick = () => modal.remove();
        modal.onclick = (e) => {
            if (e.target === modal) modal.remove();
        };
    }

    /**
     * 更新输出弹窗内容
     */
    updateOutputModal(output) {
        const outputContent = document.getElementById('output-content');
        if (outputContent) {
            // 处理输出文本，确保换行符正确显示
            let processedOutput = output || '正在执行C代码...';

            // 确保换行符正确处理
            processedOutput = processedOutput.replace(/\\n/g, '\n');

            // 格式化输出，特别处理循环相关的输出
            const lines = processedOutput.split('\n');
            const formattedLines = [];

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) {
                    formattedLines.push(''); // 保留空行
                    continue;
                }

                // 循环开始标记 - 添加分隔线
                if (line.includes('次循环开始')) {
                    formattedLines.push(''); // 空行
                    formattedLines.push(`🔄 ${line}`);
                    formattedLines.push('─'.repeat(40)); // 分隔线
                }
                // 循环结束标记 - 添加分隔线
                else if (line.includes('次循环结束')) {
                    formattedLines.push(`✅ ${line}`);
                    formattedLines.push('─'.repeat(40)); // 分隔线
                    formattedLines.push(''); // 空行
                }
                // 程序开始/结束
                else if (line.includes('程序开始执行') || line.includes('程序执行完成')) {
                    formattedLines.push(''); // 空行
                    formattedLines.push(`🚀 ${line}`);
                    formattedLines.push(''); // 空行
                }
                // 重复执行相关
                else if (line.includes('开始重复执行') || line.includes('重复执行完成')) {
                    formattedLines.push(''); // 空行
                    formattedLines.push(`🔁 ${line}`);
                    formattedLines.push(''); // 空行
                }
                // 机器人动作
                else if (line.includes('机器人前进') || line.includes('机器人左转') || line.includes('机器人右转')) {
                    formattedLines.push(`  🤖 ${line}`);
                }
                // 动作完成
                else if (line.includes('动作完成') || line.includes('移动完成')) {
                    formattedLines.push(`  ✓ ${line}`);
                }
                // 其他输出
                else {
                    formattedLines.push(`  ${line}`);
                }
            }

            // 使用textContent和pre-wrap样式来保持换行格式
            const formattedText = formattedLines.join('\n');
            outputContent.textContent = formattedText;
            outputContent.scrollTop = outputContent.scrollHeight;
        }
    }

    /**
     * 完成输出弹窗显示
     */
    finalizeOutputModal(output) {
        const statusElement = document.getElementById('execution-status');
        if (statusElement) {
            statusElement.textContent = '执行完成';
            statusElement.style.color = '#4CAF50';
        }

        // 如果没有输出，显示提示信息
        if (!output || output.trim() === '') {
            this.updateOutputModal('程序执行完成，无控制台输出。\n精灵的移动和动作已在舞台区域显示。');
        }
    }

    /**
     * 解析 C 程序输出并执行相应的 Scratch 操作
     */
    parseAndExecuteCommands (output) {
        if (!this.props.vm) return;

        const lines = output.split('\n');
        const vm = this.props.vm;

        lines.forEach((line, index) => {
            line = line.trim();
            if (!line) return;

            console.log('解析命令:', line);

            // 添加延时，让动作有序执行
            setTimeout(() => {
                this.executeVMCommand(line, vm);
            }, index * 200); // 每个命令间隔200ms
        });
    }

    /**
     * 执行VM命令
     */
    executeVMCommand(line, vm) {
        try {
            // 获取当前活动的精灵（非舞台精灵）
            let target = vm.runtime.getEditingTarget();
            if (!target || target.isStage) {
                // 如果没有编辑目标或目标是舞台，则获取第一个非舞台精灵
                target = vm.runtime.targets.find(t => !t.isStage);
            }

            if (!target) {
                console.warn('没有找到可控制的精灵');
                return;
            }

            console.log('控制精灵:', target.sprite.name, '命令:', line);

            // 1. 处理说话命令
            if (line.startsWith('GPP_SAY:')) {
                const parts = line.split(':');
                if (parts.length >= 3) {
                    const mode = parseInt(parts[1]);
                    let text = parts.slice(2).join(':'); // 重新组合文本，防止文本中包含冒号
                    // 清理文本中的引号
                    text = text.replace(/['"]/g, '');

                    // 调用scratch-vm的say功能
                    target.say(text);
                    console.log(`精灵说话: ${text}`);

                    if (mode === 0) {
                        setTimeout(() => {
                            if (target) target.say(null);
                        }, 2000);
                    }

                    // 触发重绘
                    vm.runtime.requestRedraw();
                }
            }
            // 2. 处理移动命令
            else if (line.startsWith('FORWARD:')) {
                const parts = line.split(':');
                if (parts.length === 3) {
                    const speed = parseInt(parts[1]);
                    const distance = parseInt(parts[2]);

                    // 使用scratch-vm的移动功能
                    const moveDistance = distance * (speed / 4);
                    const direction = target.direction;
                    const radians = direction * Math.PI / 180;
                    const dx = moveDistance * Math.sin(radians);
                    const dy = moveDistance * Math.cos(radians);

                    target.setXY(target.x + dx, target.y + dy);
                    console.log(`精灵前进: 速度=${speed}, 距离=${distance}, 新位置=(${target.x.toFixed(2)}, ${target.y.toFixed(2)})`);

                    // 触发重绘
                    vm.runtime.requestRedraw();
                }
            } else if (line.startsWith('BACK:')) {
                const parts = line.split(':');
                if (parts.length === 3) {
                    const speed = parseInt(parts[1]);
                    const distance = parseInt(parts[2]);

                    // 使用scratch-vm的移动功能
                    const moveDistance = distance * (speed / 4);
                    const direction = target.direction;
                    const radians = direction * Math.PI / 180;
                    const dx = -moveDistance * Math.sin(radians);
                    const dy = -moveDistance * Math.cos(radians);

                    target.setXY(target.x + dx, target.y + dy);
                    console.log(`精灵后退: 速度=${speed}, 距离=${distance}, 新位置=(${target.x.toFixed(2)}, ${target.y.toFixed(2)})`);

                    // 触发重绘
                    vm.runtime.requestRedraw();
                }
            } else if (line.startsWith('TURN_LEFT:')) {
                const parts = line.split(':');
                if (parts.length === 2) {
                    const degrees = parseInt(parts[1]);

                    // 使用scratch-vm的转向功能
                    target.setDirection(target.direction - degrees);
                    console.log(`精灵左转: ${degrees}度, 新方向=${target.direction}`);

                    // 触发重绘
                    vm.runtime.requestRedraw();
                }
            } else if (line.startsWith('TURN_RIGHT:')) {
                const parts = line.split(':');
                if (parts.length === 2) {
                    const degrees = parseInt(parts[1]);

                    // 使用scratch-vm的转向功能
                    target.setDirection(target.direction + degrees);
                    console.log(`精灵右转: ${degrees}度, 新方向=${target.direction}`);

                    // 触发重绘
                    vm.runtime.requestRedraw();
                }
            }
            // 3. 处理机械手命令
            else if (line === 'SERVO_OPEN') {
                console.log('机械手开启');
                target.say('机械手开启');
                setTimeout(() => {
                    if (target) target.say(null);
                }, 1000);
                vm.runtime.requestRedraw();
            } else if (line === 'SERVO_CLOSE') {
                console.log('机械手关闭');
                target.say('机械手关闭');
                setTimeout(() => {
                    if (target) target.say(null);
                }, 1000);
                vm.runtime.requestRedraw();
            }
            // 4. 处理传感器命令
            else if (line === 'LIGHTSENSOR') {
                console.log('光线传感器读取，模拟值: 50');
            } else if (line === 'DISTSENSOR') {
                console.log('距离传感器读取，模拟值: 30cm');
            } else if (line === 'MIC1SENSOR') {
                console.log('噪声传感器读取，模拟值: 40');
            } else if (line.startsWith('TRACKER:')) {
                const parts = line.split(':');
                if (parts.length === 2) {
                    const id = parseInt(parts[1]);
                    console.log(`巡线传感器${id}读取，模拟值: 1`);
                }
            }
            // 5. 处理其他命令
            else if (line.startsWith('BEEP:')) {
                const parts = line.split(':');
                if (parts.length === 3) {
                    const bound = parseInt(parts[1]);
                    const time = parseInt(parts[2]);
                    console.log(`蜂鸣器: 频率=${bound}, 时间=${time}ms`);
                    target.say(`蜂鸣器响${time}ms`);
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                    vm.runtime.requestRedraw();
                }
            } else if (line.startsWith('LED:')) {
                const parts = line.split(':');
                if (parts.length === 3) {
                    const mode = parseInt(parts[1]);
                    const rgb = parseInt(parts[2]);
                    console.log(`LED灯: 模式=${mode}, 颜色=${rgb}`);
                    target.say(`LED模式${mode}`);
                    setTimeout(() => {
                        if (target) target.say(null);
                    }, 1000);
                    vm.runtime.requestRedraw();
                }
            }
        } catch (error) {
            console.error('执行VM命令失败:', line, error);
        }
    }



    /**
     * 触发绿旗动画（仅视觉效果，不执行积木块）
     */
    triggerGreenFlag() {
        try {
            // 只触发视觉动画，不执行积木块程序
            // 查找绿旗按钮并添加动画效果
            const greenFlagButton = document.querySelector('[class*="green-flag"]');
            if (greenFlagButton) {
                greenFlagButton.style.transform = 'scale(1.1)';
                greenFlagButton.style.transition = 'transform 0.2s';
                setTimeout(() => {
                    greenFlagButton.style.transform = 'scale(1)';
                }, 200);
                console.log('绿旗视觉动画已触发');
            } else {
                console.log('未找到绿旗按钮，跳过动画');
            }
        } catch (error) {
            console.error('触发绿旗动画失败:', error);
        }
    }

    render () {
        return (
            <CCodePanelComponent
                codeContent={this.state.codeContent}
                onRunClick={this.handleRunClick}
                runStatus={this.state.runStatus}
                runMessage={this.state.runMessage}
                isCompiling={this.state.isCompiling}
                isRunning={this.state.isRunning}
                picocLoaded={this.state.picocLoaded}
            />
        );
    }
}

CCodePanel.propTypes = {
    vm: PropTypes.instanceOf(VM)
};

const mapStateToProps = state => ({
    vm: state.scratchGui.vm
});

export default connect(
    mapStateToProps
)(CCodePanel);