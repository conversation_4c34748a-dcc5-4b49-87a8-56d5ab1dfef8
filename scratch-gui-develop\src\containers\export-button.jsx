import {connect} from 'react-redux';
import ExportButton from '../components/export-button/export-button.jsx';
import {showStandardAlert, closeAlertWithId} from '../reducers/alerts';

const mapStateToProps = state => ({
    vm: state.scratchGui.vm,
    projectName: state.scratchGui.projectTitle
});

const mapDispatchToProps = dispatch => ({
    onExportSuccess: () => {
        dispatch(showStandardAlert('exportSuccess'));
        setTimeout(() => {
            dispatch(closeAlertWithId('exportSuccess'));
        }, 5000); // 5秒后自动关闭
    },
    onExportError: () => {
        dispatch(showStandardAlert('exportError'));
        setTimeout(() => {
            dispatch(closeAlertWithId('exportError'));
        }, 5000); // 5秒后自动关闭
    }
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(ExportButton);
