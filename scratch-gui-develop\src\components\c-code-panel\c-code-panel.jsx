import PropTypes from 'prop-types';
import React from 'react';
import { FormattedMessage } from 'react-intl';
import Box from '../box/box.jsx';

import styles from './c-code-panel.css';

// C语言关键字列表
const C_KEYWORDS = [
    'auto', 'break', 'case', 'char', 'const', 'continue', 'default', 'do', 'double',
    'else', 'enum', 'extern', 'float', 'for', 'goto', 'if', 'int', 'long', 'register',
    'return', 'short', 'signed', 'sizeof', 'static', 'struct', 'switch', 'typedef',
    'union', 'unsigned', 'void', 'volatile', 'while'
];

// C语言常用库函数
const C_BUILTIN = [
    'printf', 'scanf', 'malloc', 'free', 'calloc', 'realloc', 'memset', 'memcpy',
    'strcpy', 'strcmp', 'strcat', 'strlen', 'fopen', 'fclose', 'fread', 'fwrite',
    'rand', 'srand', 'time', 'sleep'
];

// C语言类型和预处理器指令
const C_PREPROCESSOR = ['#include', '#define', '#ifdef', '#ifndef', '#endif', '#if', '#else', '#elif', '#pragma'];

/**
 * 高亮单行C代码
 * @param {string} line 一行C代码
 * @returns {Array} 带有高亮样式的React元素数组
 */
const highlightCLine = (line) => {
    if (!line) return [line];

    // 处理注释
    if (line.trim().startsWith('//')) {
        return [
            <span key="comment" className={styles.hljs_comment}>{line}</span>
        ];
    }

    // 简单的高亮处理，使用正则表达式匹配不同的代码元素
    let result = [];
    let currentIndex = 0;
    let currentKey = 0;

    // 提取部分
    const parts = [];

    // 处理字符串
    const stringPattern = /"([^"\\]|\\.)*"/g;
    let stringMatch;
    while ((stringMatch = stringPattern.exec(line)) !== null) {
        if (stringMatch.index > currentIndex) {
            parts.push({
                text: line.substring(currentIndex, stringMatch.index),
                type: 'normal',
                index: currentIndex
            });
        }
        parts.push({
            text: stringMatch[0],
            type: 'string',
            index: stringMatch.index
        });
        currentIndex = stringMatch.index + stringMatch[0].length;
    }

    // 添加剩余部分
    if (currentIndex < line.length) {
        parts.push({
            text: line.substring(currentIndex),
            type: 'normal',
            index: currentIndex
        });
    }

    // 处理普通文本部分
    parts.forEach(part => {
        if (part.type === 'string') {
            result.push(
                <span key={currentKey++} className={styles.hljs_string}>{part.text}</span>
            );
        } else {
            // 进一步分解普通文本
            const words = part.text.split(/(\s+|[;,(){}[\]<>=+\-*/%&|^!~?:])/);
            words.forEach(word => {
                if (C_KEYWORDS.includes(word)) {
                    result.push(
                        <span key={currentKey++} className={styles.hljs_keyword}>{word}</span>
                    );
                } else if (C_BUILTIN.includes(word)) {
                    result.push(
                        <span key={currentKey++} className={styles.hljs_builtin}>{word}</span>
                    );
                } else if (C_PREPROCESSOR.some(pp => word.startsWith(pp))) {
                    result.push(
                        <span key={currentKey++} className={styles.hljs_preprocessor}>{word}</span>
                    );
                } else if (/^-?\d+(\.\d+)?([eE][+-]?\d+)?$/.test(word)) {
                    result.push(
                        <span key={currentKey++} className={styles.hljs_number}>{word}</span>
                    );
                } else {
                    result.push(
                        <span key={currentKey++}>{word}</span>
                    );
                }
            });
        }
    });

    return result;
};

const CCodePanel = props => {
    const {
        codeContent,
        onRunClick,
        runStatus,
        runMessage
    } = props;

    // 将代码内容按行分割并添加行号
    const codeLines = codeContent.split('\n');
    const codeWithLineNumbers = codeLines.map((line, index) => (
        <div key={index} className={styles.codeLine}>
            <span className={styles.lineNumber}>{index + 1}</span>
            <span className={styles.lineContent}>
                {highlightCLine(line)}
            </span>
        </div>
    ));

    return (
        <Box className={styles.cCodePanelWrapper}>
            <Box className={styles.cCodePanelHeader}>
                <FormattedMessage
                    defaultMessage="C代码"
                    description="标题：C代码面板"
                    id="gui.cCodePanel.title"
                />
            </Box>
            <Box className={styles.cCodePanelContent}>
                <div className={styles.codeButtonsContainer}>
                    <button
                        className={`${styles.codeButton} ${styles.runButton}`}
                        onClick={onRunClick}
                    >
                        <FormattedMessage
                            defaultMessage="运行"
                            description="运行C代码按钮"
                            id="gui.cCodePanel.run"
                        />
                    </button>
                </div>

                {runStatus && (
                    <div className={`${styles.messageContainer} ${runStatus === 'success' ? styles.successMessage : styles.errorMessage}`}>
                        {runMessage}
                    </div>
                )}

                <div className={styles.codeInfo}>
                    <FormattedMessage
                        defaultMessage="积木块转换的C代码"
                        description="C代码面板描述"
                        id="gui.cCodePanel.description"
                    />
                </div>
                <div className={styles.codeDisplayWrapper}>
                    <pre className={styles.codeDisplay}>
                        <code className={styles.codeContainer}>
                            {codeWithLineNumbers}
                        </code>
                    </pre>
                </div>
            </Box>
        </Box>
    );
};

CCodePanel.propTypes = {
    codeContent: PropTypes.string,
    onRunClick: PropTypes.func,
    runStatus: PropTypes.oneOf(['success', 'error', null]),
    runMessage: PropTypes.string
};

CCodePanel.defaultProps = {
    codeContent: '// C代码将在这里显示\n// 拖动积木块到工作区来生成代码',
    onRunClick: () => {},
    runStatus: null,
    runMessage: ''
};

export default CCodePanel;