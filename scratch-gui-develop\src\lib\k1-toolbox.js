/**
 * K1机器人自定义积木块工具箱XML
 */

import ScratchBlocks from 'scratch-blocks';

/**
 * 生成K1机器人自定义积木块的工具箱XML
 * @returns {string} 工具箱XML字符串
 */
const getK1ToolboxXML = () => {
    const categorySeparator = '<sep gap="36"/>';

    // K1机器人控制类积木块
    const k1ControlXML = `
    <category name="K1控制" id="k1control" colour="#4C97FF" secondaryColour="#2855A8">
        <block type="k1_forward">
            <value name="SPEED">
                <shadow type="math_number">
                    <field name="NUM">4</field>
                </shadow>
            </value>
            <value name="DISTANCE">
                <shadow type="math_number">
                    <field name="NUM">10</field>
                </shadow>
            </value>
        </block>
        <block type="k1_back">
            <value name="SPEED">
                <shadow type="math_number">
                    <field name="NUM">4</field>
                </shadow>
            </value>
            <value name="DISTANCE">
                <shadow type="math_number">
                    <field name="NUM">10</field>
                </shadow>
            </value>
        </block>
        <block type="k1_turn_left">
            <value name="DEGREE">
                <shadow type="math_number">
                    <field name="NUM">90</field>
                </shadow>
            </value>
        </block>
        <block type="k1_turn_right">
            <value name="DEGREE">
                <shadow type="math_number">
                    <field name="NUM">90</field>
                </shadow>
            </value>
        </block>
        <block type="k1_gpp_say">
            <value name="MODE">
                <shadow type="math_number">
                    <field name="NUM">1</field>
                </shadow>
            </value>
            <value name="TEXT">
                <shadow type="text">
                    <field name="TEXT">你好！</field>
                </shadow>
            </value>
        </block>
        <block type="k1_servo_open"></block>
        <block type="k1_servo_close"></block>
        <block type="k1_tracker_start"></block>
        <block type="k1_tracker_close"></block>
        <block type="k1_beep">
            <value name="BOUND">
                <shadow type="math_number">
                    <field name="NUM">1000</field>
                </shadow>
            </value>
            <value name="TIME">
                <shadow type="math_number">
                    <field name="NUM">500</field>
                </shadow>
            </value>
        </block>
        <block type="k1_colorful_led">
            <value name="MODE">
                <shadow type="math_number">
                    <field name="NUM">3</field>
                </shadow>
            </value>
            <value name="RGB">
                <shadow type="math_number">
                    <field name="NUM">1</field>
                </shadow>
            </value>
        </block>
        <block type="k1_set_cscript_mode">
            <value name="MODE">
                <shadow type="math_number">
                    <field name="NUM">2</field>
                </shadow>
            </value>
        </block>
        <block type="k1_cexit"></block>
    </category>
    `;

    // K1机器人传感器类积木块
    const k1SensorXML = `
    <category name="K1传感器" id="k1sensor" colour="#5CB1D6" secondaryColour="#3D8EB5">
        <!-- 命令块版本 - 可以在序列中使用 -->
        <block type="k1_lightsensor"></block>
        <block type="k1_distsensor"></block>
        <block type="k1_mic1sensor"></block>
        <block type="k1_tracker">
            <value name="ID">
                <shadow type="math_number">
                    <field name="NUM">1</field>
                </shadow>
            </value>
        </block>
        <block type="k1_get_ps2value"></block>
    </category>
    `;

    return {
        k1ControlXML,
        k1SensorXML
    };
};

export default getK1ToolboxXML;
