package com.zxy.scratchserver.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 项目响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectResponse {
    private Long projectId;
    private Long userId;
    private String projectName;
    private LocalDateTime projectCreatedAt;
    private String username; // 项目创建者用户名
    private String versionNumber; // 项目版本号
}
