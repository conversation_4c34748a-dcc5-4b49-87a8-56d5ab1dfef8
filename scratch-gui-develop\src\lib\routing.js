/**
 * 路由系统
 * 处理页面跳转和路由保护
 */

import { isAuthenticated } from './auth-service';

// 登录页面路径
const LOGIN_PATH = '/login';

/**
 * 检查当前路径是否是登录页面
 * @returns {boolean} - 是否是登录页面
 */
const isLoginPage = () => {
    // 检查路径是否是登录路径或者文件名是login.html
    const path = window.location.pathname;
    const isLogin = path === LOGIN_PATH || 
                   path.endsWith('/login.html') || 
                   path.includes('/login');
    
    console.log('当前页面路径检查:', path, '是否是登录页面:', isLogin);
    return isLogin;
};

/**
 * 检查用户是否有权限访问当前页面
 * 如果未登录且不在登录页面，则重定向到登录页面
 * @returns {boolean} - 是否有权限访问
 */
const checkAccess = () => {
    // 如果用户已登录，允许访问任何页面
    if (isAuthenticated()) {
        return true;
    }

    // 如果未登录但在登录页面，允许访问
    if (isLoginPage()) {
        return true;
    }

    // 未登录且不在登录页面，重定向到登录页面
    redirectToLogin();
    return false;
};

/**
 * 重定向到登录页面
 */
const redirectToLogin = () => {
    // 使用login.html而不是/login路径，因为我们使用的是文件系统路由而不是服务器路由
    window.location.href = 'login.html';
};

/**
 * 重定向到主页
 * @param {boolean} forceReload - 是否强制重新加载主页
 */
const redirectToHome = (forceReload = false) => {
    console.log('重定向到主页...');
    // 使用replace避免浏览器历史记录问题
    if (forceReload) {
        // 强制重新加载页面
        window.location.replace('index.html');
    } else {
        // 如果已经在主页，则刷新页面
        if (window.location.pathname.endsWith('/index.html') || 
            window.location.pathname === '/' || 
            window.location.pathname === '') {
            window.location.reload();
        } else {
            window.location.replace('index.html');
        }
    }
};

export {
    LOGIN_PATH,
    isLoginPage,
    checkAccess,
    redirectToLogin,
    redirectToHome
};
