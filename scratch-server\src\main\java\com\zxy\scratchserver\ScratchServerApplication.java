package com.zxy.scratchserver;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@SpringBootApplication
public class ScratchServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(ScratchServerApplication.class, args);
    }

    /**
     * 配置Web MVC，确保API请求正确路由
     */
    @Bean
    public WebMvcConfigurer webMvcConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void configurePathMatch(PathMatchConfigurer configurer) {
                // 确保所有API请求都被正确路由到控制器
                configurer.setUseTrailingSlashMatch(true);
                configurer.setUseRegisteredSuffixPatternMatch(true);
            }
        };
    }
}
