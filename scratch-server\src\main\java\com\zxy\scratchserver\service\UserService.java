package com.zxy.scratchserver.service;

import com.zxy.scratchserver.dto.AuthResponse;
import com.zxy.scratchserver.dto.LoginRequest;
import com.zxy.scratchserver.dto.RegisterRequest;
import com.zxy.scratchserver.dto.UpdatePasswordRequest;
import com.zxy.scratchserver.dto.UpdateUserRequest;
import com.zxy.scratchserver.dto.UserResponse;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 认证响应
     */
    AuthResponse login(LoginRequest loginRequest);

    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 认证响应
     */
    AuthResponse register(RegisterRequest registerRequest);

    /**
     * 获取当前用户信息
     * @param username 用户名
     * @return 用户响应
     */
    UserResponse getCurrentUser(String username);

    /**
     * 更新用户信息
     * @param username 用户名
     * @param updateUserRequest 更新用户信息请求
     * @return 更新后的用户信息
     */
    UserResponse updateUser(String username, UpdateUserRequest updateUserRequest);

    /**
     * 修改密码
     * @param username 用户名
     * @param updatePasswordRequest 修改密码请求
     * @return 是否修改成功
     */
    boolean updatePassword(String username, UpdatePasswordRequest updatePasswordRequest);
}
