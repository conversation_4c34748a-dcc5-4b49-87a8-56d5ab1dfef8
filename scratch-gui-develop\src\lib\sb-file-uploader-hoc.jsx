import bindAll from 'lodash.bindall';
import React from 'react';
import PropTypes from 'prop-types';
import {defineMessages, intlShape, injectIntl} from 'react-intl';
import {connect} from 'react-redux';
import log from '../lib/log';
import sharedMessages from './shared-messages';

import {
    LoadingStates,
    getIsLoadingUpload,
    getIsShowingWithoutId,
    onLoadedProject,
    requestProjectUpload
} from '../reducers/project-state';
import {setProjectTitle} from '../reducers/project-title';
import {
    openLoadingProject,
    closeLoadingProject
} from '../reducers/modals';
import {
    closeFileMenu
} from '../reducers/menus';

const messages = defineMessages({
    loadError: {
        id: 'gui.projectLoader.loadError',
        defaultMessage: 'The project file that was selected failed to load.',
        description: 'An error that displays when a local project file fails to load.'
    },
    corruptedError: {
        id: 'gui.projectLoader.corruptedError',
        defaultMessage: 'The project file is corrupted.',
        description: 'An error that displays when a project file is corrupted.'
    },
    invalidFileFormat: {
        id: 'gui.projectLoader.invalidFileFormat',
        defaultMessage: 'The project file format is invalid.',
        description: 'An error that displays when a project file format is invalid.'
    }
});

/**
 * Higher Order Component to provide behavior for loading local project files into editor.
 * @param {React.Component} WrappedComponent the component to add project file loading functionality to
 * @returns {React.Component} WrappedComponent with project file loading functionality added
 *
 * <SBFileUploaderHOC>
 *     <WrappedComponent />
 * </SBFileUploaderHOC>
 */
const SBFileUploaderHOC = function (WrappedComponent) {
    class SBFileUploaderComponent extends React.Component {
        constructor (props) {
            super(props);
            bindAll(this, [
                'createFileObjects',
                'getProjectTitleFromFilename',
                'handleFinishedLoadingUpload',
                'handleStartSelectingFileUpload',
                'handleChange',
                'onload',
                'removeFileObjects',
                'handleFileUpload',
                'preprocessProjectData'
            ]);
        }
        componentDidUpdate (prevProps) {
            if (this.props.isLoadingUpload && !prevProps.isLoadingUpload) {
                this.handleFinishedLoadingUpload(); // cue step 5 below
            }
        }
        componentWillUnmount () {
            this.removeFileObjects();
        }
        // step 1: this is where the upload process begins
        handleStartSelectingFileUpload () {
            this.createFileObjects(); // go to step 2
        }
        // step 2: create a FileReader and an <input> element, and issue a
        // pseudo-click to it. That will open the file chooser dialog.
        createFileObjects () {
            // redo step 7, in case it got skipped last time and its objects are
            // still in memory
            this.removeFileObjects();
            // create fileReader
            this.fileReader = new FileReader();
            this.fileReader.onload = this.onload;
            // create <input> element and add it to DOM
            this.inputElement = document.createElement('input');
            this.inputElement.accept = '.sb,.sb2,.sb3';
            this.inputElement.style = 'display: none;';
            this.inputElement.type = 'file';
            this.inputElement.onchange = this.handleChange; // connects to step 3
            document.body.appendChild(this.inputElement);
            // simulate a click to open file chooser dialog
            this.inputElement.click();
        }
        // step 3: user has picked a file using the file chooser dialog.
        // We don't actually load the file here, we only decide whether to do so.
        handleChange (e) {
            const {
                intl,
                isShowingWithoutId,
                loadingState,
                projectChanged,
                userOwnsProject
            } = this.props;
            const thisFileInput = e.target;
            if (thisFileInput.files) { // Don't attempt to load if no file was selected
                this.fileToUpload = thisFileInput.files[0];

                // If user owns the project, or user has changed the project,
                // we must confirm with the user that they really intend to
                // replace it. (If they don't own the project and haven't
                // changed it, no need to confirm.)
                let uploadAllowed = true;
                if (userOwnsProject || (projectChanged && isShowingWithoutId)) {
                    uploadAllowed = confirm( // eslint-disable-line no-alert
                        intl.formatMessage(sharedMessages.replaceProjectWarning)
                    );
                }
                if (uploadAllowed) {
                    // cues step 4
                    this.props.requestProjectUpload(loadingState);
                } else {
                    // skips ahead to step 7
                    this.removeFileObjects();
                }
                this.props.closeFileMenu();
            }
        }
        // step 4 is below, in mapDispatchToProps

        // step 5: called from componentDidUpdate when project state shows
        // that project data has finished "uploading" into the browser
        handleFinishedLoadingUpload () {
            if (this.fileToUpload && this.fileReader) {
                // begin to read data from the file. When finished,
                // cues step 6 using the reader's onload callback
                this.fileReader.readAsArrayBuffer(this.fileToUpload);
            } else {
                this.props.cancelFileUpload(this.props.loadingState);
                // skip ahead to step 7
                this.removeFileObjects();
            }
        }
        // used in step 6 below
        getProjectTitleFromFilename (fileInputFilename) {
            if (!fileInputFilename) return '';
            // only parse title with valid scratch project extensions
            // (.sb, .sb2, and .sb3)
            const matches = fileInputFilename.match(/^(.*)\.sb[23]?$/);
            if (!matches) return '';
            return matches[1].substring(0, 100); // truncate project title to max 100 chars
        }
        // step 6: attached as a handler on our FileReader object; called when
        // file upload raw data is available in the reader
        onload () {
            if (this.fileReader) {
                this.props.onLoadingStarted();
                const filename = this.fileToUpload && this.fileToUpload.name;
                let loadingSuccess = false;
                this.props.vm.loadProject(this.fileReader.result)
                    .then(() => {
                        if (filename) {
                            const uploadedProjectTitle = this.getProjectTitleFromFilename(filename);
                            this.props.onSetProjectTitle(uploadedProjectTitle);
                        }
                        loadingSuccess = true;
                    })
                    .catch(error => {
                        log.warn(error);
                        alert(this.props.intl.formatMessage(messages.loadError)); // eslint-disable-line no-alert
                    })
                    .then(() => {
                        this.props.onLoadingFinished(this.props.loadingState, loadingSuccess);
                        // go back to step 7: whether project loading succeeded
                        // or failed, reset file objects
                        this.removeFileObjects();
                    });
            }
        }
        // step 7: remove the <input> element from the DOM and clear reader and
        // fileToUpload reference, so those objects can be garbage collected
        removeFileObjects () {
            if (this.inputElement) {
                this.inputElement.value = null;
                document.body.removeChild(this.inputElement);
            }
            this.inputElement = null;
            this.fileReader = null;
            this.fileToUpload = null;
        }
        handleFileUpload (e) {
            const {
                extension,
                onError,
                onLoad
            } = this.props;
            
            // 隐藏元素
            this.fileInput.value = null;
            
            // 如果更改事件是通过单击"浏览..."触发的，则handle只有一个目标文件
            const thisFile = e.target.files[0];
            let fileType = null; // 最初未知
            
            const reader = new FileReader();
            
            // 请注意：File.name在IE中很容易被编程更改，
            // 因此我们首先按文件类型进行验证，然后按扩展名
            reader.onload = () => {
                let projectData = reader.result;
                
                // 预处理项目数据，移除K1扩展依赖
                if (extension === 'sb3' && projectData instanceof ArrayBuffer) {
                    try {
                        // 在加载项目之前，尝试预处理数据以移除对K1扩展的依赖
                        projectData = this.preprocessProjectData(projectData);
                        console.log('项目数据预处理完成');
                    } catch (err) {
                        console.error('预处理项目数据失败:', err);
                        // 继续使用原始数据
                    }
                }
                
                if (fileType === 'sb') {
                    this.props.vm.loadProject(projectData)
                        .then(() => {
                            if (onLoad) onLoad();
                        })
                        .catch(error => {
                            // 由于历史原因，sb标题块信息与未压缩的项目数据相邻，
                            // 我们期望在第一个单元中的某个位置有"ScratchV"
                            if (projectData[0] === 60 && error.message === 'Failed to parse') {
                                const originalError = error;
                                error = {
                                    message: this.props.intl.formatMessage(messages.corruptedError)
                                };
                                error.originalError = originalError;
                            }
                            if (onError) onError(error);
                        });
                } else if (fileType === 'sb2' || fileType === 'sb3' || extension === 'sb2' || extension === 'sb3') {
                    this.props.vm.loadProject(projectData)
                        .then(() => {
                            if (onLoad) onLoad();
                        })
                        .catch(error => {
                            if (onError) onError(error);
                        });
                } else {
                    const errorMessage = this.props.intl.formatMessage(messages.invalidFileFormat);
                    if (onError) onError({message: errorMessage});
                }
            };
            
            // 允许的文件扩展名：.sb .sb2 .sb3
            reader.onloadstart = () => {
                // 如果我们已经知道文件扩展名，则不需要检查
                if (extension) return;
                
                const signature = []; // 每个文件类型的签名
                
                const handleData = data => {
                    const byteArray = new Uint8Array(data.target.result);
                    let i = 0;
                    while (i < byteArray.length) {
                        signature[i] = byteArray[i];
                        i++;
                        if (i === 4) break;
                    }
                    
                    // 具有.sb扩展名的2.0文件格式
                    // 确保前两个字节是'SQ'
                    if (signature[0] === 83 && signature[1] === 81 && signature[2] === 0 && signature[3] === 0) {
                        fileType = 'sb';
                    } else if (signature[0] === 80 && signature[1] === 75 && signature[2] === 3 && signature[3] === 4) {
                        // 具有.sb2 或.sb3扩展名的3.0文件格式
                        if (extension === 'sb3') {
                            fileType = 'sb3';
                        } else {
                            fileType = 'sb2';
                        }
                    }
                };
                
                // 从文件中读取前几个字节
                const slice = thisFile.slice(0, 4);
                const fileReader = new FileReader();
                fileReader.onload = handleData;
                fileReader.readAsArrayBuffer(slice);
            };
            
            if (thisFile) {
                reader.readAsArrayBuffer(thisFile);
            }
        }
        /**
         * 预处理项目数据，移除对K1扩展的依赖
         * @param {ArrayBuffer} projectData - 项目数据
         * @returns {ArrayBuffer} 处理后的项目数据
         */
        preprocessProjectData(projectData) {
            if (!(projectData instanceof ArrayBuffer)) {
                return projectData;
            }
            
            console.log('开始预处理项目数据...');
            
            try {
                // 将项目数据转换为文本，查找K1扩展引用
                const uint8Array = new Uint8Array(projectData);
                
                // 检查是否是ZIP文件（.sb3格式）
                if (uint8Array.length > 2 && uint8Array[0] === 80 && uint8Array[1] === 75) {
                    console.log('检测到.sb3格式项目');
                    
                    // 这里我们无法直接修改ZIP文件内容，但可以注入VM运行时处理
                    // 我们在K1-vm-extension.js中已经实现了这一功能
                    
                    return projectData;
                }
            } catch (e) {
                console.error('预处理项目数据失败:', e);
            }
            
            return projectData;
        }
        render () {
            const {
                /* eslint-disable no-unused-vars */
                cancelFileUpload,
                closeFileMenu: closeFileMenuProp,
                isLoadingUpload,
                isShowingWithoutId,
                loadingState,
                onLoadingFinished,
                onLoadingStarted,
                onSetProjectTitle,
                projectChanged,
                requestProjectUpload: requestProjectUploadProp,
                userOwnsProject,
                /* eslint-enable no-unused-vars */
                ...componentProps
            } = this.props;
            return (
                <React.Fragment>
                    <WrappedComponent
                        onStartSelectingFileUpload={this.handleStartSelectingFileUpload}
                        {...componentProps}
                    />
                </React.Fragment>
            );
        }
    }

    SBFileUploaderComponent.propTypes = {
        canSave: PropTypes.bool,
        cancelFileUpload: PropTypes.func,
        closeFileMenu: PropTypes.func,
        intl: intlShape.isRequired,
        isLoadingUpload: PropTypes.bool,
        isShowingWithoutId: PropTypes.bool,
        loadingState: PropTypes.oneOf(LoadingStates),
        onLoadingFinished: PropTypes.func,
        onLoadingStarted: PropTypes.func,
        onSetProjectTitle: PropTypes.func,
        projectChanged: PropTypes.bool,
        requestProjectUpload: PropTypes.func,
        userOwnsProject: PropTypes.bool,
        vm: PropTypes.shape({
            loadProject: PropTypes.func
        })
    };
    const mapStateToProps = (state, ownProps) => {
        const loadingState = state.scratchGui.projectState.loadingState;
        const user = state.session && state.session.session && state.session.session.user;
        return {
            isLoadingUpload: getIsLoadingUpload(loadingState),
            isShowingWithoutId: getIsShowingWithoutId(loadingState),
            loadingState: loadingState,
            projectChanged: state.scratchGui.projectChanged,
            userOwnsProject: ownProps.authorUsername && user &&
                (ownProps.authorUsername === user.username),
            vm: state.scratchGui.vm
        };
    };
    const mapDispatchToProps = (dispatch, ownProps) => ({
        cancelFileUpload: loadingState => dispatch(onLoadedProject(loadingState, false, false)),
        closeFileMenu: () => dispatch(closeFileMenu()),
        // transition project state from loading to regular, and close
        // loading screen and file menu
        onLoadingFinished: (loadingState, success) => {
            dispatch(onLoadedProject(loadingState, ownProps.canSave, success));
            dispatch(closeLoadingProject());
            dispatch(closeFileMenu());
        },
        // show project loading screen
        onLoadingStarted: () => dispatch(openLoadingProject()),
        onSetProjectTitle: title => dispatch(setProjectTitle(title)),
        // step 4: transition the project state so we're ready to handle the new
        // project data. When this is done, the project state transition will be
        // noticed by componentDidUpdate()
        requestProjectUpload: loadingState => dispatch(requestProjectUpload(loadingState))
    });
    // Allow incoming props to override redux-provided props. Used to mock in tests.
    const mergeProps = (stateProps, dispatchProps, ownProps) => Object.assign(
        {}, stateProps, dispatchProps, ownProps
    );
    return injectIntl(connect(
        mapStateToProps,
        mapDispatchToProps,
        mergeProps
    )(SBFileUploaderComponent));
};

export {
    SBFileUploaderHOC as default
};
