import {connect} from 'react-redux';
import React from 'react';
import PropTypes from 'prop-types';

import {
    handleLogin,
    handleRegister,
    clearError
} from '../reducers/user';

import LoginModalComponent from '../components/login/login-modal.jsx';

class LoginModal extends React.Component {
    constructor (props) {
        super(props);
        this.handleLogin = this.handleLogin.bind(this);
        this.handleRegister = this.handleRegister.bind(this);
        this.handleClose = this.handleClose.bind(this);
    }

    handleLogin (username, password) {
        this.props.onLogin(username, password);
    }

    handleRegister (username, password) {
        this.props.onRegister(username, password);
    }

    handleClose () {
        this.props.onClearError();
        this.props.onClose();
    }

    render () {
        const {
            error,
            isOpen
        } = this.props;

        return (
            <LoginModalComponent
                error={error}
                isOpen={isOpen}
                onClose={this.handleClose}
                onLogin={this.handleLogin}
                onRegister={this.handleRegister}
            />
        );
    }
}

LoginModal.propTypes = {
    error: PropTypes.string,
    isOpen: PropTypes.bool.isRequired,
    onClearError: PropTypes.func.isRequired,
    onClose: PropTypes.func.isRequired,
    onLogin: PropTypes.func.isRequired,
    onRegister: PropTypes.func.isRequired
};

const mapStateToProps = state => ({
    error: state.user.error
});

const mapDispatchToProps = dispatch => ({
    onLogin: (username, password) => dispatch(handleLogin(username, password)),
    onRegister: (username, password) => dispatch(handleRegister(username, password)),
    onClearError: () => dispatch(clearError())
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(LoginModal);
