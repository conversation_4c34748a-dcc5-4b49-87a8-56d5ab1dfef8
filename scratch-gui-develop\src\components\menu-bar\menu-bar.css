@import "../../css/colors.css";
@import "../../css/units.css";
@import "../../css/z-index.css";

.menu-bar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: nowrap;

    /*
        For most things, we shouldn't explicitly set height, and let the
        content push the element to whatever fits. Using a fixed height
        instead, will help us subtract the value we assign from the body,
        adding up to a perfect 100%. This means we don't need to set
        overflow: hidden, which makes it hard to debug. border-box
        simplifies by all of this by removing padding from the equation.
    */
    box-sizing: border-box;
    height: $menu-bar-height;

    /*
        @todo: This adds ~20px in Chrome, when scrolling to the right,
        but fixes [FFx + Safari] [resize window down + scroll to the right] bug.
        width: 100%;
    */
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: $menu-bar-standard-font-size;
    font-weight: bold;
    background-color: $looks-secondary;
    color: $ui-white;
}

.main-menu {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: nowrap;
    align-items: center;
    flex-grow: 1;
}
.inactive {
    opacity: 0;
}
.scratch-logo {
    height:  1.6rem;
    vertical-align: middle;
}

.scratch-logo.clickable {
    cursor: pointer;
}

.menu-bar-item {
    display: flex;
    padding: 0 0.25rem;
    text-decoration: none;
    color: $ui-white;
    user-select: none;
    align-self: center;
    position: relative;
    align-items: center;
    white-space: nowrap;
    height: $menu-bar-height;
}

.menu-bar-item.hoverable {
    cursor: pointer;
}

.menu-bar-item.active,
.menu-bar-item.hoverable:hover {
    background-color: $ui-black-transparent;
}

.menu-bar-item.growable {
    max-width: $menu-bar-item-max-width;
    flex: 1;
}

.title-field-growable {
    flex-grow: 1;
    width: 2rem;
}

.file-group {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.file-group .menu-bar-item {
    margin: 0 .25rem;
    padding: 0 0.75rem;
}

.file-group .no-offset {
    margin: 0 .25rem;
    padding: 0 0.25rem;
}

.menu-bar-menu {
    margin-top: $menu-bar-height;
    z-index: $z-index-menu-bar;
}

.feedback-link {
    color: $looks-secondary;
    text-decoration: none;
}

.feedback-button {
    background-color: $ui-white;
    height: 34px;
}

.divider {
    margin: 0 .5rem;
    height: 34px;
}

.author-info {
    margin-left: .25rem;
    margin-right: .6875rem;
}

.menu-bar-button {
    height: $menu-bar-button-size;
}

.remix-button {
    background-color: $pen-primary
}

.remix-button-icon {
    height: 1.25rem;
}

.coming-soon >:not(.coming-soon-tooltip) {
    opacity: 0.5;
}

.account-info-group {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.account-info-group .menu-bar-item {
    margin: 0 .25rem;
    padding: 0 0.75rem;
}

.mystuff-icon {
    margin: 0 .25rem;
    height: 1rem;
}

.help-icon {
    margin: 0 .25rem 0 0;
}

[dir="rtl"] .help-icon {
    margin: 0 0 0 .25rem;
}

.account-nav-menu, .mystuff-button {
    padding: 0 .25rem;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.profile-icon {
    margin: 0 .25rem;
    width: $menu-bar-button-size;
    border-radius: $form-radius;
}

.dropdown-caret-icon {
    width: 0.5rem;
    height: 0.5rem;
}

[dir="ltr"] .dropdown-caret-icon {
    margin-left: .5rem;
}

[dir="rtl"] .dropdown-caret-icon {
    margin-right: .5rem;
}

.disabled {
    opacity: 0.5;
}

.mystuff > a {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 45%;
  padding-right: 10px;
  padding-left: 10px;
  width: 30px;
  overflow: hidden;
  text-indent: 50px;
  white-space: nowrap;
}
.mystuff > a:hover {
  background-size: 50%;
}

.mystuff > a {
  background-image: url("./icon--mystuff.png");
}

.about-icon {
    height: 1.25rem;
    margin: 0.5rem;
    vertical-align: middle;
}

.collapsible-label {
    margin: 0 .5rem 0 .25rem;
}

[dir="rtl"] .collapsible-label {
    margin: 0 0.25rem 0 0.5rem;
}

@media only screen and (max-width: 1024px) {
    .tutorials-label, .collapsible-label, .debug-label{
        display: none;
    }

    .help-icon {
        margin-right: 0;
    }
}
