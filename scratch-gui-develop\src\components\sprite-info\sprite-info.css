@import "../../css/units.css";
@import "../../css/colors.css";

.sprite-info {
    padding: 0.75rem;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    background-color: white;
    color: $text-primary;
    border-top-left-radius: $space;
    border-top-right-radius: $space;
    border-bottom: 1px solid $ui-black-transparent;
}

.row {
    display: flex;
    justify-content: space-between;
}

.row-primary {
    margin-bottom: 0.5rem;
}

.label { opacity: 0.8; }

.group {
    display: inline-flex;
    flex-direction: row; /* makes columns, for each label/form group */
    align-items: center;
}

.column {
    display: inline-flex;
    flex-direction: column; /* put label above input */
    align-items: flex-start;
}

.column span {
    margin-bottom: .25rem;
}

.icon-wrapper {
    display: inline-block;
    width: calc(2rem + 2px);
    height: calc(2rem + 2px);
    padding: 0.5rem;
    outline: none;
    user-select: none;
}

.icon {
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.rotation-select {
    width: 100%;
    height: 1.85rem;
    border: 1px solid $ui-black-transparent;
    user-select: none;
    outline: none;
}

.rotation-select:focus {
    border-color: $looks-secondary;
    box-shadow: inset 0 0 0 1px $ui-black-transparent);
}

.larger-input input {
    width: 4rem;
}

.sprite-input {
    width: 8rem;
}
