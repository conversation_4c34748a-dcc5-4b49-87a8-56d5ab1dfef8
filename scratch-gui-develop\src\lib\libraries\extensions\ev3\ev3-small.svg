<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 50.2 (55047) - http://www.bohemiancoding.com/sketch -->
    <title>ev3-block-icon</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="ev3-block-icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="ev3" transform="translate(5.500000, 3.500000)" fill-rule="nonzero">
            <rect id="Rectangle-path" stroke="#7C87A5" fill="#FFFFFF" stroke-linecap="round" stroke-linejoin="round" x="0.5" y="3.59" width="28" height="25.81" rx="1"></rect>
            <rect id="Rectangle-path" stroke="#7C87A5" fill="#E6E7E8" stroke-linecap="round" stroke-linejoin="round" x="2.5" y="0.5" width="24" height="32" rx="1"></rect>
            <rect id="Rectangle-path" stroke="#7C87A5" fill="#FFFFFF" stroke-linecap="round" stroke-linejoin="round" x="2.5" y="14.5" width="24" height="13"></rect>
            <path d="M14.5,10.5 L14.5,14.5" id="Shape" stroke="#7C87A5" fill="#E6E7E8" stroke-linecap="round" stroke-linejoin="round"></path>
            <rect id="Rectangle-path" fill="#414757" x="4.5" y="2.5" width="20" height="10" rx="1"></rect>
            <rect id="Rectangle-path" fill="#7C87A5" opacity="0.5" x="13.5" y="20.13" width="2" height="2" rx="0.5"></rect>
            <path d="M9.06,20.13 L10.56,20.13 C10.8361424,20.13 11.06,20.3538576 11.06,20.63 L11.06,21.63 C11.06,21.9061424 10.8361424,22.13 10.56,22.13 L9.06,22.13 C8.50771525,22.13 8.06,21.6822847 8.06,21.13 C8.06,20.5777153 8.50771525,20.13 9.06,20.13 Z" id="Shape" fill="#7C87A5" opacity="0.5"></path>
            <path d="M18.91,20.13 L20.42,20.13 C20.6961424,20.13 20.92,20.3538576 20.92,20.63 L20.92,21.63 C20.92,21.9061424 20.6961424,22.13 20.42,22.13 L18.92,22.13 C18.3677153,22.13 17.92,21.6822847 17.92,21.13 C17.9199726,20.581597 18.3616245,20.135484 18.91,20.13 Z" id="Shape" fill="#7C87A5" opacity="0.5" transform="translate(19.420000, 21.130000) rotate(-180.000000) translate(-19.420000, -21.130000) "></path>
            <path d="M8.23,17.5 L5,17.5 C4.72385763,17.5 4.5,17.2761424 4.5,17 L4.5,14.5 L10.5,14.5 L8.65,17.28 C8.55466961,17.4179082 8.39765006,17.5001566 8.23,17.5 Z" id="Shape" fill="#7C87A5" opacity="0.5"></path>
            <path d="M18.15,18.85 L17.65,19.35 C17.5523416,19.4440756 17.4980339,19.5744142 17.5,19.71 L17.5,20 C17.5,20.2761424 17.2761424,20.5 17,20.5 L16.5,20.5 C16.2238576,20.5 16,20.2761424 16,20 C16,19.7238576 15.7761424,19.5 15.5,19.5 L13.5,19.5 C13.2238576,19.5 13,19.7238576 13,20 C13,20.2761424 12.7761424,20.5 12.5,20.5 L12,20.5 C11.7238576,20.5 11.5,20.2761424 11.5,20 L11.5,19.71 C11.5019661,19.5744142 11.4476584,19.4440756 11.35,19.35 L10.85,18.85 C10.6582167,18.6521863 10.6582167,18.3378137 10.85,18.14 L12.36,16.65 C12.4502803,16.5528617 12.5773961,16.4983835 12.71,16.5 L16.29,16.5 C16.4226039,16.4983835 16.5497197,16.5528617 16.64,16.65 L18.15,18.14 C18.3417833,18.3378137 18.3417833,18.6521863 18.15,18.85 Z" id="Shape" fill="#7C87A5" opacity="0.5"></path>
            <path d="M10.85,23.45 L11.35,22.95 C11.4476584,22.8559244 11.5019661,22.7255858 11.5,22.59 L11.5,22.3 C11.5,22.0238576 11.7238576,21.8 12,21.8 L12.5,21.8 C12.7761424,21.8 13,22.0238576 13,22.3 C13,22.5761424 13.2238576,22.8 13.5,22.8 L15.5,22.8 C15.7761424,22.8 16,22.5761424 16,22.3 C16,22.0238576 16.2238576,21.8 16.5,21.8 L17,21.8 C17.2761424,21.8 17.5,22.0238576 17.5,22.3 L17.5,22.59 C17.4980339,22.7255858 17.5523416,22.8559244 17.65,22.95 L18.15,23.45 C18.3405714,23.6444218 18.3405714,23.9555782 18.15,24.15 L16.64,25.65 C16.5497197,25.7471383 16.4226039,25.8016165 16.29,25.8 L12.71,25.8 C12.5773961,25.8016165 12.4502803,25.7471383 12.36,25.65 L10.85,24.15 C10.6594286,23.9555782 10.6594286,23.6444218 10.85,23.45 Z" id="Shape" fill="#7C87A5" opacity="0.5"></path>
            <path d="M21.5,27.5 L26.5,27.5 L26.5,31.5 C26.5,32.0522847 26.0522847,32.5 25.5,32.5 L21.5,32.5 L21.5,27.5 Z" id="Shape" stroke="#CC4C23" fill="#F15A29" stroke-linecap="round" stroke-linejoin="round"></path>
        </g>
    </g>
</svg>