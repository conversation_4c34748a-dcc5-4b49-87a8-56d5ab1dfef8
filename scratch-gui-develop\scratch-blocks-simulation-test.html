<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scratch积木块模拟测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .code-area {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            min-height: 200px;
            border: 1px solid #ddd;
        }
        .output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            min-height: 200px;
            margin: 10px 0;
            overflow-y: auto;
            max-height: 400px;
            line-height: 1.6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .two-column {
            display: flex;
            gap: 20px;
        }
        .column {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Scratch积木块模拟测试</h1>
        <p>模拟从Scratch积木块生成C代码，然后执行的完整流程</p>

        <div id="status" class="status info">正在初始化...</div>

        <div class="two-column">
            <div class="column">
                <h3>模拟积木块数据</h3>
                <button class="btn" onclick="simulateSimpleLoop()" id="simpleBtn" disabled>简单循环积木块</button>
                <button class="btn" onclick="simulateComplexLoop()" id="complexBtn" disabled>复杂循环积木块</button>
                <button class="btn" onclick="simulateMovementBlocks()" id="movementBtn" disabled>运动积木块</button>
                
                <h4>生成的C代码：</h4>
                <div id="codeArea" class="code-area">等待选择积木块...</div>
                
                <button class="btn" onclick="executeGeneratedCode()" id="executeBtn" disabled>执行生成的C代码</button>
            </div>
            
            <div class="column">
                <h3>执行结果</h3>
                <div id="output" class="output">等待执行...</div>
            </div>
        </div>
    </div>

    <script src="./static/picoc-test/bundle.umd.js"></script>
    <script src="./src/lib/c-code-generator.js"></script>
    <script>
        let picocjs = null;
        let currentCode = '';
        
        // 初始化
        function init() {
            const statusDiv = document.getElementById('status');
            
            if (window.picocjs) {
                picocjs = window.picocjs;
                statusDiv.textContent = 'PicoC和C代码生成器加载成功！';
                statusDiv.className = 'status success';
                
                document.getElementById('simpleBtn').disabled = false;
                document.getElementById('complexBtn').disabled = false;
                document.getElementById('movementBtn').disabled = false;
            } else {
                statusDiv.textContent = 'PicoC加载失败';
                statusDiv.className = 'status error';
            }
        }

        // 模拟简单循环积木块
        function simulateSimpleLoop() {
            // 模拟Scratch积木块数据结构
            const mockBlocks = {
                _blocks: {
                    'block1': {
                        opcode: 'event_whenflagclicked',
                        next: 'block2',
                        inputs: {},
                        fields: {}
                    },
                    'block2': {
                        opcode: 'control_repeat',
                        next: null,
                        inputs: {
                            'TIMES': {
                                block: 'block3'
                            },
                            'SUBSTACK': {
                                block: 'block4'
                            }
                        },
                        fields: {}
                    },
                    'block3': {
                        opcode: 'math_number',
                        next: null,
                        inputs: {},
                        fields: {
                            'NUM': { value: '5' }
                        }
                    },
                    'block4': {
                        opcode: 'motion_movesteps',
                        next: 'block5',
                        inputs: {
                            'STEPS': {
                                block: 'block6'
                            }
                        },
                        fields: {}
                    },
                    'block5': {
                        opcode: 'motion_turnright',
                        next: null,
                        inputs: {
                            'DEGREES': {
                                block: 'block7'
                            }
                        },
                        fields: {}
                    },
                    'block6': {
                        opcode: 'math_number',
                        next: null,
                        inputs: {},
                        fields: {
                            'NUM': { value: '10' }
                        }
                    },
                    'block7': {
                        opcode: 'math_number',
                        next: null,
                        inputs: {},
                        fields: {
                            'NUM': { value: '72' }
                        }
                    }
                }
            };

            // 使用C代码生成器
            if (typeof generateCCode === 'function') {
                currentCode = generateCCode(mockBlocks, ['block1']);
                document.getElementById('codeArea').textContent = currentCode;
                document.getElementById('executeBtn').disabled = false;
            } else {
                // 如果生成器不可用，使用手动生成的代码
                currentCode = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    // 程序开始执行
    printf("开始重复执行 5 次\\n");
    for (int i = 0; i < 5; i++) {
        printf("第 %d 次循环开始\\n", i + 1);
        printf("开始移动 10 步\\n");
        printf("FORWARD:4:10\\n");
        printf("移动完成\\n");
        printf("开始向右旋转 72 度\\n");
        printf("TURN_RIGHT:72\\n");
        printf("右转完成\\n");
        printf("第 %d 次循环结束\\n", i + 1);
    }
    printf("重复执行完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
                document.getElementById('codeArea').textContent = currentCode;
                document.getElementById('executeBtn').disabled = false;
            }
        }

        // 模拟复杂循环积木块
        function simulateComplexLoop() {
            currentCode = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    printf("开始重复执行 10 次\\n");
    for (int i = 0; i < 10; i++) {
        printf("第 %d 次循环开始\\n", i + 1);
        printf("机器人前进：速度=4，距离=5\\n");
        printf("FORWARD:4:5\\n");
        printf("前进动作完成\\n");
        printf("机器人左转：角度=36度\\n");
        printf("TURN_LEFT:36\\n");
        printf("左转动作完成\\n");
        printf("第 %d 次循环结束\\n", i + 1);
    }
    printf("重复执行完成\\n");
    printf("GPP_SAY:1:十边形绘制完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            document.getElementById('codeArea').textContent = currentCode;
            document.getElementById('executeBtn').disabled = false;
        }

        // 模拟运动积木块
        function simulateMovementBlocks() {
            currentCode = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    printf("开始移动序列\\n");
    
    printf("开始移动 15 步\\n");
    printf("FORWARD:4:15\\n");
    printf("移动完成\\n");
    
    printf("开始向右旋转 90 度\\n");
    printf("TURN_RIGHT:90\\n");
    printf("右转完成\\n");
    
    printf("开始移动 10 步\\n");
    printf("FORWARD:4:10\\n");
    printf("移动完成\\n");
    
    printf("开始向左旋转 45 度\\n");
    printf("TURN_LEFT:45\\n");
    printf("左转完成\\n");
    
    printf("GPP_SAY:1:运动序列完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            document.getElementById('codeArea').textContent = currentCode;
            document.getElementById('executeBtn').disabled = false;
        }

        // 执行生成的C代码
        async function executeGeneratedCode() {
            if (!picocjs || !currentCode) {
                alert('请先加载PicoC并生成C代码');
                return;
            }

            const outputDiv = document.getElementById('output');
            outputDiv.textContent = '开始执行...\n';

            try {
                await picocjs.runC(currentCode, (output) => {
                    const processedOutput = output.replace(/\\n/g, '\n');
                    outputDiv.textContent += processedOutput;
                    outputDiv.scrollTop = outputDiv.scrollHeight;
                });

                outputDiv.textContent += '\n执行完成！\n';
                
            } catch (error) {
                outputDiv.textContent += `\n执行错误: ${error.message}\n`;
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
