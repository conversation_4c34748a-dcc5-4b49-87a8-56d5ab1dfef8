@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

html,
body,
.app {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
    background-color: #f9f9f9;
}

/* 隐藏滚动条但允许滚动 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: rgba(76, 151, 255, 0.5);
    border-radius: 10px;
    transition: all 0.3s;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(76, 151, 255, 0.7);
}

/* 设置全局字体 */
body {
    font-family: 'Poppins', "Helvetica Neue", Helvetica, Arial, sans-serif;
    color: #333;
    line-height: 1.6;
}

/* 添加全局过渡效果 */
* {
    transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
}
