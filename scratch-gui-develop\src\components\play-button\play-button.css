
@import "../../css/colors.css";
@import "../../css/units.css";

.play-button {
    display: flex;
    align-items: center;
    justify-content: center;

    overflow: hidden;  /* Mask the icon animation */
    width: 2.5rem;
    height: 2.5rem;
    background-color: $sound-primary;
    color: $ui-white;
    border-radius: 50%;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    user-select: none;
    cursor: pointer;
    transition: all 0.15s ease-out;
}

.play-button {
    position: absolute;
    top: .5rem;
    z-index: auto;
}

.play-button:focus {
    outline: none;
}

.play-icon {
  width: 50%;
}

[dir="ltr"] .play-button {
    right: .5rem;
}

[dir="rtl"] .play-button {
    left: .5rem;
}
