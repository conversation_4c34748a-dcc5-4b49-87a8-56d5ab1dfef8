苏晶.Scratch项目式学习研析[J].小学科学,2025,(13):127-129.
王明芳,承亚萍.Scratch编程思想融入小学数学单元项目化作业的实践[J].教育文汇,2025,(04):52-55.
贾旭梅,冯雪花.在“健康小导演”活动跨学科项目式学习中践行STEAM教育理念[J].小学科学,2025,(11):106-108.
詹洋,卢大伟.新课标下小学中高年级Scratch编程教学实践[J].天津教育,2025,(09):111-113.
李沫霏.基于STEAM教育理念下特色诗歌课程的构建——以诗歌《声声慢》教学为例[J].科学咨询,2025,(06):165-168.
徐文婷.发挥项目式学习在人工智能编程教学中的作用[J].小学科学,2025,(10):35-37.
张丽芳.提升小学生信息科技核心素养的教学实践——以Scratch编程教学为例[J].新课程,2025,(07):157-160.
祝带君,张伟明.基于Scratch的高能效人工智能语言教学系统构建[J].自动化与仪器仪表,2025,(02):266-269+274.DOI:10.14016/j.cnki.1001-9227.2025.02.266.
弭鑫鑫,董庆瑞.计算思维导向的小学科学教学设计研究——以“设计小台灯”为例[J].数字教育,2025,11(01):48-53.
高莹莹,陈永超,吴勇灵.Scratch在“自由落体运动”中的情境创设[J].数理化解题研究,2025,(03):102-105.
邵洪影.项目式教学在小学Scratch编程课中的应用路径探索[J].新课程导学,2025,(02):71-74.
余子斌.基于项目式学习的小学Scratch编程教学策略研究[J].信息与电脑,2025,37(01):230-232.
王宇.借力线上平台为编程教学赋能[J].小学科学,2025,(03):16-18.
严罗江.构建scratch编程教学模式[J].小学科学,2025,(01):82-84.
彭亮.小学信息技术Scratch编程教学中培养学生信息素养的有效路径[J].教育,2025,(01):90-92.
郭绍娜.图形化编程软件赋能中职数学的拓展应用研究[J].教育传播与技术,2024,(S1):141-147.
马宗兵.计算思维导向的小学编程教学策略研究[D].华南师范大学,2020.DOI:10.27154/d.cnki.ghnsu.2020.000003. 
王爱斌.Scratch软件在初中数学教学中的应用实践[J].中国新通信,2024,26(21):200-202.
陈芬.Scratch Jr在小学低年级中的教学实践[J].中小学班主任,2024,(20):62-63.
朱孟良.基于信息技术教学的小学Scratch编程软件教育研究[J].信息与电脑(理论版),2024,36(20):233-235.


[1]Sun L ,Liu J .Comparative study of codecombat-based python programming and scratch programming effects on sixth graders' Computational thinking: interaction effects of gender and programming experience[J].Thinking Skills and Creativity,2025,57101852-101852.
[2]Exposito E D ,Vega R M ,Remedios R C , et al.QScratch: introduction to quantum mechanics concepts through block-based programming[J].EPJ Quantum Technology,2025,12(1):12-12.
[3]Akanda W ,Clause J .Understanding comment practices in Scratch: A study of comments in a block-based visual programming language[J].The Journal of Systems &amp; Software,2025,222112329-112329.
[4]Lin Y ,Dultz S A ,Bailey M , et al.A programmable, open-source robot that scratches cultured tissues to investigate cell migration, healing, and tissue sculpting.[J].Cell reports methods,2024,4(12):100915.
[5]Taufiq M ,Kaniawati I ,Liliasari, et al.Computational modeling of parabolic motion with air resistance using scratch programming[J].Journal of Physics: Conference Series,2024,2900(1):012040-012040.
[6]Żyła K ,Chwaleba K ,Choma D .Evaluating Usability and Accessibility of Visual Programming Tools for Novice Programmers—The Case of App Inventor, Scratch, and StarLogo[J].Applied Sciences,2024,14(21):9887-9887.
[7]Lin Y ,Dultz S A ,Bailey M , et al.SCRATCH: A programmable, open-hardware, benchtop robot that automatically scratches cultured tissues to investigate cell migration, healing, and tissue sculpting.[J].bioRxiv : the preprint server for biology,2024,
[8]Kalemkuş J ,Kalemkuş F .The Effects of Designing Scientific Experiments with Visual Programming Language on Learning Outcomes[J].Science &amp; Education,2024,(prepublish):1-22.
[9]John H .Beginning C++ Game Programming:Learn C++ from scratch by building fun games[M].Packt Publishing Limited:2024-05-31. DOI:10.0000/9781835088258.
[10]Pellas N .Assessing Computational Thinking, Motivation, and Grit of Undergraduate Students Using Educational Robots[J].Journal of Educational Computing Research,2024,62(2):620-644.
[11]Osogami M ,Ohkuma K ,Pirotto C .The impact of teaching a graphical programming language before character-based programming on Japanese university students’ programming understanding[J].SN Social Sciences,2024,4(1):17-17.
[12]Saleem M M .Learn C Programming from Scratch:A step-by-step methodology with problem solving approach (English Edition)[M].BPB Publishers:2024-01-09. DOI:10.0000/9789355516060.
[13]Niko S ,Robbe P D ,Klaas G , et al.Blink: An educational software debugger for Scratch[J].SoftwareX,2024,25
[14]Alejandro E ,Camilo V ,Valeria B G .Student ability and difficulties with transfer from a block-based programming language into other programming languages: a case study in Colombia[J].Computer Science Education,2023,33(4):567-599.
[15]Christina Z ,Keisha V .Engaging girls in computer science: gender differences in attitudes and beliefs about learning scratch and python[J].Computer Science Education,2023,33(4):600-620.
[16]Paraskevi T ,Anastasios T M .Scratch-based learning objects for novice programmers: exploring quality aspects and perceptions for primary education[J].Interactive Learning Environments,2023,31(7):4219-4234.
[17]Jesennia C C ,Cristian S V ,Lisett A , et al.Applying recommendation system for developing programming competencies in children from a non-weird context[J].Education and Information Technologies,2023,29(8):9355-9386.
[18]Liat B ,B. R K R .Online pair-programming: elementary school children learning scratch together online[J].Journal of Research on Technology in Education,2023,55(5):799-816.
[19]Yusuf A ,Noor M N .Research trends on learning computer programming with program animation: A systematic mapping study[J].Computer Applications in Engineering Education,2023,31(6):1552-1582.
[20]Orit B ,Liat B ,Nurit C .“Start from scratch”: Integrating computational thinking skills in teacher education program[J].Thinking Skills and Creativity,2023,48
