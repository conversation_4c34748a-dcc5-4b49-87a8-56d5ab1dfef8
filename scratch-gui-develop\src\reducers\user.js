import { login as login<PERSON>pi, register as registerApi, logout as logout<PERSON>pi } from '../lib/user-api';
import { setToken, removeToken, setUser, getUser, isAuthenticated } from '../lib/auth-service';

const LOGIN = 'scratch-gui/user/LOGIN';
const LOGOUT = 'scratch-gui/user/LOGOUT';
const REGISTER = 'scratch-gui/user/REGISTER';
const LOGIN_ERROR = 'scratch-gui/user/LOGIN_ERROR';
const REGISTER_ERROR = 'scratch-gui/user/REGISTER_ERROR';
const CLEAR_ERROR = 'scratch-gui/user/CLEAR_ERROR';
const SET_LOADING = 'scratch-gui/user/SET_LOADING';

// 从localStorage获取用户信息初始化状态
const user = getUser();
const initialState = {
    isLoggedIn: isAuthenticated(),
    username: user ? user.username : '',
    error: null,
    loading: false
};

const reducer = function (state, action) {
    if (typeof state === 'undefined') state = initialState;

    switch (action.type) {
    case LOGIN:
        return {
            ...state,
            isLoggedIn: true,
            username: action.username,
            error: null,
            loading: false
        };
    case LOGOUT:
        return {
            ...state,
            isLoggedIn: false,
            username: '',
            error: null,
            loading: false
        };
    case REGISTER:
        return {
            ...state,
            isLoggedIn: true,
            username: action.username,
            error: null,
            loading: false
        };
    case LOGIN_ERROR:
        return {
            ...state,
            error: action.error,
            loading: false
        };
    case REGISTER_ERROR:
        return {
            ...state,
            error: action.error,
            loading: false
        };
    case CLEAR_ERROR:
        return {
            ...state,
            error: null
        };
    case SET_LOADING:
        return {
            ...state,
            loading: action.loading
        };
    default:
        return state;
    }
};

// Action Creators
const login = username => ({
    type: LOGIN,
    username
});

const logout = () => ({
    type: LOGOUT
});

const register = username => ({
    type: REGISTER,
    username
});

const loginError = error => ({
    type: LOGIN_ERROR,
    error
});

const registerError = error => ({
    type: REGISTER_ERROR,
    error
});

const clearError = () => ({
    type: CLEAR_ERROR
});

const setLoading = loading => ({
    type: SET_LOADING,
    loading
});

// Thunks
const handleLogin = (username, password) => (dispatch => {
    console.log('Redux handleLogin 被调用:', username);

    if (!username || !password) {
        console.warn('用户名或密码为空');
        dispatch(loginError('用户名或密码不能为空'));
        alert('Redux验证: 用户名或密码不能为空');
        return Promise.reject(new Error('用户名或密码不能为空'));
    }

    dispatch(setLoading(true));
    console.log('开始登录请求:', username);

    try {
        return loginApi(username, password)
            .then(data => {
                console.log('登录成功，获取数据:', data);
                // 保存token和用户信息
                setToken(data.token);
                setUser(data.user);
                dispatch(login(data.user.username));
                return data;
            })
            .catch(error => {
                console.error('登录失败:', error);
                dispatch(loginError(error.message || '登录失败'));
                alert('登录失败: ' + (error.message || '未知错误'));
                throw error;
            })
            .finally(() => {
                dispatch(setLoading(false));
            });
    } catch (error) {
        console.error('登录API调用异常:', error);
        dispatch(loginError(error.message || '登录请求异常'));
        dispatch(setLoading(false));
        alert('登录请求异常: ' + (error.message || '未知错误'));
        return Promise.reject(error);
    }
});

const handleRegister = (username, password) => (dispatch => {
    console.log('Redux handleRegister 被调用:', username);

    if (!username || !password) {
        console.warn('用户名或密码为空');
        dispatch(registerError('用户名或密码不能为空'));
        alert('Redux验证: 用户名或密码不能为空');
        return Promise.reject(new Error('用户名或密码不能为空'));
    }

    dispatch(setLoading(true));
    console.log('开始注册请求:', username);

    try {
        return registerApi(username, password)
            .then(data => {
                console.log('注册成功，获取数据:', data);
                // 保存token和用户信息
                setToken(data.token);
                setUser(data.user);
                dispatch(register(data.user.username));
                return data;
            })
            .catch(error => {
                console.error('注册失败:', error);
                dispatch(registerError(error.message || '注册失败'));
                alert('注册失败: ' + (error.message || '未知错误'));
                throw error;
            })
            .finally(() => {
                dispatch(setLoading(false));
            });
    } catch (error) {
        console.error('注册API调用异常:', error);
        dispatch(registerError(error.message || '注册请求异常'));
        dispatch(setLoading(false));
        alert('注册请求异常: ' + (error.message || '未知错误'));
        return Promise.reject(error);
    }
});

const handleLogout = () => (dispatch => {
    dispatch(setLoading(true));

    return logoutApi()
        .then(() => {
            // 移除token和用户信息
            removeToken();
            dispatch(logout());
            // 在成功登出后重定向到登录页面，添加logout参数确保完全清除登录状态
            window.location.href = 'login.html?logout=true';
        })
        .catch(error => {
            console.error('登出失败', error);
            // 即使API调用失败，也要清除本地存储和状态
            removeToken();
            dispatch(logout());
            // 在登出后重定向到登录页面，即使发生错误
            window.location.href = 'login.html?logout=true';
        })
        .finally(() => {
            dispatch(setLoading(false));
        });
});

// 检查用户是否已认证的函数
const checkAuth = () => (dispatch => {
    return new Promise(resolve => {
        if (isAuthenticated()) {
            const user = getUser();
            if (user) {
                dispatch(login(user.username));
                resolve(true);
                return;
            }
        }
        resolve(false);
    });
});

export {
    reducer as default,
    initialState as userInitialState,
    login,
    logout,
    register,
    loginError,
    registerError,
    clearError,
    setLoading,
    handleLogin,
    handleRegister,
    handleLogout,
    checkAuth,
    isAuthenticated
};
