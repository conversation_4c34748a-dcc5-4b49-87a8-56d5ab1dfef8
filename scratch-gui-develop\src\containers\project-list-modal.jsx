import {connect} from 'react-redux';
import React from 'react';
import PropTypes from 'prop-types';

import {
    closeProjectListModal,
    openLoadingProject,
    closeLoadingProject
} from '../reducers/modals';
import {
    fetchProjects,
    fetchProjectDetail,
    deleteProject
} from '../lib/project-api';
import {
    setProjectIdWithoutFetch,
    onLoadedProject
} from '../reducers/project-state';
import {setProjectTitle} from '../reducers/project-title';
import {showStandardAlert, closeAlertWithId} from '../reducers/alerts';

import ProjectListModalComponent from '../components/project-list/project-list-modal.jsx';
// 引入K1积木块定义
import defineK1CustomBlocks from '../lib/k1-custom-blocks';
// 引入K1积木块VM扩展
import registerK1Blocks from '../lib/k1-vm-extension';

class ProjectListModal extends React.Component {
    constructor (props) {
        super(props);
        this.handleClose = this.handleClose.bind(this);
        this.handleFetchProjects = this.handleFetchProjects.bind(this);
        this.handleOpenProject = this.handleOpenProject.bind(this);
        this.handleDeleteProject = this.handleDeleteProject.bind(this);
    }

    handleClose () {
        this.props.onClose();
    }

    handleFetchProjects () {
        return fetchProjects()
            .then(response => {
                return response.data || [];
            })
            .catch(error => {
                console.error('获取项目列表失败:', error);
                alert('获取项目列表失败: ' + (error.message || '未知错误'));
                return [];
            });
    }

    handleOpenProject (projectId) {
        // 显示加载中提示
        this.props.onLoadingStarted();

        // 先关闭项目列表模态框
        this.props.onClose();

        // 获取项目详情
        fetchProjectDetail(projectId)
            .then(response => {
                const projectData = response.data;
                if (!projectData || !projectData.projectBlocks) {
                    throw new Error('项目数据为空或格式不正确');
                }

                console.log('获取到项目数据:', projectData.projectName, '项目ID:', projectData.projectId);

                // 设置项目标题
                this.props.onSetProjectTitle(projectData.projectName);

                // 设置项目ID到Redux状态，但不触发获取操作
                this.props.onSetProjectId(projectData.projectId.toString());
                console.log('已设置项目ID到Redux状态(不触发获取):', projectData.projectId);

                try {
                    // 优先使用Base64编码的项目数据
                    const projectBlocksBase64 = projectData.projectBlocksBase64;

                    if (!projectBlocksBase64) {
                        throw new Error('项目数据为空或格式不正确（缺少Base64编码数据）');
                    }

                    console.log('使用Base64编码的项目数据');

                    // 解码Base64字符串为二进制数据
                    const binaryString = atob(projectBlocksBase64);
                    const uint8Array = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        uint8Array[i] = binaryString.charCodeAt(i);
                    }

                    console.log('项目数据大小:', uint8Array.length, '字节');

                    // 检查数据是否为有效的.sb3文件（应该以PK开头，表示ZIP文件）
                    if (uint8Array.length > 2 && uint8Array[0] === 80 && uint8Array[1] === 75) {
                        console.log('数据格式有效，似乎是.sb3文件');
                    } else {
                        console.warn('数据格式可能不是有效的.sb3文件');
                        // 尝试打印前几个字节以进行调试
                        console.log('前10个字节:', Array.from(uint8Array.slice(0, 10)));
                    }

                    // 确保K1积木块已经注册到VM中
                    this.ensureK1BlocksRegistered();

                    // 为VM加载项目添加超时和错误处理
                    const loadProjectWithTimeout = () => {
                        return new Promise((resolve, reject) => {
                            // 设置15秒超时
                            const timeoutId = setTimeout(() => {
                                reject(new Error('加载项目超时，可能是扩展脚本加载失败'));
                            }, 15000);

                            // 尝试加载项目
                            this.props.vm.loadProject(uint8Array.buffer)
                                .then(result => {
                                    clearTimeout(timeoutId);
                                    resolve(result);
                                })
                                .catch(error => {
                                    clearTimeout(timeoutId);

                                    // 检查是否是K1脚本加载错误
                                    if (error.message && (
                                        error.message.includes('k1') ||
                                        error.message.includes('Failed to execute \'importScripts\'')
                                    )) {
                                        console.error('K1扩展脚本加载失败，尝试应用补丁后重新加载');

                                        // 应用K1扩展脚本补丁
                                        this.applyK1ExtensionPatch();

                                        // 重新尝试加载项目
                                        return this.props.vm.loadProject(uint8Array.buffer)
                                            .then(resolve)
                                            .catch(secondError => {
                                                console.error('第二次尝试加载项目失败:', secondError);
                                                reject(secondError);
                                            });
                                    }

                                    reject(error);
                                });
                        });
                    };

                    // 加载项目到VM
                    return loadProjectWithTimeout()
                        .then(() => {
                            console.log('项目加载成功');
                            // 更新项目状态 - 使用常量值而不是依赖props中的loadingState
                            this.props.onLoadingFinished('LOADING_VM_FILE_UPLOAD', true);
                        })
                        .catch(vmError => {
                            console.error('VM加载项目失败:', vmError);
                            // 尝试更详细的错误信息
                            if (vmError.message.includes('FixedAsciiString')) {
                                throw new Error('项目数据包含非ASCII字符，可能是数据格式问题');
                            } else if (vmError.message.includes('importScripts') && vmError.message.includes('k1')) {
                                throw new Error('K1扩展加载失败，请检查网络连接或联系管理员');
                            } else {
                                throw vmError;
                            }
                        });
                } catch (e) {
                    console.error('处理项目数据时出错:', e);
                    throw new Error('处理项目数据时出错: ' + e.message);
                }
            })
            .catch(error => {
                console.error('加载项目失败:', error);
                alert('加载项目失败: ' + (error.message || '未知错误'));
                // 使用常量值而不是依赖props中的loadingState
                this.props.onLoadingFinished('LOADING_VM_FILE_UPLOAD', false);
            });
    }

    // 确保VM能够识别K1积木块
    ensureK1BlocksRegistered() {
        try {
            console.log('确保K1积木块已注册到VM...');

            // 确保K1积木块已经在Scratch Blocks中定义
            defineK1CustomBlocks();

            // 注册K1积木块到VM运行时
            registerK1Blocks(this.props.vm);

            // 替换VM的loadProject方法，添加错误处理
            this.patchVMLoadProject();

            console.log('K1积木块已完全注册，VM现在可以正确处理K1积木块');
        } catch (e) {
            console.error('注册K1积木块失败:', e);
        }
    }

    // 给VM的loadProject方法添加补丁，处理K1扩展加载错误
    patchVMLoadProject() {
        // 只应用一次补丁
        if (this.props.vm._originalLoadProject) return;

        try {
            // 保存原始的loadProject方法
            this.props.vm._originalLoadProject = this.props.vm.loadProject;

            // 替换为带错误处理的版本
            this.props.vm.loadProject = (projectData) => {
                console.log('使用带K1错误处理的loadProject...');

                return new Promise((resolve, reject) => {
                    // 首先尝试正常加载
                    this.props.vm._originalLoadProject(projectData)
                        .then(resolve)
                        .catch(error => {
                            // 检查是否是K1扩展加载错误
                            if (error && error.message && (
                                error.message.includes('k1') ||
                                error.message.includes('importScripts') ||
                                error.message.includes('extension')
                            )) {
                                console.error('K1扩展加载失败，应用错误恢复机制:', error);

                                // 应用K1扩展补丁
                                this.applyK1ExtensionPatch();

                                // 重试加载
                                this.props.vm._originalLoadProject(projectData)
                                    .then(resolve)
                                    .catch(secondError => {
                                        console.error('应用补丁后加载项目仍然失败:', secondError);
                                        reject(secondError);
                                    });
                            } else {
                                // 不是K1相关错误，直接拒绝
                                reject(error);
                            }
                        });
                });
            };

            console.log('VM loadProject方法补丁已应用');
        } catch (e) {
            console.error('应用VM loadProject补丁失败:', e);
        }
    }

    // 应用K1扩展脚本补丁
    applyK1ExtensionPatch() {
        console.log('应用K1扩展脚本补丁...');

        try {
            // 创建一个假的K1扩展对象
            const fakeK1Extension = {
                id: 'k1',
                name: 'K1机器人',
                blocks: []
            };

            // 直接注入到VM的扩展管理器
            if (this.props.vm.extensionManager && this.props.vm.extensionManager._loadedExtensions) {
                this.props.vm.extensionManager._loadedExtensions.k1 = fakeK1Extension;
                console.log('K1扩展补丁应用成功');
            } else {
                console.warn('无法应用K1扩展补丁：扩展管理器不可用');
            }
        } catch (e) {
            console.error('应用K1扩展补丁失败:', e);
        }
    }

    // 处理删除项目
    handleDeleteProject(projectId) {
        console.log('删除项目，ID:', projectId);

        return deleteProject(projectId)
            .then(response => {
                console.log('删除项目成功:', response);

                // 显示成功提示
                this.props.onShowSuccessAlert('deleteProjectSuccess');

                return response;
            })
            .catch(error => {
                console.error('删除项目失败:', error);
                throw error;
            });
    }

    render () {
        const {
            isOpen
        } = this.props;

        return (
            <ProjectListModalComponent
                isOpen={isOpen}
                onClose={this.handleClose}
                onFetchProjects={this.handleFetchProjects}
                onOpenProject={this.handleOpenProject}
                onDeleteProject={this.handleDeleteProject}
            />
        );
    }
}

ProjectListModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    loadingState: PropTypes.string,
    onClose: PropTypes.func.isRequired,
    onLoadingStarted: PropTypes.func.isRequired,
    onLoadingFinished: PropTypes.func.isRequired,
    onSetProjectTitle: PropTypes.func.isRequired,
    onSetProjectId: PropTypes.func.isRequired,
    vm: PropTypes.shape({
        loadProject: PropTypes.func
    })
};

const mapStateToProps = state => ({
    isOpen: state.scratchGui.modals.projectListModal,
    loadingState: state.scratchGui.projectState.loadingState,
    vm: state.scratchGui.vm
});

const mapDispatchToProps = dispatch => ({
    onClose: () => dispatch(closeProjectListModal()),
    onLoadingStarted: () => dispatch(openLoadingProject()),
    onLoadingFinished: (loadingState, success) => {
        // 确保loadingState是有效的，如果未定义则使用默认值
        const safeLoadingState = loadingState || 'LOADING_VM_FILE_UPLOAD';
        dispatch(onLoadedProject(safeLoadingState, true, success));
        dispatch(closeLoadingProject());

        // 如果成功加载，显示成功提示
        if (success) {
            dispatch(showStandardAlert('openProjectSuccess'));
            // 5秒后自动关闭提示
            setTimeout(() => {
                dispatch(closeAlertWithId('openProjectSuccess'));
            }, 5000);
        }
    },
    onSetProjectTitle: title => dispatch(setProjectTitle(title)),
    onSetProjectId: id => dispatch(setProjectIdWithoutFetch(id)),
    onShowSuccessAlert: alertId => {
        dispatch(showStandardAlert(alertId));
        // 5秒后自动关闭提示
        setTimeout(() => {
            dispatch(closeAlertWithId(alertId));
        }, 5000);
    }
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(ProjectListModal);
