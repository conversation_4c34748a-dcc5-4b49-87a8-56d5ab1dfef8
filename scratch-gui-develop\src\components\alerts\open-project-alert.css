@keyframes fadeInSlideUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    20% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(0, 150, 136, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 150, 136, 0.8);
    }
    100% {
        box-shadow: 0 0 5px rgba(0, 150, 136, 0.5);
    }
}

/* 使用更高优先级的选择器 */
div.open-project-alert {
    animation:
        fadeInSlideUp 0.5s ease-out forwards,
        pulse 1s ease-in-out 0.5s,
        glow 2s ease-in-out 0.5s !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.3s ease !important;
    border-left: 4px solid #009688 !important; /* 添加左侧边框，使用!important确保覆盖原样式 */
}

div.open-project-alert:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
    transform: translateY(-2px) !important;
}

img.open-project-alert-icon {
    animation: pulse 1s ease-in-out infinite !important;
    filter: drop-shadow(0 0 3px rgba(0, 150, 136, 0.5)) !important;
}
