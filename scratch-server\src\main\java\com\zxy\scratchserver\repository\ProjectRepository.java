package com.zxy.scratchserver.repository;

import com.zxy.scratchserver.model.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 项目数据访问接口
 */
@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    
    /**
     * 根据用户ID查找项目列表
     * @param userId 用户ID
     * @return 项目列表
     */
    List<Project> findByUserId(Long userId);
}
