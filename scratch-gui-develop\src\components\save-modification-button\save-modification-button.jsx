import React from 'react';
import PropTypes from 'prop-types';
import {FormattedMessage} from 'react-intl';
import bindAll from 'lodash.bindall';
import Button from '../button/button.jsx';
import styles from './save-modification-button.css';
import {saveModification} from '../../lib/project-api';

class SaveModificationButton extends React.Component {
    constructor (props) {
        super(props);
        bindAll(this, [
            'handleClick',
            'handleSaveSuccess',
            'handleSaveError'
        ]);
        this.state = {
            saving: false,
            error: null,
            success: false
        };
    }

    handleClick () {
        this.setState({saving: true, error: null, success: false});

        // 使用VM的saveProjectSb3方法导出项目
        this.props.vm.saveProjectSb3()
            .then(content => {
                // 创建Blob对象
                const blob = new Blob([content], {type: 'application/zip'});

                // 获取项目名称和ID
                const projectName = this.props.projectName || '未命名项目';
                const projectId = this.props.projectId;

                console.log('保存修改项目ID:', projectId);

                // 检查项目ID是否有效
                if (!projectId || projectId === '0') {
                    console.error('项目ID无效:', projectId);
                    throw new Error('项目ID无效，请先打开一个有效的项目');
                }

                // 保存修改到服务器
                return saveModification(projectName, blob, projectId);
            })
            .then(this.handleSaveSuccess)
            .catch(this.handleSaveError);
    }

    handleSaveSuccess (response) {
        console.log('保存修改成功:', response);

        this.setState({
            saving: false,
            success: true,
            error: null
        });

        // 如果提供了成功回调，则调用
        if (this.props.onSaveSuccess) {
            try {
                this.props.onSaveSuccess(response);
            } catch (callbackError) {
                console.error('成功回调执行失败:', callbackError);
            }
        }
    }

    handleSaveError (error) {
        console.error('保存修改错误:', error);

        this.setState({
            saving: false,
            success: false,
            error: error && error.message ? error.message : '保存失败'
        });

        // 如果提供了错误回调，则调用
        if (this.props.onSaveError) {
            try {
                this.props.onSaveError();
            } catch (callbackError) {
                console.error('错误回调执行失败:', callbackError);
            }
        }
    }

    render () {
        return (
            <Button
                className={styles.saveModificationButton}
                onClick={this.handleClick}
                disabled={this.state.saving}
            >
                {this.state.saving ? (
                    <FormattedMessage
                        defaultMessage="保存中..."
                        description="保存修改按钮文本 - 保存中"
                        id="gui.saveModificationButton.saving"
                    />
                ) : (
                    <FormattedMessage
                        defaultMessage="保存修改"
                        description="保存修改按钮文本"
                        id="gui.saveModificationButton.saveModification"
                    />
                )}
            </Button>
        );
    }
}

SaveModificationButton.propTypes = {
    vm: PropTypes.shape({
        saveProjectSb3: PropTypes.func
    }),
    projectName: PropTypes.string,
    projectId: PropTypes.string,
    onSaveSuccess: PropTypes.func,
    onSaveError: PropTypes.func
};

export default SaveModificationButton;
