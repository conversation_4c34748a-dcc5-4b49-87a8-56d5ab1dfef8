@import "../../css/units.css";
@import "../../css/colors.css";

.backpack-container {
    flex-shrink: 1;
    position: relative;
}

.backpack-header {
    margin-top: 0.5rem;
    border: 1px solid $ui-black-transparent;
    background: $ui-white;
    padding: 0.25rem;
    text-align: center;
    font-size: 0.85rem;
    color: $text-primary;
    transition: 0.2s;
    cursor: pointer;
    user-select: none;
}

[dir="ltr"] .backpack-header {
    border-top-right-radius: $space;
}

[dir="rtl"] .backpack-header {
    border-top-left-radius: $space;
}

.backpack-list {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-right: 1px solid $ui-black-transparent;
    min-height: 5.5rem;
}

/* Absolute position the inner list to allow scrolling inside flex sized container */
.backpack-list-inner {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    overflow-x: auto;
}

.drag-over:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.75;
    background-color: $drop-highlight;
    transition: all 0.25s ease;
}

.status-message {
    width: 100%;
    text-align: center;
    font-size: 0.85rem;
    color: $text-primary;
}

.backpack-item {
    width: 4rem;
    height: 4.5rem;
    margin: 0 0.25rem;
    flex-shrink: 0;

    /* Need to hide overflow because of background setting below */
    overflow: hidden;
}

.backpack-item > div {
    /* Need to set the background to get blend-mode below to work */
    background: $ui-primary;
}

.backpack-item img {
    mix-blend-mode: multiply; /* Make white transparent for thumnbnails */
}

.more {
    background: $looks-secondary;
    color: $ui-white;
    border: none;
    outline: none;
    font-weight: bold;
    border-radius: 0.5rem;
    font-size: 0.85rem;
    padding: 0.5rem;
    margin: 0.5rem;
    cursor: pointer;
}
