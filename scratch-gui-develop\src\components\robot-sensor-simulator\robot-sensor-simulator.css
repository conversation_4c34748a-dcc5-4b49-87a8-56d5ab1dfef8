@import "../../css/units.css";
@import "../../css/colors.css";

.robot-sensor-simulator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: $ui-white;
    border-radius: $space;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    z-index: 1000;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid $ui-black-transparent;
    background-color: $ui-primary;
    color: $ui-white;
    border-radius: $space $space 0 0;
}

.title {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.close-button {
    background: none;
    border: none;
    color: $ui-white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: $space;
    transition: background-color 0.2s;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.content {
    flex: 1;
    padding: 0;
    overflow: hidden;
}

.iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 0 0 $space $space;
}
