@startuml C代码生成器模块时序图

' 定义参与者
actor 用户
participant "编辑器" as Editor
participant "代码生成器" as CodeGen
participant "积木处理器" as BlockProc
participant "变量处理器" as VarProc
participant "C代码面板" as CCodePanel

' 核心流程
用户 -> Editor: 编辑积木程序
Editor -> CodeGen: CodeGenerator.generateCode(blockStructure)
activate CodeGen

CodeGen -> VarProc: 处理变量声明
VarProc -> VarProc: 分析变量类型和值
VarProc -> CodeGen: 返回变量声明代码

CodeGen -> CodeGen: 添加函数声明和头文件

CodeGen -> BlockProc: 处理积木块序列
activate BlockProc

loop 遍历积木树
    BlockProc -> BlockProc: CodeGenerator.processBlock(block, context)
    
    alt 函数定义块
        BlockProc -> BlockProc: 生成用户自定义函数
    else 事件处理块
        BlockProc -> BlockProc: 生成事件处理代码
    else 控制流块
        BlockProc -> BlockProc: 处理条件分支和循环
    else 传感器和执行块
        BlockProc -> BlockProc: 映射到对应的C函数调用
    end
    
    BlockProc -> BlockProc: 处理下一个积木块
end

BlockProc --> CodeGen: 返回生成的代码
deactivate BlockProc

CodeGen -> CodeGen: 优化和格式化代码
CodeGen -> CodeGen: 添加注释增强可读性
CodeGen -> CodeGen: 生成完整的C程序

CodeGen --> CCodePanel: 返回生成的C代码
deactivate CodeGen

CCodePanel -> CCodePanel: 高亮显示代码
CCodePanel --> 用户: 显示生成的C代码

@enduml
