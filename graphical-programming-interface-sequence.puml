@startuml 图形化编程界面模块时序图

' 定义参与者
actor 用户
participant "编辑器" as Editor
participant "积木管理器" as BlockMgr
participant "虚拟机" as VM
participant "代码生成器" as CodeGen

' 简化的流程
== 初始化阶段 ==
用户 -> Editor: 打开编辑器
Editor -> VM: 初始化环境
VM --> Editor: 加载积木库资源完成
Editor --> 用户: 显示编程界面(积木面板、编辑区、舞台区)

== 积木操作阶段 ==
用户 -> BlockMgr: 从面板选择积木
BlockMgr --> 用户: 显示拖动效果

用户 -> BlockMgr: 拖放积木至编辑区
BlockMgr -> BlockMgr: 验证积木连接

alt 连接有效
    BlockMgr -> VM: 更新积木结构
    VM -> CodeGen: 生成代码
    CodeGen --> 用户: 显示生成的代码
else 连接无效
    BlockMgr --> 用户: 显示无效连接提示
end

用户 -> BlockMgr: 继续组装程序

== 程序执行阶段 ==
用户 -> VM: 点击运行按钮
VM -> VM: 解析积木结构生成指令

loop 程序执行
    VM -> VM: 执行当前积木
    VM -> Editor: 高亮当前执行的积木
    VM -> VM: 更新舞台显示
end

alt 用户中断
    用户 -> VM: 点击停止按钮
    VM -> VM: 中断程序执行
end

VM --> 用户: 程序执行结束

@enduml
