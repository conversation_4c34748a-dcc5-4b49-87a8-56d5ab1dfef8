@startuml 传感器仿真模块时序图

' 定义参与者
actor 用户
participant "积木面板" as BlockPanel
participant "传感器仿真器" as SensorSim
participant "虚拟机" as VM

' 核心流程
用户 -> BlockPanel: 按传感器类型选择积木
note right: 选择"碰到颜色？"等侦测类积木\n或K1传感器积木

alt Scratch内置侦测类积木
    BlockPanel -> SensorSim: 创建颜色传感器仿真
    用户 -> SensorSim: 配置传感器参数(如选择颜色)
else K1传感器积木
    BlockPanel -> SensorSim: 创建对应传感器仿真
    note right: 光线传感器、距离传感器等
end

用户 -> VM: 运行程序

loop 程序执行
    VM -> SensorSim: 请求传感器数据
    SensorSim -> SensorSim: 模拟传感器行为
    note right: 检测颜色碰撞或\n生成模拟传感器数值
    SensorSim --> VM: 返回传感器数据
    VM -> VM: 根据传感器数据执行程序
end

VM --> 用户: 程序执行结束

@enduml
