<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单PicoC测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .output {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            min-height: 100px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简单PicoC测试</h1>
        <p>这个页面用于测试picoc-js是否能正常加载和执行</p>

        <div id="status" class="status info">正在加载PicoC...</div>

        <button class="btn" onclick="testBasic()" id="testBtn" disabled>测试基本功能</button>
        <button class="btn" onclick="testRobotCommands()" id="robotBtn" disabled>测试机器人命令</button>
        <button class="btn" onclick="testLoop()" id="loopBtn" disabled>测试循环</button>

        <div id="output" class="output">等待测试...</div>
    </div>

    <script>
        let picocjs = null;
        let statusDiv = document.getElementById('status');
        let outputDiv = document.getElementById('output');

        // 尝试加载picoc-js
        function loadPicoC() {
            statusDiv.textContent = '正在加载PicoC...';
            statusDiv.className = 'status info';

            // 尝试多个可能的路径
            const paths = [
                './static/picoc-test/bundle.umd.js',
                './picoc-test/bundle.umd.js',
                '../static/picoc-test/bundle.umd.js'
            ];

            let currentPath = 0;

            function tryLoadPath() {
                if (currentPath >= paths.length) {
                    statusDiv.textContent = '所有路径都无法加载PicoC';
                    statusDiv.className = 'status error';
                    return;
                }

                const script = document.createElement('script');
                script.src = paths[currentPath];
                
                script.onload = () => {
                    console.log('PicoC脚本加载成功，路径:', paths[currentPath]);
                    if (window.picocjs) {
                        picocjs = window.picocjs;
                        statusDiv.textContent = `PicoC加载成功！路径: ${paths[currentPath]}`;
                        statusDiv.className = 'status success';
                        
                        // 启用按钮
                        document.getElementById('testBtn').disabled = false;
                        document.getElementById('robotBtn').disabled = false;
                        document.getElementById('loopBtn').disabled = false;
                        
                        outputDiv.textContent = 'PicoC已准备就绪，可以开始测试！';
                    } else {
                        statusDiv.textContent = `脚本加载但未找到picocjs对象，路径: ${paths[currentPath]}`;
                        statusDiv.className = 'status error';
                    }
                };
                
                script.onerror = () => {
                    console.log('PicoC脚本加载失败，路径:', paths[currentPath]);
                    currentPath++;
                    tryLoadPath();
                };
                
                document.head.appendChild(script);
            }

            tryLoadPath();
        }

        function clearOutput() {
            outputDiv.textContent = '';
        }

        function appendOutput(text) {
            outputDiv.textContent += text;
            outputDiv.scrollTop = outputDiv.scrollHeight;
        }

        async function runCCode(code, description) {
            if (!picocjs) {
                appendOutput('PicoC 未加载\n');
                return;
            }

            clearOutput();
            appendOutput(`开始执行: ${description}\n`);
            appendOutput('='.repeat(40) + '\n');

            try {
                await picocjs.runC(code, (output) => {
                    appendOutput(output);
                });
                appendOutput('\n' + '='.repeat(40) + '\n');
                appendOutput('执行完成！\n');
            } catch (error) {
                appendOutput('\n' + '='.repeat(40) + '\n');
                appendOutput(`执行错误: ${error.message}\n`);
            }
        }

        function testBasic() {
            const code = `#include <stdio.h>

int main() {
    printf("Hello, PicoC!\\n");
    printf("基本功能测试成功\\n");
    return 0;
}`;
            runCCode(code, '基本功能测试');
        }

        function testRobotCommands() {
            const code = `#include <stdio.h>

int main() {
    printf("FORWARD:4:10\\n");
    printf("TURN_RIGHT:90\\n");
    printf("FORWARD:4:5\\n");
    printf("GPP_SAY:1:机器人控制测试\\n");
    return 0;
}`;
            runCCode(code, '机器人命令测试');
        }

        function testLoop() {
            const code = `#include <stdio.h>

int main() {
    int i;
    printf("开始循环测试\\n");
    for(i = 0; i < 3; i++) {
        printf("循环 %d: FORWARD:4:5\\n", i+1);
        printf("循环 %d: TURN_RIGHT:90\\n", i+1);
    }
    printf("循环测试完成\\n");
    return 0;
}`;
            runCCode(code, '循环测试');
        }

        // 页面加载时自动尝试加载PicoC
        window.addEventListener('load', loadPicoC);
    </script>
</body>
</html>
