import {connect} from 'react-redux';
import React from 'react';
import PropTypes from 'prop-types';

import {
    handleLogin,
    handleRegister,
    clearError
} from '../reducers/user';
import {redirectToHome} from '../lib/routing';

import LoginPageComponent from '../components/login/login-page.jsx';

class LoginPage extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            localError: null
        };
        this.handleLogin = this.handleLogin.bind(this);
        this.handleRegister = this.handleRegister.bind(this);
        this.clearLocalError = this.clearLocalError.bind(this);
    }

    componentDidMount () {
        console.log('登录页面组件挂载，当前登录状态:', this.props.isLoggedIn);
        
        // 清除之前的错误
        this.props.onClearError();
        this.clearLocalError();
        
        // 检查URL参数，如果有logout参数，强制清除登录状态
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('logout')) {
            console.log('检测到登出参数，强制清除登录状态');
            // 使用localStorage直接清除，确保彻底登出
            localStorage.removeItem('scratch_token');
            localStorage.removeItem('scratch_user');
            // 移除URL参数并刷新页面
            window.history.replaceState({}, document.title, window.location.pathname);
            window.location.reload();
            return;
        }
        
        // 检查是否已登录
        if (this.props.isLoggedIn) {
            console.log('用户已登录，显示提示信息');
            this.setState({
                localError: '您已经登录，可以直接进入系统或退出后重新登录'
            });
        }
    }

    componentDidUpdate (prevProps) {
        // 登录成功后重定向到主页
        if (!prevProps.isLoggedIn && this.props.isLoggedIn) {
            this.redirectToHome();
        }
    }

    redirectToHome () {
        // 重定向到主页
        console.log('登录成功，即将跳转到主页');
        
        try {
            // 确保localStorage中的用户信息已经保存
            const user = localStorage.getItem('scratch_user');
            console.log('当前用户信息:', user);
            
            // 使用延迟确保状态已更新
            setTimeout(() => {
                // 使用导入的redirectToHome函数
                redirectToHome(true); // 传入true强制刷新页面
            }, 100);
        } catch (error) {
            console.error('跳转出错:', error);
            alert('登录成功！尝试手动跳转到主页');
            redirectToHome(true);
        }
    }

    clearLocalError() {
        this.setState({ localError: null });
    }

    handleLogin (username, password) {
        console.log('容器组件handleLogin被调用，用户名:', username);
        
        // 清除之前的错误
        this.clearLocalError();
        this.props.onClearError();

        if (!username || !password) {
            console.error('用户名或密码为空');
            this.setState({ 
                localError: '用户名和密码不能为空' 
            });
            return;
        }

        try {
            console.log('调用Redux onLogin方法');
            const result = this.props.onLogin(username, password);
            
            if (result && typeof result.then === 'function') {
                console.log('onLogin返回了Promise，等待结果');
                result
                    .then(data => {
                        console.log('登录请求成功:', data);
                    })
                    .catch(error => {
                        console.error('登录失败', error);
                        this.setState({ 
                            localError: error.message || '登录失败，请稍后再试' 
                        });
                        alert(`登录出错: ${error.message || '未知错误'}`);
                    });
            } else {
                console.warn('登录函数没有返回Promise:', result);
                this.setState({ 
                    localError: '系统错误：登录函数没有返回Promise' 
                });
            }
        } catch (error) {
            console.error('登录函数调用出错:', error);
            this.setState({ 
                localError: `登录处理出错: ${error.message || '未知错误'}` 
            });
            alert(`登录系统错误: ${error.message || '未知错误'}`);
        }
    }

    handleRegister (username, password) {
        console.log('容器组件handleRegister被调用，用户名:', username);
        
        // 清除之前的错误
        this.clearLocalError();
        this.props.onClearError();

        if (!username || !password) {
            console.error('用户名或密码为空');
            this.setState({ 
                localError: '用户名和密码不能为空' 
            });
            return;
        }

        try {
            console.log('调用Redux onRegister方法');
            const result = this.props.onRegister(username, password);
            
            if (result && typeof result.then === 'function') {
                console.log('onRegister返回了Promise，等待结果');
                result
                    .then(data => {
                        console.log('注册请求成功:', data);
                    })
                    .catch(error => {
                        console.error('注册失败', error);
                        this.setState({ 
                            localError: error.message || '注册失败，请稍后再试' 
                        });
                        alert(`注册出错: ${error.message || '未知错误'}`);
                    });
            } else {
                console.warn('注册函数没有返回Promise:', result);
                this.setState({ 
                    localError: '系统错误：注册函数没有返回Promise' 
                });
            }
        } catch (error) {
            console.error('注册函数调用出错:', error);
            this.setState({ 
                localError: `注册处理出错: ${error.message || '未知错误'}` 
            });
            alert(`注册系统错误: ${error.message || '未知错误'}`);
        }
    }

    render () {
        const {
            error: reduxError,
            loading,
            isLoggedIn
        } = this.props;

        const { localError } = this.state;
        const error = localError || reduxError;

        return (
            <LoginPageComponent
                error={error}
                loading={loading}
                isLoggedIn={isLoggedIn}
                onLogin={this.handleLogin}
                onRegister={this.handleRegister}
            />
        );
    }
}

LoginPage.propTypes = {
    error: PropTypes.string,
    isLoggedIn: PropTypes.bool.isRequired,
    loading: PropTypes.bool.isRequired,
    onClearError: PropTypes.func.isRequired,
    onLogin: PropTypes.func.isRequired,
    onRegister: PropTypes.func.isRequired
};

const mapStateToProps = state => ({
    error: state.user.error,
    isLoggedIn: state.user.isLoggedIn,
    loading: state.user.loading
});

const mapDispatchToProps = dispatch => ({
    onLogin: (username, password) => dispatch(handleLogin(username, password)),
    onRegister: (username, password) => dispatch(handleRegister(username, password)),
    onClearError: () => dispatch(clearError())
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(LoginPage);
