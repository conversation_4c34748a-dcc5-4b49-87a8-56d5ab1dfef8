.loginForm {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    box-sizing: border-box;
}

.title {
    text-align: center;
    margin-bottom: 20px;
    color: #4C97FF;
}

.formGroup {
    margin-bottom: 15px;
}

.label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.input:focus {
    border-color: #4C97FF;
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 151, 255, 0.2);
}

.inputError {
    border-color: #FF6680;
    background-color: rgba(255, 102, 128, 0.05);
}

.inputError:focus {
    border-color: #FF6680;
    box-shadow: 0 0 0 2px rgba(255, 102, 128, 0.2);
}

.fieldError {
    color: #FF6680;
    font-size: 12px;
    margin-top: 4px;
    padding-left: 2px;
}

.buttonRow {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.loginButton {
    background-color: #4C97FF;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s, transform 0.2s;
}

.loginButton:hover {
    background-color: #3373CC;
    transform: translateY(-1px);
}

.loginButton:active {
    transform: translateY(1px);
}

.loginButton:disabled {
    background-color: #9DC6FF;
    cursor: not-allowed;
    transform: none;
}

.errorMessage {
    color: #FF6680;
    margin-bottom: 15px;
    text-align: center;
    padding: 8px;
    background-color: rgba(255, 102, 128, 0.1);
    border-radius: 4px;
}

.switchForm {
    margin-top: 15px;
    text-align: center;
}

.switchForm a {
    color: #4C97FF;
    text-decoration: none;
}

.switchForm a:hover {
    text-decoration: underline;
}
