import React from 'react';
import PropTypes from 'prop-types';
import {defineMessages, FormattedMessage, injectIntl, intlShape} from 'react-intl';
import classNames from 'classnames';

import styles from './login-form.css';
import Button from '../button/button.jsx';

const messages = defineMessages({
    username: {
        defaultMessage: '用户名',
        description: '登录表单中的用户名标签',
        id: 'gui.login.username'
    },
    password: {
        defaultMessage: '密码',
        description: '登录表单中的密码标签',
        id: 'gui.login.password'
    },
    login: {
        defaultMessage: '登录',
        description: '登录按钮文本',
        id: 'gui.login.login'
    },
    register: {
        defaultMessage: '注册',
        description: '注册按钮文本',
        id: 'gui.login.register'
    },
    goToRegister: {
        defaultMessage: '没有账号？去注册',
        description: '跳转到注册页面的链接文本',
        id: 'gui.login.goToRegister'
    },
    usernameRequired: {
        defaultMessage: '请输入用户名',
        description: '用户名必填提示',
        id: 'gui.login.usernameRequired'
    },
    passwordRequired: {
        defaultMessage: '请输入密码',
        description: '密码必填提示',
        id: 'gui.login.passwordRequired'
    },
    passwordTooShort: {
        defaultMessage: '密码长度不能少于6个字符',
        description: '密码长度提示',
        id: 'gui.login.passwordTooShort'
    }
});

class LoginForm extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            username: '',
            password: '',
            formErrors: {
                username: '',
                password: ''
            },
            formValid: false,
            formTouched: false
        };
        this.handleUsernameChange = this.handleUsernameChange.bind(this);
        this.handlePasswordChange = this.handlePasswordChange.bind(this);
        this.handleSubmit = this.handleSubmit.bind(this);
        this.validateField = this.validateField.bind(this);
        this.validateForm = this.validateForm.bind(this);
    }

    validateField (fieldName, value) {
        const {intl} = this.props;
        const formErrors = this.state.formErrors;

        switch (fieldName) {
        case 'username':
            formErrors.username = value.trim() === '' ?
                intl.formatMessage(messages.usernameRequired) : '';
            break;
        case 'password':
            formErrors.password = value === '' ?
                intl.formatMessage(messages.passwordRequired) : '';
            if (value !== '' && value.length < 6) {
                formErrors.password = intl.formatMessage(messages.passwordTooShort);
            }
            break;
        default:
            break;
        }

        this.setState({formErrors, formTouched: true}, this.validateForm);
    }

    validateForm () {
        const {username, password, formErrors} = this.state;
        const isValid = username.trim() !== '' &&
                       password !== '' &&
                       password.length >= 6 &&
                       formErrors.username === '' &&
                       formErrors.password === '';

        this.setState({formValid: isValid});
    }

    handleUsernameChange (e) {
        const name = e.target.name || 'username';
        const value = e.target.value;
        this.setState({username: value, formTouched: true},
            () => this.validateField(name, value));
    }

    handlePasswordChange (e) {
        const name = e.target.name || 'password';
        const value = e.target.value;
        this.setState({password: value, formTouched: true},
            () => this.validateField(name, value));
    }

    handleSubmit (e) {
        e.preventDefault();
        console.log('登录表单提交事件触发');

        // 再次验证表单
        const {username, password} = this.state;
        this.validateField('username', username);
        this.validateField('password', password);

        // 标记表单已触发提交
        this.setState({formTouched: true});

        // 如果表单有效，则提交
        if (this.state.formValid) {
            console.log('登录表单验证通过，准备调用onLogin', username, password);
            try {
                this.props.onLogin(username, password);
                console.log('onLogin调用成功');
            } catch (error) {
                console.error('登录调用失败:', error);
                alert(`登录失败: ${error.message || '未知错误'}`);
            }
        } else {
            // 显示错误信息
            console.log('登录表单验证失败');
            const errorMessages = [];
            
            if (this.state.formErrors.username) {
                errorMessages.push(this.state.formErrors.username);
            } else if (username.trim() === '') {
                errorMessages.push(this.props.intl.formatMessage(messages.usernameRequired));
            }
            
            if (this.state.formErrors.password) {
                errorMessages.push(this.state.formErrors.password);
            } else if (password === '') {
                errorMessages.push(this.props.intl.formatMessage(messages.passwordRequired));
            } else if (password.length < 6) {
                errorMessages.push(this.props.intl.formatMessage(messages.passwordTooShort));
            }
            
            if (errorMessages.length > 0) {
                alert(errorMessages.join('\n'));
            } else {
                alert('请正确填写所有必填字段');
            }
        }
    }

    render () {
        const {
            intl,
            error,
            onSwitchToRegister
        } = this.props;

        return (
            <form
                className={styles.loginForm}
                onSubmit={this.handleSubmit}
            >
                <h2 className={styles.title}>
                    <FormattedMessage {...messages.login} />
                </h2>

                {error && (
                    <div className={styles.errorMessage}>
                        {error}
                    </div>
                )}

                <div className={styles.formGroup}>
                    <label className={styles.label}>
                        <FormattedMessage {...messages.username} />
                    </label>
                    <input
                        className={classNames(styles.input, {
                            [styles.inputError]: this.state.formTouched && this.state.formErrors.username
                        })}
                        type="text"
                        name="username"
                        value={this.state.username}
                        onChange={this.handleUsernameChange}
                        onBlur={() => this.validateField('username', this.state.username)}
                        placeholder={intl.formatMessage(messages.username)}
                    />
                    {this.state.formTouched && this.state.formErrors.username && (
                        <div className={styles.fieldError}>
                            {this.state.formErrors.username}
                        </div>
                    )}
                </div>

                <div className={styles.formGroup}>
                    <label className={styles.label}>
                        <FormattedMessage {...messages.password} />
                    </label>
                    <input
                        className={classNames(styles.input, {
                            [styles.inputError]: this.state.formTouched && this.state.formErrors.password
                        })}
                        type="password"
                        name="password"
                        value={this.state.password}
                        onChange={this.handlePasswordChange}
                        onBlur={() => this.validateField('password', this.state.password)}
                        placeholder={intl.formatMessage(messages.password)}
                    />
                    {this.state.formTouched && this.state.formErrors.password && (
                        <div className={styles.fieldError}>
                            {this.state.formErrors.password}
                        </div>
                    )}
                </div>

                <div className={styles.buttonRow}>
                    <Button
                        className={styles.loginButton}
                        type="submit"
                        onClick={(e) => {
                            console.log('登录按钮被点击');
                            // 点击按钮时不做特殊处理，让表单的onSubmit事件处理程序处理
                        }}
                    >
                        <FormattedMessage {...messages.login} />
                    </Button>
                </div>

                <div className={styles.switchForm}>
                    <a
                        href="#"
                        onClick={e => {
                            e.preventDefault();
                            onSwitchToRegister();
                        }}
                    >
                        <FormattedMessage {...messages.goToRegister} />
                    </a>
                </div>
            </form>
        );
    }
}

LoginForm.propTypes = {
    intl: intlShape.isRequired,
    error: PropTypes.string,
    onLogin: PropTypes.func.isRequired,
    onSwitchToRegister: PropTypes.func.isRequired
};

export default injectIntl(LoginForm);
