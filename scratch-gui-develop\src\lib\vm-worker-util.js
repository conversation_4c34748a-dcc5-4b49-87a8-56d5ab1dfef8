/**
 * VM Worker 工具类
 * 提供在Web Worker中处理K1扩展脚本加载的功能
 */

/**
 * 创建一个可注入到Worker中的脚本，用于劫持importScripts函数，防止加载K1扩展脚本失败
 * @param {string} baseUrl - Worker脚本的基础URL，用于解析相对路径
 * @returns {string} 可注入的脚本内容
 */
const createWorkerScript = (baseUrl) => {
    return `
        (function() {
            // 保存原始的importScripts函数
            const originalImportScripts = self.importScripts;
            
            // 记录Worker的基础URL，用于解析相对路径
            const baseUrl = "${baseUrl || ''}";
            
            // 创建一个虚拟的K1扩展脚本
            const createK1Script = function() {
                return \`
                    // K1扩展模拟脚本
                    class K1Extension {
                        constructor(runtime) {
                            this.runtime = runtime;
                        }
                        
                        getInfo() {
                            return {
                                id: 'k1',
                                name: 'K1机器人',
                                blocks: []
                            };
                        }
                    }
                    
                    // 将扩展注册到Scratch
                    if (typeof Scratch !== 'undefined') {
                        Scratch.extensions.register(new K1Extension());
                    }
                \`;
            };
            
            // 辅助函数：解析相对URL
            const resolveUrl = function(url) {
                if (url.startsWith('./') || url.startsWith('../')) {
                    // 这是一个相对URL，需要基于Worker的基础URL来解析
                    const a = new URL(url, baseUrl);
                    return a.href;
                }
                return url;
            };
            
            // 重写importScripts函数
            self.importScripts = function() {
                try {
                    const scripts = Array.from(arguments);
                    const filteredScripts = [];
                    
                    // 过滤和处理脚本URL
                    for (let i = 0; i < scripts.length; i++) {
                        let script = scripts[i];
                        
                        // 解析相对URL
                        script = resolveUrl(script);
                        
                        if (typeof script === 'string' && (
                            script.includes('/k1') || 
                            script.includes('k1.js')
                        )) {
                            console.log('[Worker] 拦截K1脚本加载:', script);
                            // 直接执行K1虚拟脚本
                            eval(createK1Script());
                        } else {
                            filteredScripts.push(script);
                        }
                    }
                    
                    // 如果还有其他脚本，调用原始的importScripts
                    if (filteredScripts.length > 0) {
                        return originalImportScripts.apply(self, filteredScripts);
                    }
                } catch (error) {
                    console.error('[Worker] importScripts错误:', error);
                    // 对于K1脚本错误，我们不抛出异常，而是静默处理
                    if (error.message && error.message.includes('k1')) {
                        console.log('[Worker] 忽略K1脚本加载错误');
                        return;
                    }
                    throw error;
                }
            };
        })();
    `;
};

/**
 * 创建一个Blob URL，包含K1脚本处理代码
 * @returns {string} Blob URL
 */
const createK1WorkerScriptURL = () => {
    const blob = new Blob([createWorkerScript()], { type: 'application/javascript' });
    return URL.createObjectURL(blob);
};

/**
 * 在页面加载时插入一个处理K1脚本加载的脚本
 * 这个函数应该在应用程序启动时调用
 */
const setupK1WorkerPatch = () => {
    // 只在浏览器环境中执行
    if (typeof document === 'undefined') return;
    
    console.log('设置K1 Worker补丁...');
    
    try {
        // 创建一个包含K1脚本处理代码的脚本元素
        const script = document.createElement('script');
        script.textContent = `
            // 劫持Worker构造函数
            (function() {
                // 保存原始构造函数
                const originalWorker = window.Worker;
                
                // 劫持Worker构造函数
                window.Worker = function(url, options) {
                    try {
                        // 获取完整的URL
                        const workerUrl = new URL(url, window.location.href).href;
                        // 获取Worker URL的基础路径（用于解析相对导入）
                        const basePath = workerUrl.substring(0, workerUrl.lastIndexOf('/') + 1);
                        
                        // 创建补丁脚本
                        const workerPatchTemplate = ${JSON.stringify(createWorkerScript('PLACEHOLDER_BASE_URL'))};
                        const patchScript = workerPatchTemplate.replace('"PLACEHOLDER_BASE_URL"', JSON.stringify(basePath));
                        
                        // 直接导入原始Worker脚本，而不是使用importScripts
                        const workerCode = \`
                            // K1 Worker补丁
                            \${patchScript}
                            
                            // 使用正确的上下文导入Worker脚本
                            try {
                                importScripts('\${workerUrl}');
                            } catch (e) {
                                console.error('[K1 Worker] 加载Worker脚本失败:', e);
                                // 尝试使用相对路径再次加载
                                try {
                                    const fileName = '\${url}'.split('/').pop();
                                    importScripts(fileName);
                                } catch (e2) {
                                    console.error('[K1 Worker] 第二次尝试加载Worker脚本失败:', e2);
                                    throw e;
                                }
                            }
                        \`;
                        
                        const blob = new Blob([workerCode], { type: 'application/javascript' });
                        const blobUrl = URL.createObjectURL(blob);
                        
                        // 使用修改后的URL创建Worker
                        return new originalWorker(blobUrl, options);
                    } catch (e) {
                        console.error('[K1 Worker] 创建Worker失败，使用原始方法:', e);
                        // 如果出错，使用原始方法创建Worker
                        return new originalWorker(url, options);
                    }
                };
            })();
        `;
        
        // 将脚本添加到页面
        document.head.appendChild(script);
        console.log('K1 Worker补丁已设置');
    } catch (e) {
        console.error('设置K1 Worker补丁失败:', e);
    }
};

export {
    setupK1WorkerPatch,
    createK1WorkerScriptURL
}; 