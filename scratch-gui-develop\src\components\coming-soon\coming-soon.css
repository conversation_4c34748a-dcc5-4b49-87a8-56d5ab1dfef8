/*
 * NOTE: the copious use of `important` is needed to overwrite
 * the default tooltip styling, and is required by the 3rd party
 * library being used, `react-tooltip`
 */

@import "../../css/colors.css";
@import "../../css/units.css";
@import "../../css/z-index.css";

.coming-soon {
    background-color: $data-primary !important;
    border: 1px solid $ui-black-transparent !important;
    border-radius: $form-radius !important;
    box-shadow: 0 0 .5rem $ui-black-transparent !important;
    padding: .75rem 1rem !important;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
    font-size: 1rem !important;
    line-height: 1.25rem !important;
    z-index: $z-index-tooltip !important;
}

.coming-soon:after {
    content: "";
    border-top: 1px solid $ui-black-transparent !important;
    border-left: 0 !important;
    border-bottom: 0 !important;
    border-right: 1px solid $ui-black-transparent !important;
    border-radius: $form-radius;
    background-color: $data-primary !important;
    height: 1rem !important;
    width: 1rem !important;
}

.show,
.show:before,
.show:after {
    opacity: 1 !important;
}

.left:after {
    margin-top: -.5rem !important;
    right: -.5rem !important;
    transform: rotate(45deg) !important;
}

.right:after {
    margin-top: -.5rem !important;
    left: -.5rem !important;
    transform: rotate(-135deg) !important;
}

.top:after {
    margin-right: -.5rem !important;
    bottom: -.5rem !important;
    transform: rotate(135deg) !important;
}

.bottom:after {
    margin-left: -.5rem !important;
    top: -.5rem !important;
    transform: rotate(-45deg) !important;
}

.coming-soon-image {
    width: 1.25rem;
    height: 1.25rem;
    vertical-align: middle;
}

[dir="ltr"] .coming-soon-image {
    margin-left: .125rem;
}

[dir="rtl"] .coming-soon-image {
    margin-right: .125rem;
}
