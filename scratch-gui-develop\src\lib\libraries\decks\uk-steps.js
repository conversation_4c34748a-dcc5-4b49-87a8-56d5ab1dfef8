// Intro
import introMove from './steps/intro-1-move.uk.gif';
import introSay from './steps/intro-2-say.uk.gif';
import introGreenFlag from './steps/intro-3-green-flag.uk.gif';

// Text to Speech
import speechAddExtension from './steps/speech-add-extension.uk.gif';
import speechSaySomething from './steps/speech-say-something.uk.png';
import speechSetVoice from './steps/speech-set-voice.uk.png';
import speechMoveAround from './steps/speech-move-around.uk.png';
import speechAddBackdrop from './steps/pick-backdrop.LTR.gif';
import speechAddSprite from './steps/speech-add-sprite.LTR.gif';
import speechSong from './steps/speech-song.uk.png';
import speechChangeColor from './steps/speech-change-color.uk.png';
import speechSpin from './steps/speech-spin.uk.png';
import speechGrowShrink from './steps/speech-grow-shrink.uk.png';

// Cartoon Network
import cnShowCharacter from './steps/cn-show-character.LTR.gif';
import cnSay from './steps/cn-say.uk.png';
import cnGlide from './steps/cn-glide.uk.png';
import cnPickSprite from './steps/cn-pick-sprite.LTR.gif';
import cnCollect from './steps/cn-collect.uk.png';
import cnVariable from './steps/add-variable.uk.gif';
import cnScore from './steps/cn-score.uk.png';
import cnBackdrop from './steps/cn-backdrop.uk.png';

// Add sprite
import addSprite from './steps/add-sprite.LTR.gif';

// Animate a name
import namePickLetter from './steps/name-pick-letter.LTR.gif';
import namePlaySound from './steps/name-play-sound.uk.png';
import namePickLetter2 from './steps/name-pick-letter2.LTR.gif';
import nameChangeColor from './steps/name-change-color.uk.png';
import nameSpin from './steps/name-spin.uk.png';
import nameGrow from './steps/name-grow.uk.png';

// Make Music
import musicPickInstrument from './steps/music-pick-instrument.LTR.gif';
import musicPlaySound from './steps/music-play-sound.uk.png';
import musicMakeSong from './steps/music-make-song.uk.png';
import musicMakeBeat from './steps/music-make-beat.uk.png';
import musicMakeBeatbox from './steps/music-make-beatbox.uk.png';

// Chase-Game
import chaseGameAddBackdrop from './steps/chase-game-add-backdrop.LTR.gif';
import chaseGameAddSprite1 from './steps/chase-game-add-sprite1.LTR.gif';
import chaseGameRightLeft from './steps/chase-game-right-left.uk.png';
import chaseGameUpDown from './steps/chase-game-up-down.uk.png';
import chaseGameAddSprite2 from './steps/chase-game-add-sprite2.LTR.gif';
import chaseGameMoveRandomly from './steps/chase-game-move-randomly.uk.png';
import chaseGamePlaySound from './steps/chase-game-play-sound.uk.png';
import chaseGameAddVariable from './steps/add-variable.uk.gif';
import chaseGameChangeScore from './steps/chase-game-change-score.uk.png';

// Clicker-Game (Pop Game)
import popGamePickSprite from './steps/pop-game-pick-sprite.LTR.gif';
import popGamePlaySound from './steps/pop-game-play-sound.uk.png';
import popGameAddScore from './steps/add-variable.uk.gif';
import popGameChangeScore from './steps/pop-game-change-score.uk.png';
import popGameRandomPosition from './steps/pop-game-random-position.uk.png';
import popGameChangeColor from './steps/pop-game-change-color.uk.png';
import popGameResetScore from './steps/pop-game-reset-score.uk.png';

// Animate A Character
import animateCharPickBackdrop from './steps/pick-backdrop.LTR.gif';
import animateCharPickSprite from './steps/animate-char-pick-sprite.LTR.gif';
import animateCharSaySomething from './steps/animate-char-say-something.uk.png';
import animateCharAddSound from './steps/animate-char-add-sound.uk.png';
import animateCharTalk from './steps/animate-char-talk.uk.png';
import animateCharMove from './steps/animate-char-move.uk.png';
import animateCharJump from './steps/animate-char-jump.uk.png';
import animateCharChangeColor from './steps/animate-char-change-color.uk.png';

// Tell A Story
import storyPickBackdrop from './steps/story-pick-backdrop.LTR.gif';
import storyPickSprite from './steps/story-pick-sprite.LTR.gif';
import storySaySomething from './steps/story-say-something.uk.png';
import storyPickSprite2 from './steps/story-pick-sprite2.LTR.gif';
import storyFlip from './steps/story-flip.uk.gif';
import storyConversation from './steps/story-conversation.uk.png';
import storyPickBackdrop2 from './steps/story-pick-backdrop2.LTR.gif';
import storySwitchBackdrop from './steps/story-switch-backdrop.uk.png';
import storyHideCharacter from './steps/story-hide-character.uk.png';
import storyShowCharacter from './steps/story-show-character.uk.png';

// Video Sensing
import videoAddExtension from './steps/video-add-extension.uk.gif';
import videoPet from './steps/video-pet.uk.png';
import videoAnimate from './steps/video-animate.uk.png';
import videoPop from './steps/video-pop.uk.png';

// Make it Fly
import flyChooseBackdrop from './steps/fly-choose-backdrop.LTR.gif';
import flyChooseCharacter from './steps/fly-choose-character.LTR.png';
import flySaySomething from './steps/fly-say-something.uk.png';
import flyMoveArrows from './steps/fly-make-interactive.uk.png';
import flyChooseObject from './steps/fly-object-to-collect.LTR.png';
import flyFlyingObject from './steps/fly-flying-heart.uk.png';
import flySelectFlyingSprite from './steps/fly-select-flyer.LTR.png';
import flyAddScore from './steps/add-variable.uk.gif';
import flyKeepScore from './steps/fly-keep-score.uk.png';
import flyAddScenery from './steps/fly-choose-scenery.LTR.gif';
import flyMoveScenery from './steps/fly-move-scenery.uk.png';
import flySwitchLooks from './steps/fly-switch-costume.uk.png';

// Pong
import pongAddBackdrop from './steps/pong-add-backdrop.LTR.png';
import pongAddBallSprite from './steps/pong-add-ball-sprite.LTR.png';
import pongBounceAround from './steps/pong-bounce-around.uk.png';
import pongAddPaddle from './steps/pong-add-a-paddle.LTR.gif';
import pongMoveThePaddle from './steps/pong-move-the-paddle.uk.png';
import pongSelectBallSprite from './steps/pong-select-ball.LTR.png';
import pongAddMoreCodeToBall from './steps/pong-add-code-to-ball.uk.png';
import pongAddAScore from './steps/add-variable.uk.gif';
import pongChooseScoreFromMenu from './steps/pong-choose-score.uk.png';
import pongInsertChangeScoreBlock from './steps/pong-insert-change-score.uk.png';
import pongResetScore from './steps/pong-reset-score.uk.png';
import pongAddLineSprite from './steps/pong-add-line.LTR.gif';
import pongGameOver from './steps/pong-game-over.uk.png';

// Imagine a World
import imagineTypeWhatYouWant from './steps/imagine-type-what-you-want.uk.png';
import imagineClickGreenFlag from './steps/imagine-click-green-flag.uk.png';
import imagineChooseBackdrop from './steps/imagine-choose-backdrop.LTR.png';
import imagineChooseSprite from './steps/imagine-choose-any-sprite.LTR.png';
import imagineFlyAround from './steps/imagine-fly-around.uk.png';
import imagineChooseAnotherSprite from './steps/imagine-choose-another-sprite.LTR.png';
import imagineLeftRight from './steps/imagine-left-right.uk.png';
import imagineUpDown from './steps/imagine-up-down.uk.png';
import imagineChangeCostumes from './steps/imagine-change-costumes.uk.png';
import imagineGlideToPoint from './steps/imagine-glide-to-point.uk.png';
import imagineGrowShrink from './steps/imagine-grow-shrink.uk.png';
import imagineChooseAnotherBackdrop from './steps/imagine-choose-another-backdrop.LTR.png';
import imagineSwitchBackdrops from './steps/imagine-switch-backdrops.uk.png';
import imagineRecordASound from './steps/imagine-record-a-sound.uk.gif';
import imagineChooseSound from './steps/imagine-choose-sound.uk.png';

// Add a Backdrop
import addBackdrop from './steps/add-backdrop.LTR.png';

// Add Effects
import addEffects from './steps/add-effects.uk.png';

// Hide and Show
import hideAndShow from './steps/hide-show.uk.png';

// Switch Costumes
import switchCostumes from './steps/switch-costumes.uk.png';

// Change Size
import changeSize from './steps/change-size.uk.png';

// Spin
import spinTurn from './steps/spin-turn.uk.png';
import spinPointInDirection from './steps/spin-point-in-direction.uk.png';

// Record a Sound
import recordASoundSoundsTab from './steps/record-a-sound-sounds-tab.uk.png';
import recordASoundClickRecord from './steps/record-a-sound-click-record.uk.png';
import recordASoundPressRecordButton from './steps/record-a-sound-press-record-button.uk.png';
import recordASoundChooseSound from './steps/record-a-sound-choose-sound.uk.png';
import recordASoundPlayYourSound from './steps/record-a-sound-play-your-sound.uk.png';

// Use Arrow Keys
import moveArrowKeysLeftRight from './steps/move-arrow-keys-left-right.uk.png';
import moveArrowKeysUpDown from './steps/move-arrow-keys-up-down.uk.png';

// Glide Around
import glideAroundBackAndForth from './steps/glide-around-back-and-forth.uk.png';
import glideAroundPoint from './steps/glide-around-point.uk.png';

// Code a Cartoon
import codeCartoonSaySomething from './steps/code-cartoon-01-say-something.uk.png';
import codeCartoonAnimate from './steps/code-cartoon-02-animate.uk.png';
import codeCartoonSelectDifferentCharacter from './steps/code-cartoon-03-select-different-character.LTR.png';
import codeCartoonUseMinusSign from './steps/code-cartoon-04-use-minus-sign.uk.png';
import codeCartoonGrowShrink from './steps/code-cartoon-05-grow-shrink.uk.png';
import codeCartoonSelectDifferentCharacter2 from './steps/code-cartoon-06-select-another-different-character.LTR.png';
import codeCartoonJump from './steps/code-cartoon-07-jump.uk.png';
import codeCartoonChangeScenes from './steps/code-cartoon-08-change-scenes.uk.png';
import codeCartoonGlideAround from './steps/code-cartoon-09-glide-around.uk.png';
import codeCartoonChangeCostumes from './steps/code-cartoon-10-change-costumes.uk.png';
import codeCartoonChooseMoreCharacters from './steps/code-cartoon-11-choose-more-characters.LTR.png';

// Talking Tales
import talesAddExtension from './steps/speech-add-extension.uk.gif';
import talesChooseSprite from './steps/talking-2-choose-sprite.LTR.png';
import talesSaySomething from './steps/talking-3-say-something.uk.png';
import talesChooseBackdrop from './steps/talking-4-choose-backdrop.LTR.png';
import talesSwitchBackdrop from './steps/talking-5-switch-backdrop.uk.png';
import talesChooseAnotherSprite from './steps/talking-6-choose-another-sprite.LTR.png';
import talesMoveAround from './steps/talking-7-move-around.uk.png';
import talesChooseAnotherBackdrop from './steps/talking-8-choose-another-backdrop.LTR.png';
import talesAnimateTalking from './steps/talking-9-animate.uk.png';
import talesChooseThirdBackdrop from './steps/talking-10-choose-third-backdrop.LTR.png';
import talesChooseSound from './steps/talking-11-choose-sound.uk.gif';
import talesDanceMoves from './steps/talking-12-dance-moves.uk.png';
import talesAskAnswer from './steps/talking-13-ask-and-answer.uk.png';

const ukImages = {
    // Intro
    introMove: introMove,
    introSay: introSay,
    introGreenFlag: introGreenFlag,

    // Text to Speech
    speechAddExtension: speechAddExtension,
    speechSaySomething: speechSaySomething,
    speechSetVoice: speechSetVoice,
    speechMoveAround: speechMoveAround,
    speechAddBackdrop: speechAddBackdrop,
    speechAddSprite: speechAddSprite,
    speechSong: speechSong,
    speechChangeColor: speechChangeColor,
    speechSpin: speechSpin,
    speechGrowShrink: speechGrowShrink,

    // Cartoon Network
    cnShowCharacter: cnShowCharacter,
    cnSay: cnSay,
    cnGlide: cnGlide,
    cnPickSprite: cnPickSprite,
    cnCollect: cnCollect,
    cnVariable: cnVariable,
    cnScore: cnScore,
    cnBackdrop: cnBackdrop,

    // Add sprite
    addSprite: addSprite,

    // Animate a name
    namePickLetter: namePickLetter,
    namePlaySound: namePlaySound,
    namePickLetter2: namePickLetter2,
    nameChangeColor: nameChangeColor,
    nameSpin: nameSpin,
    nameGrow: nameGrow,

    // Make-Music
    musicPickInstrument: musicPickInstrument,
    musicPlaySound: musicPlaySound,
    musicMakeSong: musicMakeSong,
    musicMakeBeat: musicMakeBeat,
    musicMakeBeatbox: musicMakeBeatbox,

    // Chase-Game
    chaseGameAddBackdrop: chaseGameAddBackdrop,
    chaseGameAddSprite1: chaseGameAddSprite1,
    chaseGameRightLeft: chaseGameRightLeft,
    chaseGameUpDown: chaseGameUpDown,
    chaseGameAddSprite2: chaseGameAddSprite2,
    chaseGameMoveRandomly: chaseGameMoveRandomly,
    chaseGamePlaySound: chaseGamePlaySound,
    chaseGameAddVariable: chaseGameAddVariable,
    chaseGameChangeScore: chaseGameChangeScore,

    // Make-A-Pop/Clicker Game
    popGamePickSprite: popGamePickSprite,
    popGamePlaySound: popGamePlaySound,
    popGameAddScore: popGameAddScore,
    popGameChangeScore: popGameChangeScore,
    popGameRandomPosition: popGameRandomPosition,
    popGameChangeColor: popGameChangeColor,
    popGameResetScore: popGameResetScore,

    // Animate A Character
    animateCharPickBackdrop: animateCharPickBackdrop,
    animateCharPickSprite: animateCharPickSprite,
    animateCharSaySomething: animateCharSaySomething,
    animateCharAddSound: animateCharAddSound,
    animateCharTalk: animateCharTalk,
    animateCharMove: animateCharMove,
    animateCharJump: animateCharJump,
    animateCharChangeColor: animateCharChangeColor,

    // Tell A Story
    storyPickBackdrop: storyPickBackdrop,
    storyPickSprite: storyPickSprite,
    storySaySomething: storySaySomething,
    storyPickSprite2: storyPickSprite2,
    storyFlip: storyFlip,
    storyConversation: storyConversation,
    storyPickBackdrop2: storyPickBackdrop2,
    storySwitchBackdrop: storySwitchBackdrop,
    storyHideCharacter: storyHideCharacter,
    storyShowCharacter: storyShowCharacter,

    // Video Sensing
    videoAddExtension: videoAddExtension,
    videoPet: videoPet,
    videoAnimate: videoAnimate,
    videoPop: videoPop,

    // Make it Fly
    flyChooseBackdrop: flyChooseBackdrop,
    flyChooseCharacter: flyChooseCharacter,
    flySaySomething: flySaySomething,
    flyMoveArrows: flyMoveArrows,
    flyChooseObject: flyChooseObject,
    flyFlyingObject: flyFlyingObject,
    flySelectFlyingSprite: flySelectFlyingSprite,
    flyAddScore: flyAddScore,
    flyKeepScore: flyKeepScore,
    flyAddScenery: flyAddScenery,
    flyMoveScenery: flyMoveScenery,
    flySwitchLooks: flySwitchLooks,

    // Pong
    pongAddBackdrop: pongAddBackdrop,
    pongAddBallSprite: pongAddBallSprite,
    pongBounceAround: pongBounceAround,
    pongAddPaddle: pongAddPaddle,
    pongMoveThePaddle: pongMoveThePaddle,
    pongSelectBallSprite: pongSelectBallSprite,
    pongAddMoreCodeToBall: pongAddMoreCodeToBall,
    pongAddAScore: pongAddAScore,
    pongChooseScoreFromMenu: pongChooseScoreFromMenu,
    pongInsertChangeScoreBlock: pongInsertChangeScoreBlock,
    pongResetScore: pongResetScore,
    pongAddLineSprite: pongAddLineSprite,
    pongGameOver: pongGameOver,

    // Imagine a World
    imagineTypeWhatYouWant: imagineTypeWhatYouWant,
    imagineClickGreenFlag: imagineClickGreenFlag,
    imagineChooseBackdrop: imagineChooseBackdrop,
    imagineChooseSprite: imagineChooseSprite,
    imagineFlyAround: imagineFlyAround,
    imagineChooseAnotherSprite: imagineChooseAnotherSprite,
    imagineLeftRight: imagineLeftRight,
    imagineUpDown: imagineUpDown,
    imagineChangeCostumes: imagineChangeCostumes,
    imagineGlideToPoint: imagineGlideToPoint,
    imagineGrowShrink: imagineGrowShrink,
    imagineChooseAnotherBackdrop: imagineChooseAnotherBackdrop,
    imagineSwitchBackdrops: imagineSwitchBackdrops,
    imagineRecordASound: imagineRecordASound,
    imagineChooseSound: imagineChooseSound,

    // Add a Backdrop
    addBackdrop: addBackdrop,

    // Add Effects
    addEffects: addEffects,

    // Hide and Show
    hideAndShow: hideAndShow,

    // Switch Costumes
    switchCostumes: switchCostumes,

    // Change Size
    changeSize: changeSize,

    // Spin
    spinTurn: spinTurn,
    spinPointInDirection: spinPointInDirection,

    // Record a Sound
    recordASoundSoundsTab: recordASoundSoundsTab,
    recordASoundClickRecord: recordASoundClickRecord,
    recordASoundPressRecordButton: recordASoundPressRecordButton,
    recordASoundChooseSound: recordASoundChooseSound,
    recordASoundPlayYourSound: recordASoundPlayYourSound,

    // Use Arrow Keys
    moveArrowKeysLeftRight: moveArrowKeysLeftRight,
    moveArrowKeysUpDown: moveArrowKeysUpDown,

    // Glide Around
    glideAroundBackAndForth: glideAroundBackAndForth,
    glideAroundPoint: glideAroundPoint,

    // Code a Cartoon
    codeCartoonSaySomething: codeCartoonSaySomething,
    codeCartoonAnimate: codeCartoonAnimate,
    codeCartoonSelectDifferentCharacter: codeCartoonSelectDifferentCharacter,
    codeCartoonUseMinusSign: codeCartoonUseMinusSign,
    codeCartoonGrowShrink: codeCartoonGrowShrink,
    codeCartoonSelectDifferentCharacter2: codeCartoonSelectDifferentCharacter2,
    codeCartoonJump: codeCartoonJump,
    codeCartoonChangeScenes: codeCartoonChangeScenes,
    codeCartoonGlideAround: codeCartoonGlideAround,
    codeCartoonChangeCostumes: codeCartoonChangeCostumes,
    codeCartoonChooseMoreCharacters: codeCartoonChooseMoreCharacters,

    // Talking Tales
    talesAddExtension: talesAddExtension,
    talesChooseSprite: talesChooseSprite,
    talesSaySomething: talesSaySomething,
    talesAskAnswer: talesAskAnswer,
    talesChooseBackdrop: talesChooseBackdrop,
    talesSwitchBackdrop: talesSwitchBackdrop,
    talesChooseAnotherSprite: talesChooseAnotherSprite,
    talesMoveAround: talesMoveAround,
    talesChooseAnotherBackdrop: talesChooseAnotherBackdrop,
    talesAnimateTalking: talesAnimateTalking,
    talesChooseThirdBackdrop: talesChooseThirdBackdrop,
    talesChooseSound: talesChooseSound,
    talesDanceMoves: talesDanceMoves
};

export {ukImages};
