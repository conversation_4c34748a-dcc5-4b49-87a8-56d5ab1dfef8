import React from 'react';
import PropTypes from 'prop-types';
import {FormattedMessage} from 'react-intl';
import classNames from 'classnames';
import Box from '../box/box.jsx';
import Modal from '../../containers/modal.jsx';

import LoginForm from './login-form.jsx';
import RegisterForm from './register-form.jsx';

import styles from './login-modal.css';

class LoginModal extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            showLogin: true // 默认显示登录表单
        };
        this.handleSwitchToRegister = this.handleSwitchToRegister.bind(this);
        this.handleSwitchToLogin = this.handleSwitchToLogin.bind(this);
    }

    handleSwitchToRegister () {
        this.setState({showLogin: false});
    }

    handleSwitchToLogin () {
        this.setState({showLogin: true});
    }

    render () {
        const {
            isOpen,
            onClose,
            onLogin,
            onRegister,
            error
        } = this.props;

        return (
            <Modal
                className={styles.modalContent}
                contentLabel={this.state.showLogin ? '登录' : '注册'}
                isOpen={isOpen}
                onRequestClose={onClose}
            >
                <Box className={styles.container}>
                    {this.state.showLogin ? (
                        <LoginForm
                            error={error}
                            onLogin={onLogin}
                            onSwitchToRegister={this.handleSwitchToRegister}
                        />
                    ) : (
                        <RegisterForm
                            error={error}
                            onRegister={onRegister}
                            onSwitchToLogin={this.handleSwitchToLogin}
                        />
                    )}
                </Box>
            </Modal>
        );
    }
}

LoginModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    error: PropTypes.string,
    onClose: PropTypes.func.isRequired,
    onLogin: PropTypes.func.isRequired,
    onRegister: PropTypes.func.isRequired
};

export default LoginModal;
