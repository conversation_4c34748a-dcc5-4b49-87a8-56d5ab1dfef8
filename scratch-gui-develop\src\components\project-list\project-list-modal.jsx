import React from 'react';
import PropTypes from 'prop-types';
import {FormattedMessage} from 'react-intl';
import classNames from 'classnames';
import Box from '../box/box.jsx';
import Modal from '../../containers/modal.jsx';
import Spinner from '../spinner/spinner.jsx';

import styles from './project-list-modal.css';

class ProjectListModal extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            loading: true,
            projects: []
        };
        this.handleOpenProject = this.handleOpenProject.bind(this);
        this.handleDeleteProject = this.handleDeleteProject.bind(this);
    }

    componentDidMount () {
        // 当组件挂载时，加载项目列表
        if (this.props.isOpen) {
            this.loadProjects();
        }
    }

    componentDidUpdate (prevProps) {
        // 当模态框打开时，加载项目列表
        if (!prevProps.isOpen && this.props.isOpen) {
            this.loadProjects();
        }
    }

    loadProjects () {
        this.setState({loading: true});

        // 调用API获取项目列表
        this.props.onFetchProjects()
            .then(projects => {
                this.setState({
                    projects: projects || [],
                    loading: false
                });
            })
            .catch(error => {
                console.error('获取项目列表失败:', error);
                this.setState({
                    loading: false,
                    projects: []
                });
            });
    }

    handleOpenProject (projectId) {
        this.props.onOpenProject(projectId);
        this.props.onClose();
    }

    handleDeleteProject (projectId, projectName) {
        // 确认删除
        if (window.confirm(`确定要删除项目"${projectName}"吗？此操作不可恢复。`)) {
            // 调用删除项目API
            this.props.onDeleteProject(projectId)
                .then(() => {
                    // 删除成功后重新加载项目列表
                    this.loadProjects();
                })
                .catch(error => {
                    console.error('删除项目失败:', error);
                    alert('删除项目失败: ' + (error.message || '未知错误'));
                });
        }
    }

    render () {
        const {
            isOpen,
            onClose
        } = this.props;

        const {
            loading,
            projects
        } = this.state;

        return (
            <Modal
                className={styles.modalContent}
                contentLabel={<FormattedMessage
                    defaultMessage="我的项目"
                    description="项目列表模态框标题"
                    id="gui.projectListModal.title"
                />}
                isOpen={isOpen}
                onRequestClose={onClose}
            >
                <Box className={styles.container}>
                    <div className={styles.header}>
                        <h2>
                            <FormattedMessage
                                defaultMessage="我的项目"
                                description="项目列表模态框标题"
                                id="gui.projectListModal.title"
                            />
                        </h2>
                    </div>
                    <div className={styles.body}>
                        {loading ? (
                            <div className={styles.spinnerWrapper}>
                                <Spinner />
                            </div>
                        ) : (
                            <div className={styles.projectList}>
                                {projects.length === 0 ? (
                                    <div className={styles.emptyMessage}>
                                        <FormattedMessage
                                            defaultMessage="暂无项目"
                                            description="暂无项目提示"
                                            id="gui.projectListModal.empty"
                                        />
                                    </div>
                                ) : (
                                    <table className={styles.projectTable}>
                                        <thead>
                                            <tr>
                                                <th className={styles.headerCell}>
                                                    <div className={styles.headerContent}>
                                                        <span className={styles.headerIcon}>📄</span>
                                                        <FormattedMessage
                                                            defaultMessage="项目名称"
                                                            description="项目名称列标题"
                                                            id="gui.projectListModal.projectName"
                                                        />
                                                    </div>
                                                </th>
                                                <th className={styles.headerCell}>
                                                    <div className={styles.headerContent}>
                                                        <span className={styles.headerIcon}>🕒</span>
                                                        <FormattedMessage
                                                            defaultMessage="创建时间"
                                                            description="创建时间列标题"
                                                            id="gui.projectListModal.createdAt"
                                                        />
                                                    </div>
                                                </th>
                                                <th className={styles.headerCell}>
                                                    <div className={styles.headerContent}>
                                                        <span className={styles.headerIcon}>🏷️</span>
                                                        <FormattedMessage
                                                            defaultMessage="版本号"
                                                            description="版本号列标题"
                                                            id="gui.projectListModal.version"
                                                        />
                                                    </div>
                                                </th>
                                                <th className={styles.headerCell}>
                                                    <div className={styles.headerContent}>
                                                        <span className={styles.headerIcon}>⚙️</span>
                                                        <FormattedMessage
                                                            defaultMessage="操作"
                                                            description="操作列标题"
                                                            id="gui.projectListModal.actions"
                                                        />
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {projects.map(project => (
                                                <tr key={project.projectId} className={styles.projectRow}>
                                                    <td className={styles.projectName}>{project.projectName}</td>
                                                    <td className={styles.projectDate}>
                                                        {new Date(project.projectCreatedAt).toLocaleString()}
                                                    </td>
                                                    <td className={styles.projectVersion}>
                                                        {project.versionNumber || 'v1.0'}
                                                    </td>
                                                    <td className={styles.projectActions}>
                                                        <div className={styles.actionButtonGroup}>
                                                            <button
                                                                className={styles.actionButton}
                                                                onClick={() => this.handleOpenProject(project.projectId)}
                                                            >
                                                                <FormattedMessage
                                                                    defaultMessage="打开"
                                                                    description="打开项目按钮"
                                                                    id="gui.projectListModal.open"
                                                                />
                                                            </button>
                                                            <button
                                                                className={`${styles.actionButton} ${styles.deleteButton}`}
                                                                onClick={() => this.handleDeleteProject(project.projectId, project.projectName)}
                                                            >
                                                                <FormattedMessage
                                                                    defaultMessage="删除"
                                                                    description="删除项目按钮"
                                                                    id="gui.projectListModal.delete"
                                                                />
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                )}
                            </div>
                        )}
                    </div>
                </Box>
            </Modal>
        );
    }
}

ProjectListModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    onFetchProjects: PropTypes.func.isRequired,
    onOpenProject: PropTypes.func.isRequired,
    onDeleteProject: PropTypes.func.isRequired
};

export default ProjectListModal;
