<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C代码集成测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .code-area {
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
            min-height: 200px;
            border: 1px solid #ddd;
        }
        .output {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            min-height: 150px;
            margin: 10px 0;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 300px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .two-column {
            display: flex;
            gap: 20px;
        }
        .column {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>C代码集成测试</h1>
        <p>这个页面测试从积木块生成C代码，然后使用picoc-js执行的完整流程</p>

        <div id="status" class="status info">正在初始化...</div>

        <div class="two-column">
            <div class="column">
                <div class="section">
                    <h3>1. 模拟积木块代码</h3>
                    <p>选择一个测试用例：</p>
                    <button class="btn" onclick="loadSimpleTest()" id="simpleBtn" disabled>简单移动测试</button>
                    <button class="btn" onclick="loadLoopTest()" id="loopBtn" disabled>循环测试</button>
                    <button class="btn" onclick="loadComplexTest()" id="complexBtn" disabled>复杂测试</button>
                    
                    <h4>生成的C代码：</h4>
                    <div id="codeArea" class="code-area">等待选择测试用例...</div>
                    
                    <button class="btn" onclick="executeCode()" id="executeBtn" disabled>执行C代码</button>
                </div>
            </div>
            
            <div class="column">
                <div class="section">
                    <h3>2. 执行结果</h3>
                    <div id="output" class="output">等待执行...</div>
                    
                    <h4>命令解析结果：</h4>
                    <div id="commandOutput" class="output">等待命令解析...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let picocjs = null;
        let currentCode = '';
        
        // 初始化
        function init() {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = '正在加载PicoC...';
            
            // 加载picoc-js
            const script = document.createElement('script');
            script.src = './static/picoc-test/bundle.umd.js';
            
            script.onload = () => {
                if (window.picocjs) {
                    picocjs = window.picocjs;
                    statusDiv.textContent = 'PicoC加载成功！可以开始测试。';
                    statusDiv.className = 'status success';
                    
                    // 启用按钮
                    document.getElementById('simpleBtn').disabled = false;
                    document.getElementById('loopBtn').disabled = false;
                    document.getElementById('complexBtn').disabled = false;
                } else {
                    statusDiv.textContent = 'PicoC加载失败：未找到picocjs对象';
                    statusDiv.className = 'status error';
                }
            };
            
            script.onerror = () => {
                statusDiv.textContent = 'PicoC加载失败：无法加载脚本文件';
                statusDiv.className = 'status error';
            };
            
            document.head.appendChild(script);
        }

        // 测试用例
        function loadSimpleTest() {
            currentCode = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    printf("机器人前进：速度=4，距离=10\\n");
    printf("FORWARD:4:10\\n");
    printf("前进动作完成\\n");
    printf("机器人右转：角度=90度\\n");
    printf("TURN_RIGHT:90\\n");
    printf("右转动作完成\\n");
    printf("机器人前进：速度=4，距离=5\\n");
    printf("FORWARD:4:5\\n");
    printf("前进动作完成\\n");
    printf("GPP_SAY:1:简单移动完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            document.getElementById('codeArea').textContent = currentCode;
            document.getElementById('executeBtn').disabled = false;
        }

        function loadLoopTest() {
            currentCode = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    printf("开始重复执行 4 次\\n");
    int i;
    for(i = 0; i < 4; i++) {
        printf("第 %d 次循环开始\\n", i + 1);
        printf("机器人前进：速度=4，距离=10\\n");
        printf("FORWARD:4:10\\n");
        printf("前进动作完成\\n");
        printf("机器人右转：角度=90度\\n");
        printf("TURN_RIGHT:90\\n");
        printf("右转动作完成\\n");
        printf("第 %d 次循环结束\\n", i + 1);
    }
    printf("重复执行完成\\n");
    printf("GPP_SAY:1:正方形绘制完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            document.getElementById('codeArea').textContent = currentCode;
            document.getElementById('executeBtn').disabled = false;
        }

        function loadComplexTest() {
            currentCode = `#include <stdio.h>

int main() {
    printf("程序开始执行\\n");
    
    // 复杂的机器人控制序列
    printf("GPP_SAY:1:开始复杂测试\\n");
    
    int count = 0;
    while(count < 3) {
        printf("FORWARD:4:15\\n");
        printf("TURN_LEFT:120\\n");
        count++;
    }
    
    printf("SERVO_OPEN\\n");
    printf("DELAY:1000\\n");
    printf("SERVO_CLOSE\\n");
    
    printf("BEEP:1000:500\\n");
    printf("LED:3:1\\n");
    
    printf("GPP_SAY:1:复杂测试完成\\n");
    printf("程序执行完成\\n");
    return 0;
}`;
            document.getElementById('codeArea').textContent = currentCode;
            document.getElementById('executeBtn').disabled = false;
        }

        // 执行C代码
        async function executeCode() {
            if (!picocjs || !currentCode) {
                alert('请先加载PicoC并选择测试用例');
                return;
            }

            const outputDiv = document.getElementById('output');
            const commandDiv = document.getElementById('commandOutput');
            
            outputDiv.textContent = '开始执行...\n';
            commandDiv.textContent = '等待命令解析...\n';

            try {
                let allOutput = '';
                
                await picocjs.runC(currentCode, (output) => {
                    allOutput += output;
                    outputDiv.textContent += output;
                    outputDiv.scrollTop = outputDiv.scrollHeight;
                });

                outputDiv.textContent += '\n执行完成！\n';
                
                // 解析命令
                parseCommands(allOutput);
                
            } catch (error) {
                outputDiv.textContent += `\n执行错误: ${error.message}\n`;
                commandDiv.textContent = `解析错误: ${error.message}`;
            }
        }

        // 解析命令
        function parseCommands(output) {
            const commandDiv = document.getElementById('commandOutput');
            commandDiv.textContent = '解析的命令:\n';
            commandDiv.textContent += '='.repeat(30) + '\n';

            const lines = output.split('\n');
            let commandCount = 0;

            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (!trimmedLine) return;

                if (trimmedLine.startsWith('FORWARD:')) {
                    const parts = trimmedLine.split(':');
                    commandDiv.textContent += `${++commandCount}. 前进命令 - 速度:${parts[1]}, 距离:${parts[2]}\n`;
                } else if (trimmedLine.startsWith('TURN_LEFT:')) {
                    const parts = trimmedLine.split(':');
                    commandDiv.textContent += `${++commandCount}. 左转命令 - 角度:${parts[1]}度\n`;
                } else if (trimmedLine.startsWith('TURN_RIGHT:')) {
                    const parts = trimmedLine.split(':');
                    commandDiv.textContent += `${++commandCount}. 右转命令 - 角度:${parts[1]}度\n`;
                } else if (trimmedLine.startsWith('GPP_SAY:')) {
                    const parts = trimmedLine.split(':');
                    const message = parts.slice(2).join(':');
                    commandDiv.textContent += `${++commandCount}. 说话命令 - 模式:${parts[1]}, 内容:${message}\n`;
                } else if (trimmedLine === 'SERVO_OPEN') {
                    commandDiv.textContent += `${++commandCount}. 机械手开启命令\n`;
                } else if (trimmedLine === 'SERVO_CLOSE') {
                    commandDiv.textContent += `${++commandCount}. 机械手关闭命令\n`;
                } else if (trimmedLine.startsWith('DELAY:')) {
                    const parts = trimmedLine.split(':');
                    commandDiv.textContent += `${++commandCount}. 延时命令 - ${parts[1]}毫秒\n`;
                } else if (trimmedLine.startsWith('BEEP:')) {
                    const parts = trimmedLine.split(':');
                    commandDiv.textContent += `${++commandCount}. 蜂鸣器命令 - 频率:${parts[1]}, 时间:${parts[2]}\n`;
                } else if (trimmedLine.startsWith('LED:')) {
                    const parts = trimmedLine.split(':');
                    commandDiv.textContent += `${++commandCount}. LED命令 - 模式:${parts[1]}, 颜色:${parts[2]}\n`;
                } else if (!trimmedLine.includes('程序') && !trimmedLine.includes('执行')) {
                    commandDiv.textContent += `${++commandCount}. 其他输出: ${trimmedLine}\n`;
                }
            });

            commandDiv.textContent += '='.repeat(30) + '\n';
            commandDiv.textContent += `总共解析了 ${commandCount} 个命令\n`;
        }

        // 页面加载时初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
