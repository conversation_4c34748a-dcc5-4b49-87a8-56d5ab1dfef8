# C代码执行模块详细设计文档

## 1. 模块概述

C代码执行模块是机器人仿真运行系统的执行引擎，负责解释执行由C代码生成模块产生的C语言程序。该模块集成了picoc-js解释器，实现了C代码的实时执行，并通过命令解析机制将C程序的输出转换为Scratch虚拟机的控制指令，从而实现对舞台精灵的控制。

## 2. 功能描述

### 2.1 核心功能
- **C代码解释执行**：使用picoc-js解释器执行C语言程序
- **实时输出捕获**：捕获C程序的printf输出并实时显示
- **命令解析转换**：将C程序输出解析为Scratch VM控制命令
- **精灵控制集成**：通过Scratch VM接口控制舞台精灵的运动和行为
- **执行状态管理**：管理代码执行的生命周期和状态

### 2.2 支持的C函数映射
- **运动控制函数**：forward()、turnleft()、turnright()
- **输出函数**：printf()、gpp_say()
- **传感器函数**：lightsensor()、distsensor()、mic1sensor()、tracker()
- **控制流函数**：支持for循环、while循环、if条件判断
- **延时函数**：sleep()、usleep()

## 3. 业务流程

### 3.1 主要执行流程
```
用户点击运行按钮 → 加载picoc解释器 → 预处理C代码 → 
执行C程序 → 捕获输出 → 解析命令 → 控制Scratch精灵 → 
显示执行结果 → 更新执行状态
```

### 3.2 详细业务流程

#### 步骤1：初始化准备
- 检查picoc-js解释器加载状态
- 验证C代码的完整性和语法
- 初始化输出捕获机制
- 创建执行状态监控

#### 步骤2：代码预处理
- 注入自定义C函数实现
- 添加必要的头文件和声明
- 优化代码结构以适配picoc解释器

#### 步骤3：执行监控
- 启动C程序执行
- 实时捕获printf输出
- 监控执行状态和错误
- 处理执行超时和异常

#### 步骤4：命令解析
- 解析C程序的输出文本
- 识别控制命令和参数
- 转换为Scratch VM可识别的指令

#### 步骤5：精灵控制
- 调用Scratch VM接口
- 执行精灵运动和行为控制
- 同步执行状态和动画效果

## 4. 关键函数调用

### 4.1 主要函数架构

#### handleRunClick()
**功能**：处理运行按钮点击事件，启动C代码执行流程
**调用流程**：
```javascript
handleRunClick() → 
    validateCode() → 
    createCustomCCode() → 
    executeWithPicoC() → 
    parseAndExecuteCommands()
```

#### loadPicoC()
**功能**：动态加载picoc-js解释器
**实现**：
```javascript
async loadPicoC() {
    const script = document.createElement('script');
    script.src = './static/picoc-test/bundle.umd.js';
    script.onload = () => {
        this.picocjs = window.picocjs;
        this.setState({picocLoaded: true});
    };
    document.head.appendChild(script);
}
```

#### createCustomCCode(originalCode)
**功能**：预处理C代码，注入自定义函数实现
**参数**：originalCode - 原始C代码
**返回值**：增强后的可执行C代码

#### parseAndExecuteCommands(output)
**功能**：解析C程序输出并执行相应的Scratch操作
**参数**：output - C程序的输出文本
**处理逻辑**：
- 按行分割输出文本
- 识别命令模式和参数
- 调用对应的VM控制函数

### 4.2 核心执行函数

#### executeWithPicoC(code)
**功能**：使用picoc解释器执行C代码
**实现**：
```javascript
await this.picocjs.runC(executableCode, (output) => {
    console.log('C程序输出:', output);
    outputBuffer += output;
    
    // 处理输出格式
    let processedOutput = output;
    if (output.includes('\\n')) {
        processedOutput = output.replace(/\\n/g, '\n');
    }
    
    displayOutput += processedOutput;
    
    // 更新显示
    this.updateOutputModal(displayOutput);
    
    // 解析并执行命令
    this.parseAndExecuteCommands(output);
});
```

#### executeVMCommand(command, vm)
**功能**：执行单个VM控制命令
**参数**：
- command: 解析出的控制命令
- vm: Scratch虚拟机实例

**支持的命令格式**：
```javascript
// 运动控制
"FORWARD:10" → forward(10)
"TURNLEFT:90" → turnleft(90)
"TURNRIGHT:45" → turnright(45)

// 外观控制
"GPP_SAY:1:Hello" → gpp_say(1, "Hello")
"PRINTF:Hello World" → printf("Hello World")

// 传感器读取
"LIGHTSENSOR" → lightsensor()
"DISTSENSOR" → distsensor()
```

## 5. 关键代码实现

### 5.1 picoc-js集成实现
```javascript
class CCodePanel extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            codeContent: '// C代码将在这里显示',
            runStatus: null,
            runMessage: '',
            isCompiling: false,
            isRunning: false,
            picocLoaded: false
        };
        
        this.picocjs = null;
        this.loadPicoC();
    }

    async loadPicoC() {
        try {
            if (window.picocjs) {
                this.picocjs = window.picocjs;
                this.setState({picocLoaded: true});
                return;
            }

            const script = document.createElement('script');
            script.src = './static/picoc-test/bundle.umd.js';
            
            script.onload = () => {
                if (window.picocjs) {
                    this.picocjs = window.picocjs;
                    this.setState({picocLoaded: true});
                }
            };
            
            script.onerror = () => {
                this.setState({
                    runStatus: 'error',
                    runMessage: 'PicoC 加载失败'
                });
            };
            
            document.head.appendChild(script);
        } catch (error) {
            console.error('加载 PicoC 失败:', error);
        }
    }
}
```

### 5.2 C代码预处理实现
```javascript
createCustomCCode(originalCode) {
    // 定义自定义函数实现
    const customFunctions = `
// 机器人控制函数实现
void forward(int steps) {
    printf("FORWARD:%d\\n", steps);
}

void turnleft(int angle) {
    printf("TURNLEFT:%d\\n", angle);
}

void turnright(int angle) {
    printf("TURNRIGHT:%d\\n", angle);
}

void gpp_say(int sprite_id, char* message) {
    printf("GPP_SAY:%d:%s\\n", sprite_id, message);
}

// 传感器函数实现
int lightsensor() {
    int value = 50 + (rand() % 50);
    printf("LIGHTSENSOR:%d\\n", value);
    return value;
}

int distsensor() {
    int value = 10 + (rand() % 90);
    printf("DISTSENSOR:%d\\n", value);
    return value;
}

int mic1sensor() {
    int value = 30 + (rand() % 70);
    printf("MIC1SENSOR:%d\\n", value);
    return value;
}

int tracker(int id) {
    int value = rand() % 2;
    printf("TRACKER:%d:%d\\n", id, value);
    return value;
}

int Get_Ps2Value() {
    int value = rand() % 16;
    printf("PS2VALUE:%d\\n", value);
    return value;
}
`;

    // 查找main函数位置并插入自定义函数
    const mainIndex = originalCode.indexOf('int main()');
    if (mainIndex !== -1) {
        const beforeMain = originalCode.substring(0, mainIndex);
        const fromMain = originalCode.substring(mainIndex);
        return beforeMain + customFunctions + '\n' + fromMain;
    }
    
    return customFunctions + '\n' + originalCode;
}
```

### 5.3 命令解析和执行实现
```javascript
parseAndExecuteCommands(output) {
    if (!this.props.vm) return;

    const lines = output.split('\n');
    const vm = this.props.vm;

    lines.forEach((line, index) => {
        line = line.trim();
        if (!line) return;

        // 添加延时，让动作有序执行
        setTimeout(() => {
            this.executeVMCommand(line, vm);
        }, index * 200); // 每个命令间隔200ms
    });
}

executeVMCommand(command, vm) {
    try {
        console.log('执行VM命令:', command);

        // 解析运动控制命令
        if (command.startsWith('FORWARD:')) {
            const steps = parseInt(command.split(':')[1]) || 10;
            this.moveSprite(vm, 0, steps); // 向前移动
        } else if (command.startsWith('TURNLEFT:')) {
            const angle = parseInt(command.split(':')[1]) || 90;
            this.rotateSprite(vm, -angle); // 左转
        } else if (command.startsWith('TURNRIGHT:')) {
            const angle = parseInt(command.split(':')[1]) || 90;
            this.rotateSprite(vm, angle); // 右转
        }
        
        // 解析外观控制命令
        else if (command.startsWith('GPP_SAY:')) {
            const parts = command.split(':');
            const message = parts.slice(2).join(':') || 'Hello';
            this.makeSpriteSpeak(vm, message);
        }
        
        // 解析传感器命令
        else if (command.startsWith('LIGHTSENSOR:')) {
            const value = command.split(':')[1];
            console.log('光线传感器值:', value);
        } else if (command.startsWith('DISTSENSOR:')) {
            const value = command.split(':')[1];
            console.log('距离传感器值:', value);
        }
        
        // 处理普通输出
        else if (command.startsWith('PRINTF:') || 
                (!command.includes(':') && command.length > 0)) {
            const message = command.startsWith('PRINTF:') ? 
                          command.substring(7) : command;
            console.log('程序输出:', message);
        }
    } catch (error) {
        console.error('执行VM命令失败:', error, command);
    }
}
```

### 5.4 精灵控制实现
```javascript
moveSprite(vm, direction, steps) {
    try {
        const editingTarget = vm.editingTarget;
        if (!editingTarget) return;

        const currentX = editingTarget.x;
        const currentY = editingTarget.y;
        const currentDirection = editingTarget.direction;

        // 计算新位置
        const radians = (currentDirection - 90) * Math.PI / 180;
        const deltaX = Math.cos(radians) * steps;
        const deltaY = Math.sin(radians) * steps;

        const newX = currentX + deltaX;
        const newY = currentY + deltaY;

        // 移动精灵
        editingTarget.setXY(newX, newY);
        
        console.log(`精灵移动: (${currentX}, ${currentY}) → (${newX}, ${newY})`);
    } catch (error) {
        console.error('移动精灵失败:', error);
    }
}

rotateSprite(vm, angle) {
    try {
        const editingTarget = vm.editingTarget;
        if (!editingTarget) return;

        const currentDirection = editingTarget.direction;
        const newDirection = currentDirection + angle;
        
        editingTarget.setDirection(newDirection);
        
        console.log(`精灵旋转: ${currentDirection}° → ${newDirection}°`);
    } catch (error) {
        console.error('旋转精灵失败:', error);
    }
}

makeSpriteSpeak(vm, message) {
    try {
        const editingTarget = vm.editingTarget;
        if (!editingTarget) return;

        // 创建说话气泡
        editingTarget.setSay('say', message);
        
        // 3秒后清除气泡
        setTimeout(() => {
            if (editingTarget.getSay()) {
                editingTarget.setSay('say', '');
            }
        }, 3000);
        
        console.log(`精灵说话: "${message}"`);
    } catch (error) {
        console.error('精灵说话失败:', error);
    }
}
```

## 6. 技术特点

### 6.1 设计优势
- **实时执行**：支持C代码的实时解释执行，无需编译过程
- **无缝集成**：与Scratch VM深度集成，实现图形化和文本化编程的统一
- **命令映射**：通过输出解析实现C函数到Scratch操作的映射
- **异步处理**：支持异步执行和状态管理，不阻塞用户界面

### 6.2 性能优化
- **延时控制**：通过setTimeout实现命令的有序执行
- **状态管理**：完善的执行状态跟踪和错误处理
- **内存管理**：及时清理执行资源，避免内存泄漏

### 6.3 扩展性
- **函数扩展**：易于添加新的C函数和Scratch操作映射
- **解释器升级**：支持picoc解释器的版本升级和功能扩展
- **多平台支持**：基于Web技术，支持跨平台运行

## 7. 与前端系统集成

### 7.1 React组件集成
C代码执行模块作为React组件集成到Scratch GUI中，与其他组件协同工作。

#### 主要组件关系
```javascript
// GUI主组件中的集成
import CCodePanel from '../../containers/c-code-panel.jsx';

// 在GUI组件中使用
<CCodePanel
    vm={vm}
    isVisible={activeTabIndex === BLOCKS_TAB_INDEX}
    onRunClick={this.handleCCodeRun}
/>
```

#### 状态管理集成
```javascript
// Redux状态管理
const mapStateToProps = state => ({
    vm: state.scratchGui.vm,
    editingTarget: state.scratchGui.targets.editingTarget,
    isPlayerOnly: state.scratchGui.mode.isPlayerOnly
});

const mapDispatchToProps = dispatch => ({
    onRunClick: () => dispatch(runCCode()),
    onUpdateCode: (code) => dispatch(updateCCode(code))
});
```

### 7.2 Scratch VM集成
与Scratch虚拟机深度集成，实现C代码对精灵的直接控制。

#### VM接口调用
```javascript
// 获取当前编辑目标
const editingTarget = vm.editingTarget;

// 控制精灵移动
editingTarget.setXY(newX, newY);
editingTarget.setDirection(newDirection);

// 控制精灵外观
editingTarget.setSay('say', message);
editingTarget.setVisible(true);

// 触发事件
vm.greenFlag();
vm.stopAll();
```

### 7.3 事件系统集成
```javascript
// 监听工作区变化
vm.on('workspaceUpdate', this.handleWorkspaceUpdate);
vm.on('targetsUpdate', this.handleTargetsUpdate);

// 监听积木块变化
vm.on('BLOCK_DRAG_UPDATE', this.handleBlockDragUpdate);
vm.on('BLOCK_DRAG_END', this.handleBlockDragEnd);
```

## 8. 与后端系统集成

### 8.1 用户认证集成
C代码执行需要用户登录状态，确保执行权限和数据安全。

#### JWT认证流程
```javascript
// 检查用户登录状态
const checkAuthStatus = () => {
    const token = localStorage.getItem('token');
    if (!token) {
        throw new Error('用户未登录，无法执行C代码');
    }
    return token;
};

// API请求中包含认证信息
const executeWithAuth = async (cCode) => {
    const token = checkAuthStatus();
    const response = await fetch('/api/code/execute', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: cCode })
    });
    return response.json();
};
```

### 8.2 项目数据集成
执行的C代码与项目数据关联，支持项目的保存和加载。

#### 相关后端接口
- **ProjectController.getProjectDetail()** - 获取项目的C代码
- **ProjectController.saveProject()** - 保存执行结果和状态
- **UserController.getCurrentUser()** - 获取当前用户信息

#### 数据同步机制
```javascript
// 执行前保存项目状态
const saveProjectBeforeExecution = async (projectData) => {
    const response = await fetch('/api/project/save', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            projectId: projectData.id,
            projectBlocks: projectData.blocks,
            executionStatus: 'ready'
        })
    });
    return response.json();
};

// 执行后更新项目状态
const updateProjectAfterExecution = async (projectId, result) => {
    const response = await fetch('/api/project/update-status', {
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            projectId: projectId,
            executionStatus: result.success ? 'completed' : 'failed',
            executionResult: result
        })
    });
    return response.json();
};
```

## 9. 系统架构图

### 9.1 执行流程架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面      │    │  C代码执行模块   │    │   picoc-js      │
│  (运行按钮)     │───▶│ (CCodePanel)    │───▶│   (解释器)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   命令解析器     │              │
         │              │ (parseCommands) │              │
         │              └─────────────────┘              │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scratch VM    │    │   输出显示      │    │   错误处理      │
│   (精灵控制)    │    │  (OutputModal)  │    │ (ErrorHandler)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 9.2 数据流架构
```
C代码输入 → picoc解释器 → 输出捕获 → 命令解析 →
VM指令 → 精灵控制 → 动画效果 → 状态更新 → 结果显示
```

## 10. 错误处理和异常管理

### 10.1 执行错误处理
```javascript
async executeCode(code) {
    try {
        this.setState({ isRunning: true, runStatus: null });

        await this.picocjs.runC(code, this.handleOutput);

        this.setState({
            isRunning: false,
            runStatus: 'success',
            runMessage: 'C代码执行成功！'
        });
    } catch (error) {
        console.error('C代码执行失败:', error);
        this.setState({
            isRunning: false,
            runStatus: 'error',
            runMessage: `执行失败: ${error.message}`
        });
        this.showErrorModal(error);
    }
}
```

### 10.2 VM控制错误处理
```javascript
executeVMCommand(command, vm) {
    try {
        // 检查VM状态
        if (!vm || !vm.editingTarget) {
            throw new Error('Scratch VM未就绪');
        }

        // 执行命令
        this.processCommand(command, vm);

    } catch (error) {
        console.error('VM命令执行失败:', error);
        this.logExecutionError(command, error);
    }
}
```

### 10.3 资源清理机制
```javascript
componentWillUnmount() {
    // 清理picoc实例
    if (this.picocjs) {
        this.picocjs.cleanup();
        this.picocjs = null;
    }

    // 清理定时器
    if (this.executionTimer) {
        clearTimeout(this.executionTimer);
    }

    // 清理事件监听
    if (this.props.vm) {
        this.props.vm.removeAllListeners('workspaceUpdate');
    }
}
```

## 11. 性能优化和监控

### 11.1 执行性能优化
- **异步执行**：使用Promise和async/await避免阻塞UI
- **命令队列**：通过setTimeout实现命令的有序执行
- **内存管理**：及时清理执行资源和临时变量
- **缓存机制**：缓存常用的函数实现和配置

### 11.2 监控和日志
```javascript
// 执行性能监控
const performanceMonitor = {
    startTime: null,

    start() {
        this.startTime = performance.now();
    },

    end(operation) {
        const duration = performance.now() - this.startTime;
        console.log(`${operation} 执行时间: ${duration.toFixed(2)}ms`);

        // 发送性能数据到后端
        this.sendPerformanceData(operation, duration);
    },

    sendPerformanceData(operation, duration) {
        fetch('/api/performance/log', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                operation,
                duration,
                timestamp: new Date().toISOString()
            })
        }).catch(console.error);
    }
};
```
