@import "../../css/colors.css";
@import "../../css/units.css";
@import "../../css/typography.css";

.crash-wrapper {
    background-color: $motion-primary;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.body {
    width: 35%;
    color: white;
    text-align: center;
}

/* scratch-www was overriding the colors */
.body p, .body h2 {
    color: inherit;
}

.reloadButton {
    border: 1px solid $motion-primary;
    border-radius: 0.25rem;
    padding: 0.5rem 2rem;
    background: white;
    color: $motion-primary;
    font-weight: bold;
    font-size: 0.875rem;
    cursor: pointer;
}
