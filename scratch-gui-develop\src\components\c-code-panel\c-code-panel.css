@import "../../css/units.css";
@import "../../css/colors.css";

.cCodePanelWrapper {
    display: flex;
    flex-direction: column;
    width: 300px;
    background-color: white;
    border-left: 1px solid #ddd;
    height: 100%;
}

.cCodePanelHeader {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3rem;
    background-color: #f9f9f9;
    color: #333;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
    font-size: 1rem;
}

.cCodePanelContent {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background-color: #fafafa;
}

.codeInfo {
    margin-bottom: 0.75rem;
    color: #666;
    font-size: 0.8rem;
    border-bottom: 1px dashed #ddd;
    padding-bottom: 0.5rem;
}

.codeDisplayWrapper {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    border: 1px solid #eee;
    background-color: white;
}

.codeDisplay {
    margin: 0;
    font-family: "Courier New", Courier, monospace;
    white-space: pre-wrap;
    word-break: break-all;
    color: #333;
    font-size: 0.875rem;
    line-height: 1.5;
    background-color: #f8f8f8;
    padding: 0;
    overflow-x: auto;
    border-radius: 4px;
}

.codeContainer {
    display: block;
    font-family: "Courier New", Courier, monospace;
    font-size: 0.875rem;
    padding: 0;
    margin: 0;
    background-color: transparent !important;
}

.codeLine {
    display: flex;
    min-height: 1.5em;
}

.lineNumber {
    display: inline-block;
    width: 2.5rem;
    padding-right: 0.5rem;
    text-align: right;
    color: #999;
    background-color: #f0f0f0;
    border-right: 1px solid #ddd;
    user-select: none;
    padding-left: 0.5rem;
}

.lineContent {
    flex: 1;
    padding-left: 0.5rem;
    min-height: 1.5em;
}

/* 高亮代码样式覆盖 */
.lineContent.hljs {
    background-color: transparent;
    padding: 0;
}

/* 自定义高亮颜色 */
.hljs_keyword {
    color: #0000ff;
    font-weight: bold;
}

.hljs_string {
    color: #a31515;
}

.hljs_comment {
    color: #008000;
}

.hljs_number {
    color: #098658;
}

.hljs_preprocessor {
    color: #808080;
}

.hljs_type {
    color: #267f99;
}

.hljs_function {
    color: #795e26;
}

.hljs_builtin {
    color: #0070c1;
}

/* C代码面板按钮样式 */
.codeButtonsContainer {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.codeButton {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $ui-green;
    color: white;
    border: none;
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
}

.codeButton:hover {
    background-color: $ui-green-2;
}

.codeButton:active {
    transform: scale(0.98);
}

.codeButton:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.compileButton {
    background-color: $ui-green;
}

.runButton {
    background-color: $ui-green;
}

.messageContainer {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.85rem;
}

.successMessage {
    background-color: rgba(13, 165, 122, 0.1);
    color: $ui-green;
    border: 1px solid rgba(13, 165, 122, 0.3);
}

.errorMessage {
    background-color: rgba(255, 140, 26, 0.1);
    color: $error-primary;
    border: 1px solid rgba(255, 140, 26, 0.3);
}