C程序库说明
控制部分：
void forward(int speed, int destance) 
库函数解释：用于控制机器人前进运动，包含两个参数；
参数一：int speed，说明：设定机器人前进运动的档位，范围【1-8】，效果明显档位为4，小于4的档位可能运动效果不明显；
参数二：int destance，说明：设定机器人前进运动的距离，范围在【1-200】之间。
void back(int speed, int destance)
库函数解释：用于控制机器人后退运动，包含两个参数；
参数一：int speed，说明：设定机器人后退运动的档位，范围【1-8】，效果明显档位为4，小于4的档位可能运动效果不明显；
参数二：int destance，说明：设定机器人后退运动的距离，范围在【1-200】之间。
void turn_left(int degree)
库函数解释：用于控制机器人左转运动，包含两个参数；
参数一：int degree，说明：机器人左转角度。
void turn_right(int degree)
库函数解释：用于控制机器人右转运动，包含两个参数；
参数一：int degree，说明：机器人右转角度。

void gpp_say(int mode, void *str) 
库函数解释：使机器人说话；
参数一：int mode，说明：0（当前语句打断前面语句说话），1（当前语句等待前面语句说完再说话）；
参数二：void *str，说明：说话内容，可中文课英文，字数范围20字以内。
高级用法（在参数二中加入下列标记，会有额外的发音效果）：
[h1]：字母发音方式，单词将以单个字母开始朗读；[h2]：英文发音方式，单词以其本来的发音朗读。
[m52]：男声发音；[m53]：女声发音；[m54]：唐老鸭发音；[m55]：许小宝发音。
[s?]：?为语速值，范围【0~10】。
[t?]：?为语调值，范围【0~10】。
[v?]：?为音量值，范围【0~10】。
[d]：恢复所有设置为默认设置。
[sound***]：发出指定提示音，***在【101-125】之间为信息提示音，***在【201-225】之间为铃声提示音，***在【301-330】之间为警报提示音。
高级用法示例："[sound221] 出现紧急情况，[h2]warning"。

int servo_open(void)
库函数解释：控制打开机器人的机械手。（注意：机器人需要装配机械手，否者无效）；
返回值：-1（失败），0（成功）。
int servo_close(void)
库函数解释：控制闭合机器人的机械手。（注意：机器人需要装配机械手，否者无效）；
返回值：-1（失败），0（成功）。

int tracker_start(void)
库函数解释：开启机器人智能巡线模式；
返回值：-1（失败），0（成功）。
int tracker_close(void)
库函数解释：关闭机器人智能巡线模式；
返回值：-1（失败），0（成功）。

void beep(int bound, int time)
库函数解释：控制机器人蜂鸣器发出指定频率和指定时间的声音；
参数一：int bound，说明：指定发音频率，范围【200-5000】；
参数二：int time，说明：指定发音时间，范围【0-3000】，单位：ms（毫秒）。

void colorful_led(int mode,int rgb)
库函数解释：控制机器人炫彩LED灯亮灯模式和亮灯颜色;
参数一：int mode，说明：0（恢复两个LED灯为默认模式），1（单独控制左侧LED灯），2（单独控制右侧LED灯），3或其它数字（两个LED同时控制）;
参数二：int rgb，说明：控制LED灯亮灯的颜色，范围【1-7】。

void Set_CScript_Mode(int mode)
库函数解释：设置机器人C程序运行模式;
参数一：int mode，说明：1（抢占式执行，直接中断前面的C程序并执行），2（等待式执行，等待前面的C程序执行完毕才执行），其它（等待式执行）.

void cexit(void);
库函数解释：停止当前运行的C程序;
传感器部分：
int lightsensor(void);
库函数解释：获取机器人光线传感器数值;
int distsensor(void);
库函数解释：获取机器人距离传感器数值，单位：厘米;
int mic1sensor(void);
库函数解释：获取机器人噪声检测传感器数值;
int tracker(int id);
库函数解释：获取机器人循迹传感器数值;
参数一：int id，说明：指定循迹传感器编号，范围【1-4】，以获取该编号下的循迹传感器的状态数值；
返回值：黑线检测状态：0和1。
int Get_Ps2Value(void);
库函数解释：获取机器人控制手柄按下的按键值。
