import React from 'react';
import {connect} from 'react-redux';
import PropTypes from 'prop-types';

import {checkAuth, isAuthenticated} from '../reducers/user';
import {isLoginPage, redirectToLogin} from './routing';

/**
 * 路由保护高阶组件
 * 确保用户必须先登录才能访问受保护的页面
 * @param {React.Component} WrappedComponent - 被包装的组件
 * @returns {React.Component} - 包装后的组件
 */
const AuthRouteHOC = function (WrappedComponent) {
    class AuthRouteWrapper extends React.Component {
        constructor (props) {
            super(props);
            this.state = {
                isChecking: true,
                isAuthorized: false
            };
        }

        componentDidMount () {
            this.checkAuthentication();
        }

        checkAuthentication () {
            console.log('正在检查用户认证状态...');
            // 检查当前是否是登录页面
            const isInLoginPage = isLoginPage();
            console.log('当前是否在登录页面:', isInLoginPage);
            
            // 如果是登录页面，验证是否已登录
            if (isInLoginPage) {
                console.log('当前是登录页面，允许访问');
                // 无需验证身份，始终允许访问登录页面
                this.setState({
                    isChecking: false,
                    isAuthorized: true
                });
                return;
            }

            // 检查用户是否已登录 - 只有非登录页面才需要检查
            const authenticated = this.props.isLoggedIn || isAuthenticated();
            console.log('用户登录状态:', authenticated);
            
            if (authenticated) {
                console.log('用户已登录，允许访问系统页面');
                this.setState({
                    isChecking: false,
                    isAuthorized: true
                });
                return;
            }

            console.log('用户未登录，尝试从localStorage恢复用户会话');
            // 尝试从localStorage恢复用户会话 - 只有非登录页面需要
            this.props.onCheckAuth()
                .then(isAuth => {
                    console.log('会话检查结果:', isAuth);
                    if (isAuth) {
                        console.log('会话有效，允许访问');
                        this.setState({
                            isChecking: false,
                            isAuthorized: true
                        });
                    } else {
                        console.log('会话无效，重定向到登录页面');
                        // 仅当不在登录页面时才重定向
                        if (!isInLoginPage) {
                            redirectToLogin();
                        } else {
                            // 已经在登录页面，无需重定向
                            this.setState({
                                isChecking: false,
                                isAuthorized: true
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('会话检查出错:', error);
                    // 仅当不在登录页面时才重定向
                    if (!isInLoginPage) {
                        redirectToLogin();
                    } else {
                        // 已经在登录页面，无需重定向
                        this.setState({
                            isChecking: false,
                            isAuthorized: true
                        });
                    }
                });
        }

        render () {
            const {isChecking, isAuthorized} = this.state;
            
            // 如果正在检查认证状态，显示加载中
            if (isChecking) {
                return <div>加载中...</div>;
            }
            
            // 如果已授权，渲染被包装的组件
            if (isAuthorized) {
                return <WrappedComponent {...this.props} />;
            }
            
            // 默认返回null，实际上不会到达这里，因为未授权会重定向
            return null;
        }
    }

    AuthRouteWrapper.propTypes = {
        isLoggedIn: PropTypes.bool.isRequired,
        onCheckAuth: PropTypes.func.isRequired
    };

    const mapStateToProps = state => ({
        isLoggedIn: state.user.isLoggedIn
    });

    const mapDispatchToProps = dispatch => ({
        onCheckAuth: () => dispatch(checkAuth())
    });

    return connect(
        mapStateToProps,
        mapDispatchToProps
    )(AuthRouteWrapper);
};

export default AuthRouteHOC;
