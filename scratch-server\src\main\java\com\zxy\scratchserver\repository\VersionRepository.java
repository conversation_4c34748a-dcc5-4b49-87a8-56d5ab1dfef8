package com.zxy.scratchserver.repository;

import com.zxy.scratchserver.model.Version;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 版本数据访问接口
 */
@Repository
public interface VersionRepository extends JpaRepository<Version, Long> {
    
    /**
     * 根据项目ID查找版本列表
     * @param projectId 项目ID
     * @return 版本列表
     */
    List<Version> findByProjectId(Long projectId);
    
    /**
     * 根据项目ID查找最新版本
     * @param projectId 项目ID
     * @return 最新版本
     */
    Optional<Version> findFirstByProjectIdOrderByIdDesc(Long projectId);
}
