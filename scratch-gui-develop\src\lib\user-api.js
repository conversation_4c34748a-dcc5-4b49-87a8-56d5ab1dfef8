/**
 * 用户相关的API服务
 * 这个文件包含了与用户认证相关的API调用
 */

import { getToken } from './auth-service';

// 后端API的基础URL
const API_BASE_URL = 'http://localhost:8080/api';

/**
 * 创建带有认证头的请求选项
 * @param {Object} options - 请求选项
 * @returns {Object} - 带有认证头的请求选项
 */
const createAuthHeaders = (options = {}) => {
    const token = getToken();
    const headers = options.headers || {};

    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }

    return {
        ...options,
        headers: {
            ...headers,
            'Content-Type': 'application/json'
        }
    };
};

/**
 * 创建不带认证头的请求选项（用于登录/注册等不需要认证的请求）
 * @param {Object} options - 请求选项
 * @returns {Object} - 不带认证头的请求选项
 */
const createBasicHeaders = (options = {}) => {
    const headers = options.headers || {};

    return {
        ...options,
        headers: {
            ...headers,
            'Content-Type': 'application/json'
        }
    };
};

/**
 * 处理API响应
 * @param {Response} response - fetch API的响应对象
 * @returns {Promise} - 处理后的响应数据
 */
const handleResponse = (response) => {
    if (!response.ok) {
        // 对于非2xx响应，先尝试解析错误消息
        return response.json()
            .then(errorData => {
                throw new Error(errorData.message || `请求失败，状态码: ${response.status}`);
            })
            .catch(err => {
                if (err.message) {
                    throw err;
                }
                throw new Error(`请求失败，状态码: ${response.status}`);
            });
    }
    return response.json();
};

/**
 * 登录API
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise} - 返回包含用户信息和token的Promise
 */
const login = (username, password) => {
    console.log('API login 函数被调用:', username, '密码长度:', password ? password.length : 0);

    // 验证参数
    if (!username || !password) {
        console.error('用户名或密码为空');
        return Promise.reject(new Error('用户名或密码不能为空'));
    }

    const url = `${API_BASE_URL}/user/login`;
    // 登录时使用不带认证头的请求选项
    const options = createBasicHeaders({
        method: 'POST',
        body: JSON.stringify({ username, password })
    });

    console.log(`发送登录请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('登录成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('登录请求失败:', error);
            throw error;
        });
};

/**
 * 注册API
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise} - 返回包含用户信息和token的Promise
 */
const register = (username, password) => {
    console.log('API register 函数被调用:', username, '密码长度:', password ? password.length : 0);

    // 验证参数
    if (!username || !password) {
        console.error('用户名或密码为空');
        return Promise.reject(new Error('用户名或密码不能为空'));
    }

    const url = `${API_BASE_URL}/user/register`;
    // 注册时使用不带认证头的请求选项
    const options = createBasicHeaders({
        method: 'POST',
        body: JSON.stringify({ username, password })
    });

    console.log(`发送注册请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('注册成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('注册请求失败:', error);
            throw error;
        });
};

/**
 * 登出API
 * @returns {Promise} - 返回登出结果的Promise
 */
const logout = () => {
    console.log('API logout 函数被调用');

    const url = `${API_BASE_URL}/user/logout`;
    const options = createAuthHeaders({
        method: 'POST'
    });

    console.log(`发送登出请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('登出成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('登出请求失败:', error);
            throw error;
        });
};

/**
 * 获取当前用户信息
 * @returns {Promise} - 返回用户信息的Promise
 */
const getCurrentUser = () => {
    console.log('API getCurrentUser 函数被调用');

    const url = `${API_BASE_URL}/user/current`;
    const options = createAuthHeaders({
        method: 'GET'
    });

    console.log(`发送获取用户信息请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('获取用户信息成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('获取用户信息失败:', error);
            throw error;
        });
};

/**
 * 更新用户信息
 * @param {Object} userInfo - 用户信息对象
 * @returns {Promise} - 返回更新结果的Promise
 */
const updateUserInfo = (userInfo) => {
    console.log('API updateUserInfo 函数被调用:', userInfo);

    const url = `${API_BASE_URL}/user/update`;
    const options = createAuthHeaders({
        method: 'POST',
        body: JSON.stringify(userInfo)
    });

    console.log(`发送更新用户信息请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('更新用户信息成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('更新用户信息失败:', error);
            throw error;
        });
};

/**
 * 修改密码
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 * @returns {Promise} - 返回修改结果的Promise
 */
const updatePassword = (oldPassword, newPassword) => {
    console.log('API updatePassword 函数被调用');

    // 验证参数
    if (!oldPassword || !newPassword) {
        console.error('旧密码或新密码为空');
        return Promise.reject(new Error('旧密码或新密码不能为空'));
    }

    // 确保URL正确
    const url = `${API_BASE_URL}/user/update-password`;
    console.log('修改密码请求URL:', url);

    // 创建请求选项，确保包含认证头
    const options = createAuthHeaders({
        method: 'POST',
        body: JSON.stringify({ oldPassword, newPassword })
    });

    // 打印请求头和请求体
    console.log('修改密码请求头:', options.headers);
    console.log('修改密码请求体:', { oldPassword: '***', newPassword: '***' });

    console.log(`发送修改密码请求至: ${url}`);
    return fetch(url, options)
        .then(handleResponse)
        .then(data => {
            console.log('修改密码成功，返回数据:', data);
            return data;
        })
        .catch(error => {
            console.error('修改密码失败:', error);
            throw error;
        });
};

export {
    login,
    register,
    logout,
    getCurrentUser,
    updateUserInfo,
    updatePassword
};
