@import "../../css/units.css";
@import "../../css/colors.css";
@import "../../css/z-index.css";

.stage {
    /*
        Fixes a few extra pixels of margin/padding, that adds on to the bottom
        of the element, which messes up the chrome padding consistency
    */
    display: block;

    border-radius: $space;
    border: $stage-standard-border-width solid $ui-black-transparent;
    overflow: hidden;

    /* @todo: This is for overriding the value being set somewhere. Where is it being set? */
    background-color: transparent;

    /* Allow custom touch handling to prevent scrolling on Edge */
    touch-action: none;

    /* Make sure border is not included in size calculation */
    box-sizing: content-box !important;

    /* enforce overflow + reset position of absolutely-positioned children */
    position: relative;
}

.stage.full-screen {
    border: $stage-full-screen-border-width solid rgb(126, 133, 151);
}

.with-color-picker {
    cursor: none;
    z-index: $z-index-stage-with-color-picker;
}

.color-picker-background {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.55);
    display: block;
    z-index: $z-index-stage-color-picker-background;
    top: 0;
    left: 0;
}

.stage-wrapper {
    position: relative;
}

/* we want stage overlays to all be positioned in the same spot as the stage, but can't put them inside the border
because we want their overflow to be visible, and the bordered element must have overflow: hidden set so that the
stage doesn't "spill" out from under its rounded corners. instead, shift these over by the border width. */
.stage-overlays {
    position: absolute;
    top: $stage-standard-border-width;
    left: $stage-standard-border-width;

    /* the overlay itself should not capture pointer events; only its child elements can do that */
    pointer-events: none;
}

.stage-overlays.full-screen {
    top: $stage-full-screen-border-width;
    left: $stage-full-screen-border-width;
}

.monitor-wrapper,
.frame-wrapper,
.green-flag-overlay-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
}

.dragging-sprite {
    position: absolute;
    top: 0;
    left: 0;
    z-index: $z-index-dragging-sprite;
    filter: drop-shadow(5px 5px 5px $ui-black-transparent);
}

.stage-bottom-wrapper {
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    top: 0;
    left: 0;
    overflow: hidden;
    pointer-events: none;
}

.mic-indicator {
    transform-origin: bottom right;
    z-index: $z-index-stage-indicator;
    pointer-events: none;
    align-self: flex-end;
}

.question-wrapper {
    z-index: $z-index-stage-question;
    pointer-events: auto;
}

.frame {
    background: $looks-transparent;
    border: 2px solid $looks-secondary;
    border-radius: 0.5rem;
    animation-name: flash;
    animation-duration: 0.75s;
    animation-fill-mode: forwards; /* Leave at 0 opacity after animation */
}

.green-flag-overlay-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0,0,0,0.25);
    border-radius: 0.5rem;
    pointer-events: all;
    cursor: pointer;
}

.green-flag-overlay {
    padding: 1rem;
    border-radius: 100%;
    background: rgba(255,255,255,0.75);
    border: 3px solid $ui-white;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    width: 5rem;
    height: 5rem;
}

.green-flag-overlay > img {
    width: 100%;
    object-fit: contain;
}



@keyframes flash {
    0% { opacity: 1; }
    100% { opacity: 0; }
}
