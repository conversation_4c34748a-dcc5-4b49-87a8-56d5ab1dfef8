@startuml 项目管理模块时序图

' 定义参与者
actor 用户
participant "前端" as Frontend
participant "后端" as Backend
participant "数据库" as DB

' 核心流程
== 项目生命周期管理 ==
用户 -> Frontend: 创建新项目
Frontend -> Backend: 保存项目(ProjectController.saveProject)
Backend -> DB: 存储项目数据
DB --> Backend: 返回项目ID
Backend --> Frontend: 返回创建结果
Frontend --> 用户: 显示项目创建成功

用户 -> Frontend: 保存项目修改
Frontend -> Frontend: 序列化项目状态
Frontend -> Backend: 保存修改并创建版本
Backend -> DB: 更新项目和版本信息
DB --> Backend: 保存成功
Backend --> Frontend: 返回保存结果
Frontend --> 用户: 显示保存成功

用户 -> Frontend: 加载项目
Frontend -> Backend: 获取项目(ProjectController.getProjectDetail)
Backend -> DB: 查询项目数据
Backend -> Backend: 验证访问权限
DB --> Backend: 返回项目数据
Backend --> Frontend: 返回项目详情
Frontend -> Frontend: 重建项目状态
Frontend --> 用户: 显示加载的项目

@enduml
