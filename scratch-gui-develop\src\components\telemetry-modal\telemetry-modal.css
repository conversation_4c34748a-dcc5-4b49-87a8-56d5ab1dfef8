@import "../../css/colors.css";
@import "../../css/units.css";
@import "../../css/typography.css";
@import "../../css/z-index.css";

/* from scratch-www */
$active-gray: hsla(0, 0%, 0%, .1);
$ui-blue-10percent: hsla(215, 100%, 65%, .1);
$ui-blue-25percent:  hsla(215, 100%, 65%, .25);
$ui-gray: hsla(0, 0%, 95%, 1);

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $z-index-modal;
    background-color: $ui-modal-overlay;
}

.modal-content {
    margin: 100px auto;
    outline: none;
    border: .25rem solid $ui-white-transparent;
    padding: 0;
    border-radius: $space;
    user-select: none;
    width: 640px;

    color: $text-primary;
    overflow: hidden;
}

.illustration {
    width: 100%;
    height: 123px;
    background-color: $looks-secondary;
    background-image: url('./telemetry-modal-header.png');
    background-size: cover;
}

.body {
    background: $ui-white;
    padding: 1.5rem 2.25rem;
}

.privacy-policy-link {
    color: $looks-secondary;
    text-decoration: none;
}

/* stack the radio buttons vertically, not horizontally */
.radio-buttons label {
    display: block;
    margin: 0.5rem;
    transition: all .125s ease;
    background-color: $ui-gray;
    border-radius: .5rem;
    margin: 0 auto 0.375rem;
    align-items: center;
    padding: 1rem 0;
    vertical-align: middle;
}

.radio-buttons label:hover {
    background-color: $ui-blue-10percent;
}

.radio-buttons label.label-selected,
.radio-buttons label.label-selected:hover {
    background-color: $ui-blue-25percent;
}

.radio-buttons input[type="radio"] {
    margin: -1px 0.75rem 1px;
    border: 1px solid $active-gray;
    border-radius: 50%;
    width: 1rem;
    height: 1rem;
    appearance: none;
    background-color: $ui-white;
    vertical-align: middle;
}

.radio-buttons input[type="radio"]:checked,
.radio-buttons input[type="radio"]:focus {
    box-shadow: 0 0 0 2px $ui-blue-25percent;
    outline: none;
}

.radio-buttons input[type="radio"]:checked {
    transition: all .25s ease;
    background-color: $ui-white;
    border: 1px solid $looks-secondary;
}

.radio-buttons input[type="radio"]:checked::after {
    display: block;
    margin: 0.125rem;
    border-radius: 50%;
    background-color: $looks-secondary;
    width: .625rem;
    height: .625rem;
    content: "";
}

/* Confirmation buttons at the bottom of the modal */
.button-row {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    align-items: baseline;

    margin: 1.5rem 0;
    font-weight: bolder;
}

@keyframes fade-out {
    0% {opacity: 1}
    100% {opacity: 0}
}

.setting-was-updated {
    animation: fade-out 3s ease-out;
    color: $extensions-primary;
}

.button-row button {
    border: 1px solid $looks-secondary;
    border-radius: 0.25rem;
    padding: 0.5rem 1.5rem;
    color: white;
    background: $looks-secondary;
    font-weight: bold;
    font-size: .875rem;
    cursor: pointer;
}

.button-row button:hover {
    background: $extensions-primary;
    box-shadow: 0 0 0 6px $looks-transparent;
}

.button-row button:disabled {
    background: $text-primary;
    border-color: $ui-black-transparent;
    box-shadow: none;
    opacity: 0.25;
}

@media screen and (max-height: 660px) {
    .modal-content {
        margin: 5vh auto;
        width: 90%;
    }
}

@media screen and (max-height: 540px) {
    .illustration {
        display: none;
    }
}
