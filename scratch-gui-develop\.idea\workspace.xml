<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bc0f85b2-2909-4edd-82ff-63dcdd66f86f" name="更改" comment="feat：完成解释执行C语言函数">
      <change afterPath="$PROJECT_DIR$/robot_sensor.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/menu-bar/menu-bar.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/menu-bar/menu-bar.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/menu-bar/menu-bar.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/menu-bar/menu-bar.jsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/lib/c-code-generator.js" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/lib/k1-vm-extension.js" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2vBWsITsvsuBoRotm4ctOsjao84" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Downloads/scratch-gui-develop/scratch-server&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.build.executor&quot;: &quot;Run&quot;,
    &quot;npm.start.executor&quot;: &quot;Run&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\Program Files\\JetBrains\\IntelliJ IDEA 2024.3.5\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Downloads\scratch-gui-develop\scratch-gui-develop\picoc-test" />
      <recent name="C:\Users\<USER>\Downloads\scratch-gui-develop\scratch-gui-develop\src\components\sprite-selector-item" />
      <recent name="C:\Users\<USER>\Downloads\scratch-gui-develop\scratch-gui-develop\generated" />
    </key>
  </component>
  <component name="RunManager" selected="npm.start">
    <configuration name="build" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="start" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="start" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.start" />
        <item itemvalue="npm.build" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="bc0f85b2-2909-4edd-82ff-63dcdd66f86f" name="更改" comment="" />
      <created>1743619152645</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743619152645</updated>
      <workItem from="1743619153900" duration="1054000" />
      <workItem from="1743620230065" duration="1000" />
      <workItem from="1743620241896" duration="5685000" />
      <workItem from="1743654502080" duration="307000" />
      <workItem from="1743656661575" duration="317000" />
      <workItem from="1743656989643" duration="1034000" />
      <workItem from="1743658082391" duration="1217000" />
      <workItem from="1743660732998" duration="1882000" />
      <workItem from="1745661711469" duration="1675000" />
      <workItem from="1746194874664" duration="353000" />
      <workItem from="1746434136575" duration="2162000" />
      <workItem from="1746569182928" duration="1877000" />
      <workItem from="1746580284127" duration="1412000" />
      <workItem from="1746626395534" duration="759000" />
      <workItem from="1746638958097" duration="11116000" />
      <workItem from="1746685457931" duration="5635000" />
      <workItem from="1746712783075" duration="1599000" />
      <workItem from="1746725880364" duration="2000" />
      <workItem from="1746725893890" duration="2517000" />
      <workItem from="1746767079355" duration="1000" />
      <workItem from="1746767088810" duration="5116000" />
      <workItem from="1746784149138" duration="2621000" />
      <workItem from="1746800443438" duration="11564000" />
      <workItem from="1746859910025" duration="1246000" />
      <workItem from="1746862551273" duration="3126000" />
      <workItem from="1747059937209" duration="1119000" />
      <workItem from="1747074750762" duration="5062000" />
      <workItem from="1747082159341" duration="6503000" />
      <workItem from="1747192412730" duration="205000" />
      <workItem from="1747192633861" duration="635000" />
      <workItem from="1747227367925" duration="1000" />
      <workItem from="1747227406290" duration="2980000" />
      <workItem from="1747247814496" duration="5157000" />
      <workItem from="1747295562101" duration="3805000" />
      <workItem from="1747675069359" duration="27000" />
      <workItem from="1747675107906" duration="6471000" />
      <workItem from="1747714299341" duration="589000" />
      <workItem from="1747714907141" duration="9177000" />
      <workItem from="1747757688036" duration="1774000" />
      <workItem from="1747767983747" duration="1930000" />
      <workItem from="1747783779253" duration="1906000" />
      <workItem from="1747788883412" duration="751000" />
      <workItem from="1747810585028" duration="659000" />
      <workItem from="1747813458919" duration="1268000" />
      <workItem from="1748247628754" duration="364000" />
      <workItem from="1748248055737" duration="120000" />
      <workItem from="1748248309614" duration="109000" />
      <workItem from="1748249814060" duration="2259000" />
      <workItem from="1748258759946" duration="4343000" />
      <workItem from="1748320169562" duration="3396000" />
      <workItem from="1748345902048" duration="2359000" />
      <workItem from="1748364865395" duration="4103000" />
      <workItem from="1748418564555" duration="2463000" />
      <workItem from="1748425424009" duration="3561000" />
      <workItem from="1748591454043" duration="6377000" />
      <workItem from="1748625610774" duration="1083000" />
      <workItem from="1748676380001" duration="1821000" />
      <workItem from="1748780528022" duration="3686000" />
      <workItem from="1748848639973" duration="1515000" />
      <workItem from="1748854050848" duration="389000" />
      <workItem from="1748854461862" duration="880000" />
      <workItem from="1748856279285" duration="922000" />
      <workItem from="1748857479487" duration="2670000" />
      <workItem from="1748860767550" duration="176000" />
      <workItem from="1748861489082" duration="1092000" />
    </task>
    <task id="LOCAL-00001" summary="初始化项目">
      <option name="closed" value="true" />
      <created>1743620712828</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1743620712828</updated>
    </task>
    <task id="LOCAL-00002" summary="新增右侧的C语言代码块">
      <option name="closed" value="true" />
      <created>1743621934708</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1743621934708</updated>
    </task>
    <task id="LOCAL-00003" summary="改进右侧的C语言代码块，增加了高亮显示">
      <option name="closed" value="true" />
      <created>1743625858143</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1743625858143</updated>
    </task>
    <task id="LOCAL-00004" summary="改进右侧的C语言代码块，导入了K1函数">
      <option name="closed" value="true" />
      <created>1746640444141</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1746640444141</updated>
    </task>
    <task id="LOCAL-00005" summary="准备回退">
      <option name="closed" value="true" />
      <created>1746642213575</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1746642213575</updated>
    </task>
    <task id="LOCAL-00006" summary="准备回退">
      <option name="closed" value="true" />
      <created>1746643650662</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1746643650662</updated>
    </task>
    <task id="LOCAL-00007" summary="准备回退">
      <option name="closed" value="true" />
      <created>1746643656540</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1746643656540</updated>
    </task>
    <task id="LOCAL-00008" summary="feat: 完善登录注册功能的前端页面。">
      <option name="closed" value="true" />
      <created>1746647227570</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1746647227570</updated>
    </task>
    <task id="LOCAL-00009" summary="feat: 完善登录注册功能的前端页面。">
      <option name="closed" value="true" />
      <created>1746647402150</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746647402150</updated>
    </task>
    <task id="LOCAL-00010" summary="feat: 完善登录注册功能的前端页面。但仍有bug">
      <option name="closed" value="true" />
      <created>1746653876782</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1746653876782</updated>
    </task>
    <task id="LOCAL-00011" summary="feat: 删除代码前留档">
      <option name="closed" value="true" />
      <created>1746813084458</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1746813084458</updated>
    </task>
    <task id="LOCAL-00012" summary="feat: 删除代码前留档">
      <option name="closed" value="true" />
      <created>1746814698702</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1746814698702</updated>
    </task>
    <task id="LOCAL-00013" summary="feat: 暂时完成login页面的登录功能，但主页的登录功能依然在，并且可以在未登录的情况下直接访问主页（需修改）">
      <option name="closed" value="true" />
      <created>1746827153051</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1746827153051</updated>
    </task>
    <task id="LOCAL-00014" summary="feat: 完善主页右上角的个人信息修改和项目信息显示，但目前样式依然不对">
      <option name="closed" value="true" />
      <created>1747082324971</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1747082324971</updated>
    </task>
    <task id="LOCAL-00015" summary="feat: 完善主页右上角的个人信息修改和项目信息显示，但目前样式依然不对">
      <option name="closed" value="true" />
      <created>1747083987535</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1747083987535</updated>
    </task>
    <task id="LOCAL-00016" summary="feat：完善个人信息页面">
      <option name="closed" value="true" />
      <created>1747085696325</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1747085696325</updated>
    </task>
    <task id="LOCAL-00017" summary="feat：完善个人信息页面和对应下拉框设置">
      <option name="closed" value="true" />
      <created>1747087032633</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1747087032633</updated>
    </task>
    <task id="LOCAL-00018" summary="feat：完善积木块的导出功能">
      <option name="closed" value="true" />
      <created>1747231896158</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1747231896158</updated>
    </task>
    <task id="LOCAL-00019" summary="feat：完善积木块的导入功能（通过数据库数据），但还差版本号显示">
      <option name="closed" value="true" />
      <created>1747250721106</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1747250721106</updated>
    </task>
    <task id="LOCAL-00020" summary="feat：初步实现版本号管理">
      <option name="closed" value="true" />
      <created>1747252587709</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1747252587709</updated>
    </task>
    <task id="LOCAL-00021" summary="feat：基本完全实现版本号管理">
      <option name="closed" value="true" />
      <created>1747259785971</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1747259785971</updated>
    </task>
    <task id="LOCAL-00022" summary="feat：基本完全实现k1函数的支持，包括导入和导出">
      <option name="closed" value="true" />
      <created>1747309960599</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1747309960599</updated>
    </task>
    <task id="LOCAL-00023" summary="feat：发生错误">
      <option name="closed" value="true" />
      <created>1747714774660</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1747714774660</updated>
    </task>
    <task id="LOCAL-00024" summary="feat：完成菜单栏和k1函数具体实现">
      <option name="closed" value="true" />
      <created>1747742284486</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1747742284486</updated>
    </task>
    <task id="LOCAL-00025" summary="feat：完成菜单栏和k1函数具体实现">
      <option name="closed" value="true" />
      <created>1747742313358</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1747742313358</updated>
    </task>
    <task id="LOCAL-00026" summary="feat：完成菜单栏修改和C代码运行按钮">
      <option name="closed" value="true" />
      <created>1747760540499</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1747760540499</updated>
    </task>
    <task id="LOCAL-00027" summary="feat：完成菜单栏修改和C">
      <option name="closed" value="true" />
      <created>1748343011838</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1748343011838</updated>
    </task>
    <task id="LOCAL-00028" summary="feat：完成菜单栏修改和C代码运行按钮">
      <option name="closed" value="true" />
      <created>1748849175564</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1748849175564</updated>
    </task>
    <task id="LOCAL-00029" summary="feat：完成解释执行C语言函数">
      <option name="closed" value="true" />
      <created>1748861772255</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1748861772255</updated>
    </task>
    <option name="localTasksCounter" value="30" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化项目" />
    <MESSAGE value="新增右侧的C语言代码块" />
    <MESSAGE value="改进右侧的C语言代码块，增加了高亮显示" />
    <MESSAGE value="改进右侧的C语言代码块，导入了K1函数" />
    <MESSAGE value="准备回退" />
    <MESSAGE value="feat: 完善登录注册功能的前端页面。" />
    <MESSAGE value="feat: 完善登录注册功能的前端页面。但仍有bug" />
    <MESSAGE value="feat: 删除代码前留档" />
    <MESSAGE value="feat: 暂时完成login页面的登录功能，但主页的登录功能依然在，并且可以在未登录的情况下直接访问主页（需修改）" />
    <MESSAGE value="feat: 完善主页右上角的个人信息修改和项目信息显示，但目前样式依然不对" />
    <MESSAGE value="feat：完善个人信息页面" />
    <MESSAGE value="feat：完善个人信息页面和对应下拉框设置" />
    <MESSAGE value="feat：完善积木块的导出功能" />
    <MESSAGE value="feat：完善积木块的导入功能（通过数据库数据），但还差版本号显示" />
    <MESSAGE value="feat：初步实现版本号管理" />
    <MESSAGE value="feat：基本完全实现版本号管理" />
    <MESSAGE value="feat：基本完全实现k1函数的支持，包括导入和导出" />
    <MESSAGE value="feat：发生错误" />
    <MESSAGE value="feat：完成菜单栏和k1函数具体实现" />
    <MESSAGE value="feat：完成菜单栏修改和C" />
    <MESSAGE value="feat：完善解释执行c代码前" />
    <MESSAGE value="feat：完成菜单栏修改和C代码运行按钮" />
    <MESSAGE value="feat：完成解释执行C语言函数" />
    <option name="LAST_COMMIT_MESSAGE" value="feat：完成解释执行C语言函数" />
  </component>
</project>