@import "../../css/colors.css";
@import "../../css/units.css";

.editor-container {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: calc(2 * $space);
}

.row {
    display: flex;
    flex-direction: row;
    align-items: center;
}

[dir="rtl"] .row-reverse {
    flex-direction: row-reverse;
}

.row + .row {
    margin-top: calc(2 * $space);
}

.input-group {
    display: flex;
    flex-direction: row;
}

[dir="ltr"] .input-group + .input-group {
    margin-left: calc(2 * $space);
}

[dir="rtl"] .input-group + .input-group {
    margin-right: calc(2 * $space);
}

[dir="ltr"] .input-group {
    padding-right: calc(2 * $space);
    border-right: 1px dashed $ui-black-transparent;
}

[dir="rtl"] .input-group {
    padding-left: calc(2 * $space);
    border-left: 1px dashed $ui-black-transparent;
}

[dir="rtl"] .row-reverse > .input-group {
    padding-left: 0;
    padding-right: calc(2 * $space);
    border-left: none;
    border-right: 1px dashed $ui-black-transparent;
}

.waveform-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;

    position: relative;

    background: hsla(300, 53%, 60%, 0.15);
    border: 1px solid $ui-black-transparent;
    border-radius: 5px;

    margin-top: 20px;
    margin-bottom: 20px;
}

$border-radius: 0.25rem;

.button {
    height: 2rem;
    padding: 0.35rem;
    outline: none;
    background: white;
    border-radius: $border-radius;
    border: 1px solid $ui-black-transparent;
    cursor: pointer;
    font-size: 0.85rem;
    user-select: none;
    margin: 0px;
    position: relative;
}

.button:active {
    background: $looks-transparent;
}

.button:focus::before {
    content: "";
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    border: 1px solid $looks-secondary;
    z-index: 1;
}

.button > img {
    flex-grow: 1;
    max-width: 100%;
    max-height: 100%;
    min-width: 1.25rem;
}

.round-button {
    height: 3rem;
    width: 3rem;
    outline: none;
    background: $looks-secondary;
    border-radius: 100%;
    border: 4px solid $ui-white-dim;
    cursor: pointer;
    padding: 0.75rem;
    user-select: none;
}

.round-button > img {
    flex-grow: 1;
    max-width: 100%;
    max-height: 100%;
    /*min-width: 1.5rem;*/
}

[dir="rtl"] .undo-icon, [dir="rtl"] .redo-icon {
    transform: scaleX(-1);
}

.trim-button {
    display: flex;
    align-items: center;
    color: $text-primary;
    font-size: 0.625rem;
    user-select: none;

}

[dir="ltr"] .trim-button {
    margin-left: 1rem;
}

[dir="rtl"] .trim-button {
    margin-right: 1rem;
}

.trim-button > img {
    width: 1.25rem;
}

.effect-button {
    flex-basis: 60px;
    color: $text-primary;
    font-size: 0.625rem;
    user-select: none;
    padding: 0.25rem 0;
}

.effect-button + .effect-button {
    margin: 0;
}

.effect-button img {
    width: 1.25rem;
    height: 1.25rem;
    margin-bottom: -0.375rem;
}

.tool-button {
    flex-basis: 60px;
    color: $text-primary;
    font-size: 0.625rem;
    user-select: none;
    padding: 0.25rem 0;
}

.tool-button + .tool-button {
    margin: 0;
}

.tool-button img {
    width: 4rem;
    height: 1.5rem;
    margin-bottom: -0.375rem;
}

[dir="rtl"] .flip-in-rtl img {
    transform: scaleX(-1);
}

[dir="ltr"] .button-group {
    margin-left: 1rem;
}

[dir="rtl"] .button-group {
    margin-right: 1rem;
}

.button-group {
    display: flex;
}

.button-group .button {
    border-radius: 0;
}

.button:not(:last-child) {
    border-right: none;
}

[dir="rtl"] .button:not(:last-child) {
    border-left: none;
    border-right: 1px solid $ui-black-transparent;
}

.button-group .button:first-child,
[dir="rtl"] .button-group .button:last-child,
.button-group .button:first-child:focus::before,
[dir="rtl"] .button-group .button:last-child:focus::before {
    border-radius: $form-radius 0 0 $form-radius;
}

.button-group .button:last-child,
[dir="rtl"] .button-group .button:first-child,
.button-group .button:last-child:focus::before,
[dir="rtl"] .button-group .button:first-child:focus::before {
    border-radius: 0 $form-radius $form-radius 0;
}

.button:disabled > img {
    opacity: 0.25;
}
