@startuml 虚拟机执行模块时序图

' 定义参与者
actor 用户
participant "编辑器" as Editor
participant "虚拟机" as VM
participant "执行引擎" as Engine
participant "状态管理器" as StateMgr

' 核心流程
用户 -> Editor: 加载项目
Editor -> VM: VirtualMachine.loadProject(projectData)
VM -> VM: 解析积木结构生成指令树
VM -> StateMgr: 初始化变量环境
VM --> Editor: 项目加载完成

用户 -> Editor: 点击绿旗按钮
Editor -> VM: ExecutionEngine.executeBlockSequence()
activate VM

loop 程序执行
    VM -> Engine: 执行当前积木指令
    Engine -> StateMgr: 更新状态
    StateMgr -> Editor: 更新界面显示

    alt 条件分支或循环
        Engine -> Engine: 处理控制流
    end

    Engine -> Engine: 移动到下一条指令
end

alt 用户中断
    用户 -> Editor: 点击停止按钮
    Editor -> VM: ExecutionController.stop()
    VM -> Engine: 中断执行
else 程序自然结束
    Engine -> VM: 执行完成
end

VM --> 用户: 执行结束
deactivate VM

@enduml
