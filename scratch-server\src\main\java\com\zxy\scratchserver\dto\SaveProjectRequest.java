package com.zxy.scratchserver.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 保存项目请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaveProjectRequest {

    private Long projectId; // 项目ID，如果为null则创建新项目，否则更新现有项目

    private String projectName;

    @ToString.Exclude // 避免在日志中打印大量二进制数据
    private byte[] projectBlocks;

    @Override
    public String toString() {
        return "SaveProjectRequest{" +
                "projectId=" + projectId +
                ", projectName='" + projectName + '\'' +
                ", projectBlocksLength=" + (projectBlocks != null ? projectBlocks.length : 0) +
                '}';
    }
}
