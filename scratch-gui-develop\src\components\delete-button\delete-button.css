@import "../../css/colors.css";
@import "../../css/units.css";

/* wrapper to allow for touch slop if we decide to add it */
.delete-button {
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    cursor: pointer;
    transition: all 0.15s ease-out;
}

.delete-button-visible {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;  /* Mask the icon animation */
  width: 1.75rem;
  height: 1.75rem;
  box-shadow: 0px 0px 0px 2px $looks-transparent;
  background-color: $looks-secondary;
  color: $ui-white;
  border-radius: 50%;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  user-select: none;
  cursor: pointer;
  transition: all 0.15s ease-out;
}

.delete-button-clicked {
  background-color: $data-primary;
}

.delete-icon {
    position: relative;
    margin: 0.25rem;
    user-select: none;
    transform-origin: 50%;
}
