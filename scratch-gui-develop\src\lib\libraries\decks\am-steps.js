// Intro
import introMove from './steps/intro-1-move.am.gif';
import introSay from './steps/intro-2-say.am.gif';
import introGreenFlag from './steps/intro-3-green-flag.am.gif';

// Text to Speech
import speechAddExtension from './steps/speech-add-extension.am.gif';
import speechSaySomething from './steps/speech-say-something.am.png';
import speechSetVoice from './steps/speech-set-voice.am.png';
import speechMoveAround from './steps/speech-move-around.am.png';
import speechAddBackdrop from './steps/pick-backdrop.LTR.gif';
import speechAddSprite from './steps/speech-add-sprite.LTR.gif';
import speechSong from './steps/speech-song.am.png';
import speechChangeColor from './steps/speech-change-color.am.png';
import speechSpin from './steps/speech-spin.am.png';
import speechGrowShrink from './steps/speech-grow-shrink.am.png';

// Cartoon Network
import cnShowCharacter from './steps/cn-show-character.LTR.gif';
import cnSay from './steps/cn-say.am.png';
import cnGlide from './steps/cn-glide.am.png';
import cnPickSprite from './steps/cn-pick-sprite.LTR.gif';
import cnCollect from './steps/cn-collect.am.png';
import cnVariable from './steps/add-variable.am.gif';
import cnScore from './steps/cn-score.am.png';
import cnBackdrop from './steps/cn-backdrop.am.png';

// Add sprite
import addSprite from './steps/add-sprite.LTR.gif';

// Animate a name
import namePickLetter from './steps/name-pick-letter.LTR.gif';
import namePlaySound from './steps/name-play-sound.am.png';
import namePickLetter2 from './steps/name-pick-letter2.LTR.gif';
import nameChangeColor from './steps/name-change-color.am.png';
import nameSpin from './steps/name-spin.am.png';
import nameGrow from './steps/name-grow.am.png';

// Make Music
import musicPickInstrument from './steps/music-pick-instrument.LTR.gif';
import musicPlaySound from './steps/music-play-sound.am.png';
import musicMakeSong from './steps/music-make-song.am.png';
import musicMakeBeat from './steps/music-make-beat.am.png';
import musicMakeBeatbox from './steps/music-make-beatbox.am.png';

// Chase-Game
import chaseGameAddBackdrop from './steps/chase-game-add-backdrop.LTR.gif';
import chaseGameAddSprite1 from './steps/chase-game-add-sprite1.LTR.gif';
import chaseGameRightLeft from './steps/chase-game-right-left.am.png';
import chaseGameUpDown from './steps/chase-game-up-down.am.png';
import chaseGameAddSprite2 from './steps/chase-game-add-sprite2.LTR.gif';
import chaseGameMoveRandomly from './steps/chase-game-move-randomly.am.png';
import chaseGamePlaySound from './steps/chase-game-play-sound.am.png';
import chaseGameAddVariable from './steps/add-variable.am.gif';
import chaseGameChangeScore from './steps/chase-game-change-score.am.png';

// Clicker-Game (Pop Game)
import popGamePickSprite from './steps/pop-game-pick-sprite.LTR.gif';
import popGamePlaySound from './steps/pop-game-play-sound.am.png';
import popGameAddScore from './steps/add-variable.am.gif';
import popGameChangeScore from './steps/pop-game-change-score.am.png';
import popGameRandomPosition from './steps/pop-game-random-position.am.png';
import popGameChangeColor from './steps/pop-game-change-color.am.png';
import popGameResetScore from './steps/pop-game-reset-score.am.png';

// Animate A Character
import animateCharPickBackdrop from './steps/pick-backdrop.LTR.gif';
import animateCharPickSprite from './steps/animate-char-pick-sprite.LTR.gif';
import animateCharSaySomething from './steps/animate-char-say-something.am.png';
import animateCharAddSound from './steps/animate-char-add-sound.am.png';
import animateCharTalk from './steps/animate-char-talk.am.png';
import animateCharMove from './steps/animate-char-move.am.png';
import animateCharJump from './steps/animate-char-jump.am.png';
import animateCharChangeColor from './steps/animate-char-change-color.am.png';

// Tell A Story
import storyPickBackdrop from './steps/story-pick-backdrop.LTR.gif';
import storyPickSprite from './steps/story-pick-sprite.LTR.gif';
import storySaySomething from './steps/story-say-something.am.png';
import storyPickSprite2 from './steps/story-pick-sprite2.LTR.gif';
import storyFlip from './steps/story-flip.am.gif';
import storyConversation from './steps/story-conversation.am.png';
import storyPickBackdrop2 from './steps/story-pick-backdrop2.LTR.gif';
import storySwitchBackdrop from './steps/story-switch-backdrop.am.png';
import storyHideCharacter from './steps/story-hide-character.am.png';
import storyShowCharacter from './steps/story-show-character.am.png';

// Video Sensing
import videoAddExtension from './steps/video-add-extension.am.gif';
import videoPet from './steps/video-pet.am.png';
import videoAnimate from './steps/video-animate.am.png';
import videoPop from './steps/video-pop.am.png';

// Make it Fly
import flyChooseBackdrop from './steps/fly-choose-backdrop.LTR.gif';
import flyChooseCharacter from './steps/fly-choose-character.LTR.png';
import flySaySomething from './steps/fly-say-something.am.png';
import flyMoveArrows from './steps/fly-make-interactive.am.png';
import flyChooseObject from './steps/fly-object-to-collect.LTR.png';
import flyFlyingObject from './steps/fly-flying-heart.am.png';
import flySelectFlyingSprite from './steps/fly-select-flyer.LTR.png';
import flyAddScore from './steps/add-variable.am.gif';
import flyKeepScore from './steps/fly-keep-score.am.png';
import flyAddScenery from './steps/fly-choose-scenery.LTR.gif';
import flyMoveScenery from './steps/fly-move-scenery.am.png';
import flySwitchLooks from './steps/fly-switch-costume.am.png';

// Pong
import pongAddBackdrop from './steps/pong-add-backdrop.LTR.png';
import pongAddBallSprite from './steps/pong-add-ball-sprite.LTR.png';
import pongBounceAround from './steps/pong-bounce-around.am.png';
import pongAddPaddle from './steps/pong-add-a-paddle.LTR.gif';
import pongMoveThePaddle from './steps/pong-move-the-paddle.am.png';
import pongSelectBallSprite from './steps/pong-select-ball.LTR.png';
import pongAddMoreCodeToBall from './steps/pong-add-code-to-ball.am.png';
import pongAddAScore from './steps/add-variable.am.gif';
import pongChooseScoreFromMenu from './steps/pong-choose-score.am.png';
import pongInsertChangeScoreBlock from './steps/pong-insert-change-score.am.png';
import pongResetScore from './steps/pong-reset-score.am.png';
import pongAddLineSprite from './steps/pong-add-line.LTR.gif';
import pongGameOver from './steps/pong-game-over.am.png';

// Imagine a World
import imagineTypeWhatYouWant from './steps/imagine-type-what-you-want.am.png';
import imagineClickGreenFlag from './steps/imagine-click-green-flag.am.png';
import imagineChooseBackdrop from './steps/imagine-choose-backdrop.LTR.png';
import imagineChooseSprite from './steps/imagine-choose-any-sprite.LTR.png';
import imagineFlyAround from './steps/imagine-fly-around.am.png';
import imagineChooseAnotherSprite from './steps/imagine-choose-another-sprite.LTR.png';
import imagineLeftRight from './steps/imagine-left-right.am.png';
import imagineUpDown from './steps/imagine-up-down.am.png';
import imagineChangeCostumes from './steps/imagine-change-costumes.am.png';
import imagineGlideToPoint from './steps/imagine-glide-to-point.am.png';
import imagineGrowShrink from './steps/imagine-grow-shrink.am.png';
import imagineChooseAnotherBackdrop from './steps/imagine-choose-another-backdrop.LTR.png';
import imagineSwitchBackdrops from './steps/imagine-switch-backdrops.am.png';
import imagineRecordASound from './steps/imagine-record-a-sound.am.gif';
import imagineChooseSound from './steps/imagine-choose-sound.am.png';

// Add a Backdrop
import addBackdrop from './steps/add-backdrop.LTR.png';

// Add Effects
import addEffects from './steps/add-effects.am.png';

// Hide and Show
import hideAndShow from './steps/hide-show.am.png';

// Switch Costumes
import switchCostumes from './steps/switch-costumes.am.png';

// Change Size
import changeSize from './steps/change-size.am.png';

// Spin
import spinTurn from './steps/spin-turn.am.png';
import spinPointInDirection from './steps/spin-point-in-direction.am.png';

// Record a Sound
import recordASoundSoundsTab from './steps/record-a-sound-sounds-tab.am.png';
import recordASoundClickRecord from './steps/record-a-sound-click-record.am.png';
import recordASoundPressRecordButton from './steps/record-a-sound-press-record-button.am.png';
import recordASoundChooseSound from './steps/record-a-sound-choose-sound.am.png';
import recordASoundPlayYourSound from './steps/record-a-sound-play-your-sound.am.png';

// Use Arrow Keys
import moveArrowKeysLeftRight from './steps/move-arrow-keys-left-right.am.png';
import moveArrowKeysUpDown from './steps/move-arrow-keys-up-down.am.png';

// Glide Around
import glideAroundBackAndForth from './steps/glide-around-back-and-forth.am.png';
import glideAroundPoint from './steps/glide-around-point.am.png';

// Code a Cartoon
import codeCartoonSaySomething from './steps/code-cartoon-01-say-something.am.png';
import codeCartoonAnimate from './steps/code-cartoon-02-animate.am.png';
import codeCartoonSelectDifferentCharacter from './steps/code-cartoon-03-select-different-character.LTR.png';
import codeCartoonUseMinusSign from './steps/code-cartoon-04-use-minus-sign.am.png';
import codeCartoonGrowShrink from './steps/code-cartoon-05-grow-shrink.am.png';
import codeCartoonSelectDifferentCharacter2 from './steps/code-cartoon-06-select-another-different-character.LTR.png';
import codeCartoonJump from './steps/code-cartoon-07-jump.am.png';
import codeCartoonChangeScenes from './steps/code-cartoon-08-change-scenes.am.png';
import codeCartoonGlideAround from './steps/code-cartoon-09-glide-around.am.png';
import codeCartoonChangeCostumes from './steps/code-cartoon-10-change-costumes.am.png';
import codeCartoonChooseMoreCharacters from './steps/code-cartoon-11-choose-more-characters.LTR.png';

// Talking Tales
import talesAddExtension from './steps/speech-add-extension.am.gif';
import talesChooseSprite from './steps/talking-2-choose-sprite.LTR.png';
import talesSaySomething from './steps/talking-3-say-something.am.png';
import talesChooseBackdrop from './steps/talking-4-choose-backdrop.LTR.png';
import talesSwitchBackdrop from './steps/talking-5-switch-backdrop.am.png';
import talesChooseAnotherSprite from './steps/talking-6-choose-another-sprite.LTR.png';
import talesMoveAround from './steps/talking-7-move-around.am.png';
import talesChooseAnotherBackdrop from './steps/talking-8-choose-another-backdrop.LTR.png';
import talesAnimateTalking from './steps/talking-9-animate.am.png';
import talesChooseThirdBackdrop from './steps/talking-10-choose-third-backdrop.LTR.png';
import talesChooseSound from './steps/talking-11-choose-sound.am.gif';
import talesDanceMoves from './steps/talking-12-dance-moves.am.png';
import talesAskAnswer from './steps/talking-13-ask-and-answer.am.png';

const amImages = {
    // Intro
    introMove: introMove,
    introSay: introSay,
    introGreenFlag: introGreenFlag,

    // Text to Speech
    speechAddExtension: speechAddExtension,
    speechSaySomething: speechSaySomething,
    speechSetVoice: speechSetVoice,
    speechMoveAround: speechMoveAround,
    speechAddBackdrop: speechAddBackdrop,
    speechAddSprite: speechAddSprite,
    speechSong: speechSong,
    speechChangeColor: speechChangeColor,
    speechSpin: speechSpin,
    speechGrowShrink: speechGrowShrink,

    // Cartoon Network
    cnShowCharacter: cnShowCharacter,
    cnSay: cnSay,
    cnGlide: cnGlide,
    cnPickSprite: cnPickSprite,
    cnCollect: cnCollect,
    cnVariable: cnVariable,
    cnScore: cnScore,
    cnBackdrop: cnBackdrop,

    // Add sprite
    addSprite: addSprite,

    // Animate a name
    namePickLetter: namePickLetter,
    namePlaySound: namePlaySound,
    namePickLetter2: namePickLetter2,
    nameChangeColor: nameChangeColor,
    nameSpin: nameSpin,
    nameGrow: nameGrow,

    // Make-Music
    musicPickInstrument: musicPickInstrument,
    musicPlaySound: musicPlaySound,
    musicMakeSong: musicMakeSong,
    musicMakeBeat: musicMakeBeat,
    musicMakeBeatbox: musicMakeBeatbox,

    // Chase-Game
    chaseGameAddBackdrop: chaseGameAddBackdrop,
    chaseGameAddSprite1: chaseGameAddSprite1,
    chaseGameRightLeft: chaseGameRightLeft,
    chaseGameUpDown: chaseGameUpDown,
    chaseGameAddSprite2: chaseGameAddSprite2,
    chaseGameMoveRandomly: chaseGameMoveRandomly,
    chaseGamePlaySound: chaseGamePlaySound,
    chaseGameAddVariable: chaseGameAddVariable,
    chaseGameChangeScore: chaseGameChangeScore,

    // Make-A-Pop/Clicker Game
    popGamePickSprite: popGamePickSprite,
    popGamePlaySound: popGamePlaySound,
    popGameAddScore: popGameAddScore,
    popGameChangeScore: popGameChangeScore,
    popGameRandomPosition: popGameRandomPosition,
    popGameChangeColor: popGameChangeColor,
    popGameResetScore: popGameResetScore,

    // Animate A Character
    animateCharPickBackdrop: animateCharPickBackdrop,
    animateCharPickSprite: animateCharPickSprite,
    animateCharSaySomething: animateCharSaySomething,
    animateCharAddSound: animateCharAddSound,
    animateCharTalk: animateCharTalk,
    animateCharMove: animateCharMove,
    animateCharJump: animateCharJump,
    animateCharChangeColor: animateCharChangeColor,

    // Tell A Story
    storyPickBackdrop: storyPickBackdrop,
    storyPickSprite: storyPickSprite,
    storySaySomething: storySaySomething,
    storyPickSprite2: storyPickSprite2,
    storyFlip: storyFlip,
    storyConversation: storyConversation,
    storyPickBackdrop2: storyPickBackdrop2,
    storySwitchBackdrop: storySwitchBackdrop,
    storyHideCharacter: storyHideCharacter,
    storyShowCharacter: storyShowCharacter,

    // Video Sensing
    videoAddExtension: videoAddExtension,
    videoPet: videoPet,
    videoAnimate: videoAnimate,
    videoPop: videoPop,

    // Make it Fly
    flyChooseBackdrop: flyChooseBackdrop,
    flyChooseCharacter: flyChooseCharacter,
    flySaySomething: flySaySomething,
    flyMoveArrows: flyMoveArrows,
    flyChooseObject: flyChooseObject,
    flyFlyingObject: flyFlyingObject,
    flySelectFlyingSprite: flySelectFlyingSprite,
    flyAddScore: flyAddScore,
    flyKeepScore: flyKeepScore,
    flyAddScenery: flyAddScenery,
    flyMoveScenery: flyMoveScenery,
    flySwitchLooks: flySwitchLooks,

    // Pong
    pongAddBackdrop: pongAddBackdrop,
    pongAddBallSprite: pongAddBallSprite,
    pongBounceAround: pongBounceAround,
    pongAddPaddle: pongAddPaddle,
    pongMoveThePaddle: pongMoveThePaddle,
    pongSelectBallSprite: pongSelectBallSprite,
    pongAddMoreCodeToBall: pongAddMoreCodeToBall,
    pongAddAScore: pongAddAScore,
    pongChooseScoreFromMenu: pongChooseScoreFromMenu,
    pongInsertChangeScoreBlock: pongInsertChangeScoreBlock,
    pongResetScore: pongResetScore,
    pongAddLineSprite: pongAddLineSprite,
    pongGameOver: pongGameOver,

    // Imagine a World
    imagineTypeWhatYouWant: imagineTypeWhatYouWant,
    imagineClickGreenFlag: imagineClickGreenFlag,
    imagineChooseBackdrop: imagineChooseBackdrop,
    imagineChooseSprite: imagineChooseSprite,
    imagineFlyAround: imagineFlyAround,
    imagineChooseAnotherSprite: imagineChooseAnotherSprite,
    imagineLeftRight: imagineLeftRight,
    imagineUpDown: imagineUpDown,
    imagineChangeCostumes: imagineChangeCostumes,
    imagineGlideToPoint: imagineGlideToPoint,
    imagineGrowShrink: imagineGrowShrink,
    imagineChooseAnotherBackdrop: imagineChooseAnotherBackdrop,
    imagineSwitchBackdrops: imagineSwitchBackdrops,
    imagineRecordASound: imagineRecordASound,
    imagineChooseSound: imagineChooseSound,

    // Add a Backdrop
    addBackdrop: addBackdrop,

    // Add Effects
    addEffects: addEffects,

    // Hide and Show
    hideAndShow: hideAndShow,

    // Switch Costumes
    switchCostumes: switchCostumes,

    // Change Size
    changeSize: changeSize,

    // Spin
    spinTurn: spinTurn,
    spinPointInDirection: spinPointInDirection,

    // Record a Sound
    recordASoundSoundsTab: recordASoundSoundsTab,
    recordASoundClickRecord: recordASoundClickRecord,
    recordASoundPressRecordButton: recordASoundPressRecordButton,
    recordASoundChooseSound: recordASoundChooseSound,
    recordASoundPlayYourSound: recordASoundPlayYourSound,

    // Use Arrow Keys
    moveArrowKeysLeftRight: moveArrowKeysLeftRight,
    moveArrowKeysUpDown: moveArrowKeysUpDown,

    // Glide Around
    glideAroundBackAndForth: glideAroundBackAndForth,
    glideAroundPoint: glideAroundPoint,

    // Code a Cartoon
    codeCartoonSaySomething: codeCartoonSaySomething,
    codeCartoonAnimate: codeCartoonAnimate,
    codeCartoonSelectDifferentCharacter: codeCartoonSelectDifferentCharacter,
    codeCartoonUseMinusSign: codeCartoonUseMinusSign,
    codeCartoonGrowShrink: codeCartoonGrowShrink,
    codeCartoonSelectDifferentCharacter2: codeCartoonSelectDifferentCharacter2,
    codeCartoonJump: codeCartoonJump,
    codeCartoonChangeScenes: codeCartoonChangeScenes,
    codeCartoonGlideAround: codeCartoonGlideAround,
    codeCartoonChangeCostumes: codeCartoonChangeCostumes,
    codeCartoonChooseMoreCharacters: codeCartoonChooseMoreCharacters,

    // Talking Tales
    talesAddExtension: talesAddExtension,
    talesChooseSprite: talesChooseSprite,
    talesSaySomething: talesSaySomething,
    talesAskAnswer: talesAskAnswer,
    talesChooseBackdrop: talesChooseBackdrop,
    talesSwitchBackdrop: talesSwitchBackdrop,
    talesChooseAnotherSprite: talesChooseAnotherSprite,
    talesMoveAround: talesMoveAround,
    talesChooseAnotherBackdrop: talesChooseAnotherBackdrop,
    talesAnimateTalking: talesAnimateTalking,
    talesChooseThirdBackdrop: talesChooseThirdBackdrop,
    talesChooseSound: talesChooseSound,
    talesDanceMoves: talesDanceMoves
};

export {amImages};
