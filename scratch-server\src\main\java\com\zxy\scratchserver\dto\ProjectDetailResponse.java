package com.zxy.scratchserver.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Base64;

/**
 * 项目详情响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectDetailResponse {
    private Long projectId;
    private Long userId;
    private String projectName;
    private byte[] projectBlocks;
    private LocalDateTime projectCreatedAt;
    private String username; // 项目创建者用户名

    /**
     * 获取Base64编码的项目数据
     * 用于JSON序列化时避免二进制数据的问题
     * @return Base64编码的项目数据
     */
    public String getProjectBlocksBase64() {
        if (projectBlocks == null) {
            return null;
        }
        return Base64.getEncoder().encodeToString(projectBlocks);
    }
}
