<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fd69c9d0-c51b-4578-8fec-aa66ac92f9c1" name="更改" comment="feat：发生错误" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2wsLeBV4VhWlxVpfiKPeNz1SZkC" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Batch.install-gcc.executor&quot;: &quot;Debug&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.ScratchServerApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/scratch-gui-develop/scratch-gui-develop&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;File.Encoding&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="RunManager">
    <configuration name="ScratchServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="scratch-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zxy.scratchserver.ScratchServerApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zxy.scratchserver.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.ScratchServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="fd69c9d0-c51b-4578-8fec-aa66ac92f9c1" name="更改" comment="" />
      <created>1746825428004</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746825428004</updated>
      <workItem from="1746825429211" duration="1872000" />
      <workItem from="1746859935061" duration="626000" />
      <workItem from="1746862534556" duration="5269000" />
      <workItem from="1747034412695" duration="2062000" />
      <workItem from="1747059923457" duration="1025000" />
      <workItem from="1747074589032" duration="4705000" />
      <workItem from="1747082167275" duration="6201000" />
      <workItem from="1747192650887" duration="1221000" />
      <workItem from="1747227445884" duration="5037000" />
      <workItem from="1747247825577" duration="10588000" />
      <workItem from="1747295547617" duration="4822000" />
      <workItem from="1747675061071" duration="35000" />
      <workItem from="1747675170618" duration="6681000" />
      <workItem from="1747714306622" duration="573000" />
      <workItem from="1747714949231" duration="6023000" />
      <workItem from="1747757720500" duration="1700000" />
      <workItem from="1747767994409" duration="1796000" />
      <workItem from="1747783762340" duration="720000" />
      <workItem from="1747788879101" duration="699000" />
      <workItem from="1747810476808" duration="741000" />
      <workItem from="1747813452253" duration="2390000" />
      <workItem from="1748247575289" duration="417000" />
      <workItem from="1748248073071" duration="103000" />
      <workItem from="1748248402959" duration="247000" />
      <workItem from="1748248661344" duration="15000" />
      <workItem from="1748248707602" duration="2000" />
      <workItem from="1748248720920" duration="147000" />
      <workItem from="1748249009402" duration="207000" />
      <workItem from="1748249233801" duration="54000" />
      <workItem from="1748249749766" duration="32000" />
      <workItem from="1748249795920" duration="1581000" />
      <workItem from="1748258738885" duration="3857000" />
      <workItem from="1748320155488" duration="1916000" />
      <workItem from="1748345894200" duration="873000" />
      <workItem from="1748856465072" duration="461000" />
      <workItem from="1748857134670" duration="75000" />
      <workItem from="1748857496983" duration="2403000" />
      <workItem from="1748861232060" duration="127000" />
      <workItem from="1748861483319" duration="688000" />
    </task>
    <task id="LOCAL-00001" summary="初始化后端项目，现在可以正常处理前端的请求了">
      <option name="closed" value="true" />
      <created>1746826978371</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1746826978371</updated>
    </task>
    <task id="LOCAL-00002" summary="feat：提供项目projects和个人信息相关的api和逻辑">
      <option name="closed" value="true" />
      <created>1747082219554</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747082219554</updated>
    </task>
    <task id="LOCAL-00003" summary="feat：修改项目projects和个人信息相关的api和逻辑">
      <option name="closed" value="true" />
      <created>1747084054406</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747084054406</updated>
    </task>
    <task id="LOCAL-00004" summary="feat：完善个人信息页面">
      <option name="closed" value="true" />
      <created>1747085686080</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1747085686080</updated>
    </task>
    <task id="LOCAL-00005" summary="feat：完善积木块的导出功能">
      <option name="closed" value="true" />
      <created>1747231912835</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1747231912835</updated>
    </task>
    <task id="LOCAL-00006" summary="feat：完善积木块的导入功能（通过数据库数据），但还差版本号显示">
      <option name="closed" value="true" />
      <created>1747250689519</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1747250689519</updated>
    </task>
    <task id="LOCAL-00007" summary="feat：初步实现版本号管理">
      <option name="closed" value="true" />
      <created>1747252577844</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1747252577844</updated>
    </task>
    <task id="LOCAL-00008" summary="feat：基本完全实现版本号管理">
      <option name="closed" value="true" />
      <created>1747259769844</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1747259769844</updated>
    </task>
    <task id="LOCAL-00009" summary="feat：发生错误">
      <option name="closed" value="true" />
      <created>1747714851131</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1747714851131</updated>
    </task>
    <task id="LOCAL-00010" summary="feat：发生错误">
      <option name="closed" value="true" />
      <created>1747760620356</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1747760620356</updated>
    </task>
    <task id="LOCAL-00011" summary="feat：发生错误">
      <option name="closed" value="true" />
      <created>1747760628787</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1747760628787</updated>
    </task>
    <task id="LOCAL-00012" summary="feat：发生错误">
      <option name="closed" value="true" />
      <created>1748250099106</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1748250099106</updated>
    </task>
    <option name="localTasksCounter" value="13" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="初始化后端项目，现在可以正常处理前端的请求了" />
    <MESSAGE value="feat：提供项目projects和个人信息相关的api和逻辑" />
    <MESSAGE value="feat：修改项目projects和个人信息相关的api和逻辑" />
    <MESSAGE value="feat：完善个人信息页面" />
    <MESSAGE value="feat：完善积木块的导出功能" />
    <MESSAGE value="feat：完善积木块的导入功能（通过数据库数据），但还差版本号显示" />
    <MESSAGE value="feat：初步实现版本号管理" />
    <MESSAGE value="feat：基本完全实现版本号管理" />
    <MESSAGE value="feat：发生" />
    <MESSAGE value="feat：发生错误" />
    <option name="LAST_COMMIT_MESSAGE" value="feat：发生错误" />
  </component>
</project>