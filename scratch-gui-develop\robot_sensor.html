<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机器人模拟器</title>
    <style>
        /* 禁止页面滚动 */
        html, body {
            overflow: hidden;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            background-color: #f8f9fa;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0;
            position: relative;
        }

        .container {
            width: 100%;
            height: 100%;
            padding: 8px;
            display: flex;
            flex-direction: column;
        }

        .robot-status {
            background-color: #fff;
            padding: 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 8px;
            height: 100px;
            flex-shrink: 0;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            align-items: center;
            height: 100%;
        }

        .status-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
        }

        .status-title {
            font-weight: 600;
            color: #666;
            font-size: 12px;
        }

        .color-sensors {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        .color-sensor {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 2px solid #666;
            background-color: white;
            transition: background-color 0.3s;
        }

        .color-sensor.black {
            background-color: black;
        }

        .color-sensor.red {
            background-color: #ff4500;
        }

        .color-sensor.blue {
            background-color: #0000ff;
        }

        .color-sensor.green {
            background-color: #008000;
        }

        .color-sensor.yellow {
            background-color: #FFD700;
        }

        .direction-indicator {
            width: 32px;
            height: 32px;
            border: 2px solid #666;
            border-radius: 50%;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .direction-arrow {
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 16px solid #666;
            transform-origin: center;
            transition: transform 0.3s;
        }

        .direction-angle {
            font-size: 12px;
            font-weight: bold;
            color: #666;
            margin-top: 2px;
        }

        .ultrasonic-value {
            font-size: 18px;
            font-weight: bold;
            color: #2196F3;
        }

        .coordinates {
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
            font-family: monospace;
        }

        #ultrasonicNoDetect {
            width: 40px;
            height: 2px;
            background: #888;
            margin: 6px auto 0 auto;
            border-radius: 1px;
        }

        /* 模拟器头部 */
        .simulator-header {
            background-color: #fff;
            padding: 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 8px;
            flex-shrink: 0;
        }

        .simulator-header h2 {
            font-size: 16px;
            margin: 0 0 6px 0;
        }

        .simulator-header p {
            font-size: 12px;
            margin: 0 0 8px 0;
            line-height: 1.3;
        }

        /* 模拟器主内容区 */
        .simulator-content {
            background-color: #fff;
            padding: 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-grow: 1;
            min-height: 0;
        }

        #gridCanvas {
            border: 1px solid #eee;
            background-color: white;
            cursor: pointer;
            outline: none;
        }

        .controls {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            flex-wrap: wrap;
            align-items: center;
        }

        .path-selector {
            display: flex;
            gap: 6px;
            margin-top: 0;
        }

        .path-button {
            padding: 6px 12px;
            background-color: #6c6c6c;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: 500;
            font-size: 12px;
        }

        .path-button:hover {
            background-color: #444;
        }

        .path-button.active {
            background-color: #333;
        }

        button {
            padding: 6px 12px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: 500;
            font-size: 12px;
        }

        button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="robot-status">
        <div class="status-grid">
            <div class="status-item">
                <div class="status-title">机器人方向</div>
                <div class="direction-indicator">
                    <div class="direction-arrow" id="directionArrow"></div>
                </div>
                <div class="direction-angle" id="directionAngle">0°</div>
            </div>
            <div class="status-item">
                <div class="status-title">颜色传感器</div>
                <div class="color-sensors" id="colorSensors">
                    <div class="color-sensor"></div>
                    <div class="color-sensor"></div>
                    <div class="color-sensor"></div>
                    <div class="color-sensor"></div>
                    <div class="color-sensor"></div>
                </div>
            </div>
            <div class="status-item">
                <div class="status-title">超声波距离</div>
                <div class="ultrasonic-value" id="ultrasonicValue">0</div>
                <div id="ultrasonicNoDetect" style="display:none;"></div>
            </div>
            <div class="status-item">
                <div class="status-title">机器人坐标</div>
                <div class="coordinates" id="robotCoordinates">(50, 50)</div>
            </div>
        </div>
    </div>
    <header class="simulator-header">
        <h2>机器人模拟器</h2>
        <p>使用方向键控制：↑前进，↓后退，←左转，→右转</p>
        <div class="controls">
            <button id="resetSimulation">重置</button>
            <div class="path-selector">
                <button class="path-button" data-path="1">地图1</button>
                <button class="path-button" data-path="2">地图2</button>
                <button class="path-button" data-path="3">地图3</button>
            </div>

            <!-- 机器人控制按钮 -->
            <div style="margin-left: 20px; border-left: 1px solid #ccc; padding-left: 20px;">
                <span style="font-weight: bold; margin-right: 10px;">机器人控制:</span>
                <button onclick="controlRobotForward()" style="background-color: #4CAF50;">前进</button>
                <button onclick="controlRobotBack()" style="background-color: #f44336;">后退</button>
                <button onclick="controlRobotTurnLeft()" style="background-color: #2196F3;">左转</button>
                <button onclick="controlRobotTurnRight()" style="background-color: #FF9800;">右转</button>
            </div>
        </div>
    </header>

    <main class="simulator-content">
        <canvas id="gridCanvas" width="600" height="400"></canvas>
    </main>
</div>

<script>
    // 全局变量
    let vm = null; // Scratch虚拟机实例
    let robotSprite = null; // 机器人角色
    let robotSpriteId = null; // 机器人角色ID

    // 初始化函数，从父窗口获取VM实例
    function initializeRobotControl() {
        try {
            // 从父窗口获取VM实例
            if (window.parent && window.parent.vm) {
                vm = window.parent.vm;
                console.log('成功获取VM实例');

                // 创建或获取机器人角色
                createRobotSprite();
            } else {
                console.warn('无法获取VM实例，将在1秒后重试');
                setTimeout(initializeRobotControl, 1000);
            }
        } catch (error) {
            console.error('初始化机器人控制失败:', error);
            setTimeout(initializeRobotControl, 1000);
        }
    }

    // 创建机器人角色
    function createRobotSprite() {
        if (!vm) return;

        try {
            // 检查是否已存在机器人角色
            const existingRobot = vm.runtime.targets.find(target =>
                target.sprite && target.sprite.name === '机器人'
            );

            if (existingRobot) {
                robotSprite = existingRobot;
                robotSpriteId = existingRobot.id;
                console.log('找到现有机器人角色:', robotSpriteId);

                // 设置机器人初始位置和方向
                setupRobotSprite();
                return;
            }

            // 创建新的机器人角色
            const robotSpriteData = {
                name: '机器人',
                isStage: false,
                x: 0,
                y: 0,
                visible: true,
                size: 100,
                rotationStyle: 'all around',
                direction: 90, // 向上
                draggable: true,
                currentCostume: 0,
                blocks: {},
                variables: {},
                costumes: [{
                    costumeName: '机器人造型',
                    baseLayerID: -1,
                    baseLayerMD5: 'cd21514d0531fdffb22204e0ec5ed84a.svg',
                    bitmapResolution: 1,
                    rotationCenterX: 0,
                    rotationCenterY: 0
                }],
                sounds: [{
                    soundName: '机器人音效',
                    soundID: -1,
                    md5: '83a9787d4cb6f3b7632b4ddfebf74367.wav',
                    sampleCount: 258,
                    rate: 11025,
                    format: ''
                }]
            };

            // 添加角色到VM
            vm.addSprite(JSON.stringify(robotSpriteData)).then(() => {
                // 获取新创建的角色
                robotSprite = vm.runtime.targets.find(target =>
                    target.sprite && target.sprite.name === '机器人'
                );

                if (robotSprite) {
                    robotSpriteId = robotSprite.id;
                    console.log('成功创建机器人角色:', robotSpriteId);
                    setupRobotSprite();
                }
            }).catch(error => {
                console.error('创建机器人角色失败:', error);
            });

        } catch (error) {
            console.error('创建机器人角色时出错:', error);
        }
    }

    // 设置机器人角色
    function setupRobotSprite() {
        if (!robotSprite) return;

        // 设置机器人在画布中心
        robotSprite.setXY(0, 0);
        robotSprite.setDirection(90); // 向上
        robotSprite.setSize(80); // 稍小一些

        // 更新画布中的机器人位置
        updateRobotOnCanvas();

        console.log('机器人角色设置完成');
    }

    // 机器人控制函数
    function robotForward(distance = 20) {
        if (!robotSprite) {
            console.warn('机器人角色未初始化');
            return;
        }

        try {
            // 根据当前方向计算移动
            const direction = robotSprite.direction;
            const radians = direction * Math.PI / 180;

            const dx = distance * Math.sin(radians);
            const dy = distance * Math.cos(radians);

            // 更新角色位置
            robotSprite.setXY(robotSprite.x + dx, robotSprite.y + dy);

            // 更新画布显示
            updateRobotOnCanvas();

            // 触发重绘
            if (vm.runtime) {
                vm.runtime.requestRedraw();
            }

            console.log(`机器人前进 ${distance} 像素，新位置: (${robotSprite.x}, ${robotSprite.y})`);
        } catch (error) {
            console.error('机器人前进失败:', error);
        }
    }

    function robotTurnLeft(angle = 15) {
        if (!robotSprite) {
            console.warn('机器人角色未初始化');
            return;
        }

        try {
            // 更新角色方向
            robotSprite.setDirection(robotSprite.direction - angle);

            // 更新画布显示
            updateRobotOnCanvas();

            // 触发重绘
            if (vm.runtime) {
                vm.runtime.requestRedraw();
            }

            console.log(`机器人左转 ${angle} 度，新方向: ${robotSprite.direction}`);
        } catch (error) {
            console.error('机器人左转失败:', error);
        }
    }

    function robotTurnRight(angle = 15) {
        if (!robotSprite) {
            console.warn('机器人角色未初始化');
            return;
        }

        try {
            // 更新角色方向
            robotSprite.setDirection(robotSprite.direction + angle);

            // 更新画布显示
            updateRobotOnCanvas();

            // 触发重绘
            if (vm.runtime) {
                vm.runtime.requestRedraw();
            }

            console.log(`机器人右转 ${angle} 度，新方向: ${robotSprite.direction}`);
        } catch (error) {
            console.error('机器人右转失败:', error);
        }
    }

    // 更新画布中的机器人显示
    function updateRobotOnCanvas() {
        if (!robotSprite) return;

        // 更新状态显示
        updateRobotStatus();

        // 重绘画布上的机器人位置
        drawRobot();
    }

    // 更新机器人状态显示
    function updateRobotStatus() {
        if (!robotSprite) return;

        // 更新方向显示
        const directionElement = document.querySelector('.direction-arrow');
        if (directionElement) {
            const rotation = robotSprite.direction - 90; // 调整为画布坐标系
            directionElement.style.transform = `rotate(${rotation}deg)`;
        }

        // 更新角度显示
        const angleElement = document.querySelector('.direction-angle');
        if (angleElement) {
            angleElement.textContent = `${Math.round(robotSprite.direction)}°`;
        }

        // 更新坐标显示
        const coordinatesElement = document.querySelector('.coordinates');
        if (coordinatesElement) {
            coordinatesElement.textContent = `(${Math.round(robotSprite.x)}, ${Math.round(robotSprite.y)})`;
        }
    }

    // 页面加载完成后初始化
    window.addEventListener('load', () => {
        console.log('机器人传感器仿真页面加载完成');
        initializeRobotControl();
    });

    // 网格绘制逻辑
    const canvas = document.getElementById('gridCanvas');
    const ctx = canvas.getContext('2d');
    const GRID_SIZE = 100; // 100x100的网格，与robot_thefirst.html一致
    const CELL_SIZE = Math.min(canvas.width, canvas.height) / GRID_SIZE; // 每个格子的大小

    // 机器人在画布中的位置（画布坐标系）
    let robotCanvasX = canvas.width / 2;
    let robotCanvasY = canvas.height / 2;
    let robotCanvasDirection = 0; // 画布坐标系中的方向（0度为向右）

    // 机器人参数
    const ROBOT = {
        x: 50,
        y: 50,
        size: 5, // 5x5格子，与robot_thefirst.html一致
        wheelSize: 1,
        sensorSize: 0.5,
        color: '#4CAF50',
        wheelColor: '#333333',
        ultrasonicColor: '#2196F3',
        colorSensorColor: '#FFC107',
        moveSpeed: 1,
        isMoving: false,
        moveInterval: null,
        angle: 0, // 机器人朝向角度（弧度）
        rotationSpeed: 5 * Math.PI / 180 // 每次旋转的角度（5度）
    };

    // 添加路径数据
    const PATHS = {
        1: [
            // 路径1：一个简单的矩形
            {x: 20, y: 20}, {x: 20, y: 80}, {x: 80, y: 80}, {x: 80, y: 20}, {x: 20, y: 20}
        ],
        2: [
            // 路径2：一个Z字形
            {x: 20, y: 20}, {x: 80, y: 20}, {x: 20, y: 50}, {x: 80, y: 50}, {x: 20, y: 80}, {x: 80, y: 80}
        ],
        3: [
            // 路径3：一个圆形（近似）
            {x: 50, y: 20}, {x: 70, y: 30}, {x: 80, y: 50}, {x: 70, y: 70},
            {x: 50, y: 80}, {x: 30, y: 70}, {x: 20, y: 50}, {x: 30, y: 30}, {x: 50, y: 20}
        ]
    };

    let currentPath = 1; // 当前选中的路径

    // 修改地图数据结构
    const MAP = {
        data: [], // 二维数组存储地图数据
        width: GRID_SIZE,
        height: GRID_SIZE,

        // 路径数据
        paths: {
            1: [
                {x: 20, y: 20}, {x: 20, y: 80}, {x: 80, y: 80}, {x: 80, y: 20}, {x: 20, y: 20}
            ],
            2: [
                {x: 20, y: 20}, {x: 80, y: 20}, {x: 20, y: 50}, {x: 80, y: 50}, {x: 20, y: 80}, {x: 80, y: 80}
            ],
            3: [
                {x: 50, y: 20}, {x: 70, y: 30}, {x: 80, y: 50}, {x: 70, y: 70},
                {x: 50, y: 80}, {x: 30, y: 70}, {x: 20, y: 50}, {x: 30, y: 30}, {x: 50, y: 20}
            ]
        },

        // 初始化地图
        init: function() {
            this.data = Array(this.height).fill().map(() => Array(this.width).fill(0));
            // 设置边界为障碍物
            for (let x = 0; x < this.width; x++) {
                this.data[0][x] = -1;
                this.data[this.height - 1][x] = -1;
            }
            for (let y = 0; y < this.height; y++) {
                this.data[y][0] = -1;
                this.data[y][this.width - 1] = -1;
            }
        },

        // 设置栅格值
        setCell: function(x, y, value) {
            if (x >= 0 && x < this.width && y >= 0 && y < this.height) {
                this.data[y][x] = value;
            }
        },

        // 获取栅格值
        getCell: function(x, y) {
            if (x >= 0 && x < this.width && y >= 0 && y < this.height) {
                return this.data[y][x];
            }
            return -1; // 超出边界返回-1（障碍物）
        },

        // 从路径生成地图数据
        generateFromPath: function(pathId) {
            this.init(); // 重置地图并设置边界障碍
            const path = this.paths[pathId];
            if (!path) return;

            // 将路径转换为地图数据（路径为1，不是障碍物）
            for (let i = 0; i < path.length - 1; i++) {
                const start = path[i];
                const end = path[i + 1];
                let x1 = Math.floor(start.x);
                let y1 = Math.floor(start.y);
                const x2 = Math.floor(end.x);
                const y2 = Math.floor(end.y);
                const dx = Math.abs(x2 - x1);
                const dy = Math.abs(y2 - y1);
                const sx = x1 < x2 ? 1 : -1;
                const sy = y1 < y2 ? 1 : -1;
                let err = dx - dy;
                while (true) {
                    // 只设置为路径，不覆盖障碍物
                    if (this.data[y1][x1] !== -1) this.setCell(x1, y1, 1);
                    if (x1 === x2 && y1 === y2) break;
                    const e2 = 2 * err;
                    if (e2 > -dy) { err -= dy; x1 += sx; }
                    if (e2 < dx) { err += dx; y1 += sy; }
                }
            }
            // 添加彩色线条
            this.addColorfulLines();
            // 随机放置障碍物
            this.placeRandomObstacles();
        },

        // 添加彩色线条（红色、蓝色、绿色、黄色）
        addColorfulLines: function() {
            // 添加红色线条
            for (let x = 10; x < 20; x++) {
                this.setCell(x, 10, 2); // 红色线条在(10-19, 10)
            }
            for (let x = 30; x < 40; x++) {
                this.setCell(x, 70, 2); // 红色线条在(30-39, 70)
            }

            // 添加蓝色线条
            for (let y = 20; y < 30; y++) {
                this.setCell(20, y, 3); // 蓝色线条在(20, 20-29)
            }
            for (let y = 60; y < 70; y++) {
                this.setCell(80, y, 3); // 蓝色线条在(80, 60-69)
            }

            // 添加绿色线条
            for (let y = 15; y < 25; y++) {
                this.setCell(40, y, 4); // 绿色线条在(40, 15-24)
            }
            for (let y = 45; y < 55; y++) {
                this.setCell(60, y, 4); // 绿色线条在(60, 45-54)
            }

            // 添加黄色线条
            for (let x = 50; x < 60; x++) {
                this.setCell(x, 30, 5); // 黄色线条在(50-59, 30)
            }
            for (let y = 35; y < 45; y++) {
                this.setCell(70, y, 5); // 黄色线条在(70, 35-44)
            }
            for (let x = 25; x < 35; x++) {
                this.setCell(x, 50, 5); // 黄色线条在(25-34, 50)
            }
        },

        // 随机放置障碍物（2x2、3x3、4x4）
        placeRandomObstacles: function() {
            const sizes = [2, 3, 4];
            const count = 5; // 随机放5个障碍物
            let placed = 0;
            while (placed < count) {
                const size = sizes[Math.floor(Math.random() * sizes.length)];
                const x = Math.floor(Math.random() * (this.width - size - 2)) + 1;
                const y = Math.floor(Math.random() * (this.height - size - 2)) + 1;
                // 检查区域是否空闲
                let canPlace = true;
                for (let dy = 0; dy < size; dy++) {
                    for (let dx = 0; dx < size; dx++) {
                        if (this.data[y + dy][x + dx] !== 0) canPlace = false;
                    }
                }
                if (canPlace) {
                    for (let dy = 0; dy < size; dy++) {
                        for (let dx = 0; dx < size; dx++) {
                            this.setCell(x + dx, y + dy, -1);
                        }
                    }
                    placed++;
                }
            }
        }
    };

    function drawRobot() {
        const centerPixelX = (ROBOT.x + ROBOT.size / 2) * CELL_SIZE;
        const centerPixelY = (ROBOT.y + ROBOT.size / 2) * CELL_SIZE;
        const robotSize = ROBOT.size * CELL_SIZE;
        const wheelLength = 3 * CELL_SIZE; // 车轮长度3格
        const wheelWidth = 1 * CELL_SIZE;  // 车轮宽度1格
        const sensorSize = ROBOT.sensorSize * CELL_SIZE;

        ctx.save();
        ctx.translate(centerPixelX, centerPixelY); // 机器人中心
        ctx.rotate(ROBOT.angle); // 旋转

        // 绘制机器人身体（以中心为原点）
        ctx.fillStyle = ROBOT.color;
        ctx.fillRect(-robotSize / 2, -robotSize / 2, robotSize, robotSize);

        // 绘制左轮（深灰色，3格长，1格宽）
        ctx.fillStyle = '#444444';
        ctx.fillRect(-robotSize / 2 - wheelWidth, -wheelLength / 2, wheelWidth, wheelLength);

        // 绘制右轮（深灰色，3格长，1格宽）
        ctx.fillRect(robotSize / 2, -wheelLength / 2, wheelWidth, wheelLength);

        // 修正：绘制颜色传感器（头部外侧半格的5个格子中心）
        ctx.fillStyle = ROBOT.colorSensorColor;
        for (let i = 0; i < 5; i++) {
            const localX = (i - 2) * CELL_SIZE;
            const localY = -(ROBOT.size / 2 + 0.5) * CELL_SIZE;
            ctx.beginPath();
            ctx.arc(localX, localY, sensorSize, 0, Math.PI * 2);
            ctx.fill();
        }

        // 绘制超声波传感器（头部中心，在颜色传感器上方）
        ctx.fillStyle = ROBOT.ultrasonicColor;
        ctx.beginPath();
        ctx.arc(0, -(ROBOT.size / 2 + 1) * CELL_SIZE, sensorSize, 0, Math.PI * 2);
        ctx.fill();

        ctx.restore();
    }

    // 绘制超声波探测扇形范围
    function drawUltrasonicRay() {
        const robotCenterX = ROBOT.x + ROBOT.size / 2;
        const robotCenterY = ROBOT.y + ROBOT.size / 2;
        const localX = 0;
        const localY = -(ROBOT.size / 2 + 1);
        const sensorX = robotCenterX + localX * Math.cos(ROBOT.angle) - localY * Math.sin(ROBOT.angle);
        const sensorY = robotCenterY + localX * Math.sin(ROBOT.angle) + localY * Math.cos(ROBOT.angle);
        const maxDistance = 20;
        // 35度扇形，中心线与ROBOT.angle一致
        const angle = ROBOT.angle;
        const halfFov = (35 / 2) * Math.PI / 180; // 17.5度
        ctx.save();
        ctx.beginPath();
        ctx.moveTo(sensorX * CELL_SIZE, sensorY * CELL_SIZE);
        // 画扇形
        for (let a = -halfFov; a <= halfFov; a += (halfFov * 2) / 60) {
            const rayAngle = angle + a;
            const endX = sensorX + maxDistance * Math.sin(rayAngle);
            const endY = sensorY - maxDistance * Math.cos(rayAngle);
            ctx.lineTo(endX * CELL_SIZE, endY * CELL_SIZE);
        }
        ctx.closePath();
        ctx.fillStyle = 'rgba(180,180,180,0.15)';
        ctx.fill();
        ctx.restore();
    }

    // 修改drawGrid，在绘制机器人前绘制超声波射线
    function drawGrid() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.strokeStyle = '#e0e0e0';
        ctx.lineWidth = 0.5;
        for (let x = 0; x <= GRID_SIZE; x++) {
            ctx.beginPath();
            ctx.moveTo(x * CELL_SIZE, 0);
            ctx.lineTo(x * CELL_SIZE, canvas.height);
            ctx.stroke();
        }
        for (let y = 0; y <= GRID_SIZE; y++) {
            ctx.beginPath();
            ctx.moveTo(0, y * CELL_SIZE);
            ctx.lineTo(canvas.width, y * CELL_SIZE);
            ctx.stroke();
        }
        for (let y = 0; y < MAP.height; y++) {
            for (let x = 0; x < MAP.width; x++) {
                const value = MAP.getCell(x, y);
                if (value === 1) {
                    ctx.fillStyle = '#000000'; // 黑色线条
                    ctx.fillRect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
                } else if (value === -1) {
                    ctx.fillStyle = '#FF2222'; // 红色正方形障碍物
                    ctx.fillRect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
                } else if (value === 2) {
                    ctx.fillStyle = '#FF4500'; // 红色线条
                    ctx.fillRect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
                } else if (value === 3) {
                    ctx.fillStyle = '#0000FF'; // 蓝色线条
                    ctx.fillRect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
                } else if (value === 4) {
                    ctx.fillStyle = '#008000'; // 绿色线条
                    ctx.fillRect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
                } else if (value === 5) {
                    ctx.fillStyle = '#FFD700'; // 黄色线条
                    ctx.fillRect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
                }
            }
        }
        drawUltrasonicRay();
        drawRobot();
        checkSensors();
    }

    // 修改传感器检测函数
    function checkIfOnBlackLine(x, y) {
        return MAP.getCell(x, y) === 1;
    }

    // 检查是否在彩色线条上
    function checkIfOnColor(x, y) {
        const value = MAP.getCell(x, y);
        if (value === 2) return 'red';
        if (value === 3) return 'blue';
        if (value === 4) return 'green';
        if (value === 5) return 'yellow';
        return null;
    }

    // 更新机器人坐标显示
    function updateRobotCoordinates() {
        const x = Math.floor(ROBOT.x);
        const y = Math.floor(ROBOT.y);
        document.getElementById('robotCoordinates').textContent = `(${x}, ${y})`;
    }

    // 将机器人放置在地图中心的空位
    function placeRobotAtCenter() {
        const centerX = Math.floor(GRID_SIZE / 2);
        const centerY = Math.floor(GRID_SIZE / 2);
        let found = false;
        let tryRadius = 0;

        // 从中心开始向外搜索空位
        while (!found && tryRadius < GRID_SIZE / 2) {
            for (let dx = -tryRadius; dx <= tryRadius; dx++) {
                for (let dy = -tryRadius; dy <= tryRadius; dy++) {
                    let ok = true;
                    for (let rx = 0; rx < ROBOT.size; rx++) {
                        for (let ry = 0; ry < ROBOT.size; ry++) {
                            const tx = centerX + dx + rx - Math.floor(ROBOT.size / 2);
                            const ty = centerY + dy + ry - Math.floor(ROBOT.size / 2);
                            if (MAP.getCell(tx, ty) === -1) ok = false;
                        }
                    }
                    if (ok) {
                        ROBOT.x = centerX + dx - Math.floor(ROBOT.size / 2);
                        ROBOT.y = centerY + dy - Math.floor(ROBOT.size / 2);
                        found = true;
                        break;
                    }
                }
                if (found) break;
            }
            tryRadius++;
        }
        if (!found) {
            // 实在找不到就放左上角
            ROBOT.x = ROBOT.size;
            ROBOT.y = ROBOT.size;
        }
        ROBOT.angle = 0;
        updateRobotCoordinates();
    }

    // 修改传感器检测函数，添加坐标和方向角更新
    function checkSensors() {
        // 检查颜色传感器
        const sensors = document.querySelectorAll('.color-sensor');
        const robotCenterX = ROBOT.x + ROBOT.size / 2;
        const robotCenterY = ROBOT.y + ROBOT.size / 2;
        // 修正：计算每个传感器的位置（头部外侧半格的5个格子中心）
        for (let i = 0; i < 5; i++) {
            const localX = (i - 2);
            const localY = -(ROBOT.size / 2 + 0.5);
            // 旋转
            const rotatedX = localX * Math.cos(ROBOT.angle) - localY * Math.sin(ROBOT.angle);
            const rotatedY = localX * Math.sin(ROBOT.angle) + localY * Math.cos(ROBOT.angle);
            const sensorX = robotCenterX + rotatedX;
            const sensorY = robotCenterY + rotatedY;
            const gridX = Math.round(sensorX);
            const gridY = Math.round(sensorY);

            // 检查是否在黑色线条上
            const isOnBlackLine = checkIfOnBlackLine(gridX, gridY);

            // 检查是否在彩色线条上
            const color = checkIfOnColor(gridX, gridY);

            sensors[i].classList.remove('black', 'red', 'blue', 'green', 'yellow');
            if (isOnBlackLine) {
                sensors[i].classList.add('black');
            } else if (color) {
                sensors[i].classList.add(color);
            }
        }

        // 更新方向指示器
        const arrow = document.getElementById('directionArrow');
        arrow.style.transform = `rotate(${ROBOT.angle}rad)`;
        // 更新方向角度数值
        const angleInDegrees = (ROBOT.angle * 180 / Math.PI) % 360; // 转换为度，并规范化到0-360
        const displayAngle = angleInDegrees < 0 ? angleInDegrees + 360 : angleInDegrees;
        document.getElementById('directionAngle').textContent = `${Math.round(displayAngle)}°`;

        // 计算超声波距离
        const ultrasonicDistance = calculateUltrasonicDistance();
        const ultrasonicValueDiv = document.getElementById('ultrasonicValue');
        const noDetectLine = document.getElementById('ultrasonicNoDetect');
        if (ultrasonicDistance >= 20) {
            ultrasonicValueDiv.style.display = 'none';
            noDetectLine.style.display = 'block';
        } else {
            ultrasonicValueDiv.style.display = 'block';
            ultrasonicValueDiv.textContent = ultrasonicDistance;
            noDetectLine.style.display = 'none';
        }

        // 更新机器人坐标显示
        updateRobotCoordinates();
    }

    // 修改超声波检测，在35度扇形范围内探测最近的红色正方形障碍物
    function calculateUltrasonicDistance() {
        const robotCenterX = ROBOT.x + ROBOT.size / 2;
        const robotCenterY = ROBOT.y + ROBOT.size / 2;
        const localX = 0;
        const localY = -(ROBOT.size / 2 + 1);
        const sensorX = robotCenterX + localX * Math.cos(ROBOT.angle) - localY * Math.sin(ROBOT.angle);
        const sensorY = robotCenterY + localX * Math.sin(ROBOT.angle) + localY * Math.cos(ROBOT.angle);
        const maxDistance = 20;
        const step = 0.2; // 更小的步长，提高精度
        const angle = ROBOT.angle;
        const halfFov = (35 / 2) * Math.PI / 180; // 17.5度

        let minDistance = maxDistance; // 初始化最小距离为最大探测距离

        // 在扇形范围内发射多条射线进行探测
        const numRays = 60; // 在扇形内发射的射线数量
        for (let i = 0; i <= numRays; i++) {
            const rayAngle = angle - halfFov + (halfFov * 2 * i) / numRays; // 当前射线的角度

            // 沿着当前射线探测
            for (let d = step; d <= maxDistance; d += step) {
                const checkX = sensorX + d * Math.sin(rayAngle);
                const checkY = sensorY - d * Math.cos(rayAngle);
                const gridX = Math.round(checkX);
                const gridY = Math.round(checkY);

                // 只检查红色正方形障碍物(-1)
                if (gridX >= 0 && gridX < GRID_SIZE && gridY >= 0 && gridY < GRID_SIZE && MAP.getCell(gridX, gridY) === -1) {
                    // 计算传感器位置到障碍物格子边缘的距离
                    const obstacleCenterX = gridX + 0.5;
                    const obstacleCenterY = gridY + 0.5;
                    const distToObstacle = Math.sqrt(Math.pow(sensorX - obstacleCenterX, 2) + Math.pow(sensorY - obstacleCenterY, 2));

                    // 减去半个格子的距离，得到到障碍物边缘的距离
                    const distToEdge = Math.max(0, distToObstacle - 0.5);
                    minDistance = Math.min(minDistance, distToEdge);
                    break; // 当前射线遇到障碍物，停止沿着这条射线继续探测
                }
            }
        }

        // 如果距离小于0.5，认为是0（即将撞到）
        const finalDistance = minDistance < 0.5 ? 0 : Math.round(minDistance);
        return finalDistance;
    }

    // 修改移动控制函数，禁止进入障碍物
    function moveRobot(direction) {
        if (ROBOT.isMoving) return;
        let newX = ROBOT.x;
        let newY = ROBOT.y;
        switch(direction) {
        case 'ArrowUp':
            // 朝头部方向前进
            newX += Math.sin(ROBOT.angle) * ROBOT.moveSpeed;
            newY -= Math.cos(ROBOT.angle) * ROBOT.moveSpeed;
            break;
        case 'ArrowDown':
            // 朝尾部方向后退
            newX -= Math.sin(ROBOT.angle) * ROBOT.moveSpeed;
            newY += Math.cos(ROBOT.angle) * ROBOT.moveSpeed;
            break;
        case 'ArrowLeft':
            ROBOT.angle -= ROBOT.rotationSpeed;
            break;
        case 'ArrowRight':
            ROBOT.angle += ROBOT.rotationSpeed;
            break;
        }
        if (direction === 'ArrowUp' || direction === 'ArrowDown') {
            let canMove = true;

            if (direction === 'ArrowUp') {
                // 前进时，只有当超声波距离为0时才限制
                const currentUltrasonicDistance = calculateUltrasonicDistance();
                if (currentUltrasonicDistance === 0) {
                    canMove = false;
                    console.log('前进被阻止：超声波距离为0，即将撞到障碍物');
                }
            }
            else if (direction === 'ArrowDown') {
                // 后退时，只检查新位置是否直接与红色正方形障碍物重叠
                const robotCenterX = newX + ROBOT.size / 2;
                const robotCenterY = newY + ROBOT.size / 2;

                // 检查机器人身体的四个角落是否会与红色障碍物重叠
                const corners = [
                    {x: newX, y: newY},
                    {x: newX + ROBOT.size - 1, y: newY},
                    {x: newX, y: newY + ROBOT.size - 1},
                    {x: newX + ROBOT.size - 1, y: newY + ROBOT.size - 1}
                ];

                for (let corner of corners) {
                    const gridX = Math.floor(corner.x);
                    const gridY = Math.floor(corner.y);

                    // 只有红色正方形障碍物(-1)才阻止运动
                    if (MAP.getCell(gridX, gridY) === -1) {
                        canMove = false;
                        console.log('后退被阻止：会撞到红色障碍物在位置', gridX, gridY);
                        break;
                    }
                }
            }

            // 应用新的位置
            if (canMove) {
                ROBOT.x = newX;
                ROBOT.y = newY;
                updateRobotCoordinates();
                console.log('机器人移动到新位置:', Math.round(ROBOT.x), Math.round(ROBOT.y));
            } else {
                console.log('机器人移动被阻止');
            }
        }
        drawGrid();
    }

    // 键盘事件处理
    let keyState = {}; // 记录按键状态
    let moveIntervals = {};

    function startMove(key) {
        if (moveIntervals[key]) return;
        moveIntervals[key] = setInterval(() => {
            moveRobot(key);
        }, 60); // 每60ms移动一次
    }

    function stopMove(key) {
        if (moveIntervals[key]) {
            clearInterval(moveIntervals[key]);
            moveIntervals[key] = null;
        }
    }

    window.addEventListener('keydown', (event) => {
        if (keyState[event.key]) return; // 如果按键已经在按下状态，则忽略
        keyState[event.key] = true;
        canvas.focus();
        switch(event.key) {
        case 'ArrowUp':
        case 'ArrowDown':
        case 'ArrowLeft':
        case 'ArrowRight':
            event.preventDefault();
            startMove(event.key);
            break;
        }
    });

    window.addEventListener('keyup', (event) => {
        keyState[event.key] = false;
        stopMove(event.key);
    });

    // 修改canvas点击事件，允许点击边界位置
    canvas.addEventListener('click', (event) => {
        canvas.focus();
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // 修改边界限制，允许点击边界位置
        ROBOT.x = Math.max(0, Math.min(GRID_SIZE - 1, Math.floor(x / CELL_SIZE)));
        ROBOT.y = Math.max(0, Math.min(GRID_SIZE - 1, Math.floor(y / CELL_SIZE)));

        updateRobotCoordinates();
        drawGrid();
    });

    // 放置机器人到地图中央且避开障碍物
    function placeRobotAtCenter() {
        const centerX = Math.floor(GRID_SIZE / 2);
        const centerY = Math.floor(GRID_SIZE / 2);
        // 检查中心区域是否有障碍物
        let found = false;
        let tryRadius = 0;
        while (!found && tryRadius < GRID_SIZE / 2) {
            for (let dx = -tryRadius; dx <= tryRadius; dx++) {
                for (let dy = -tryRadius; dy <= tryRadius; dy++) {
                    let ok = true;
                    for (let rx = 0; rx < ROBOT.size; rx++) {
                        for (let ry = 0; ry < ROBOT.size; ry++) {
                            const tx = centerX + dx + rx - Math.floor(ROBOT.size / 2);
                            const ty = centerY + dy + ry - Math.floor(ROBOT.size / 2);
                            // 只有红色正方形障碍物(-1)才阻止机器人放置，彩色线条不阻止
                            if (MAP.getCell(tx, ty) === -1) ok = false;
                        }
                    }
                    if (ok) {
                        ROBOT.x = centerX + dx;
                        ROBOT.y = centerY + dy;
                        found = true;
                        break;
                    }
                }
                if (found) break;
            }
            tryRadius++;
        }
        if (!found) {
            // 实在找不到就放左上角
            ROBOT.x = ROBOT.size;
            ROBOT.y = ROBOT.size;
        }
        ROBOT.angle = 0;
        updateRobotCoordinates();
    }

    // 修改路径选择按钮事件，切换路径时重新生成障碍物和路径，并放置机器人
    document.querySelectorAll('.path-button').forEach(button => {
        button.addEventListener('click', () => {
            document.querySelectorAll('.path-button').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            const pathId = parseInt(button.dataset.path);
            currentPath = pathId; // 更新当前路径
            MAP.generateFromPath(pathId); // 重新生成路径和障碍物
            placeRobotAtCenter(); // 放置机器人
            drawGrid();
        });
    });

    // 页面加载后自动开始模拟，生成初始路径和障碍物，放置机器人
    window.addEventListener('DOMContentLoaded', () => {
        MAP.init();
        MAP.generateFromPath(1);
        placeRobotAtCenter();
        drawGrid();
        document.querySelector('.path-button[data-path="1"]').classList.add('active');
    });

    // 修改重置按钮的处理函数，重置时也放置机器人到中心空位
    document.getElementById('resetSimulation').addEventListener('click', () => {
        placeRobotAtCenter();
        drawGrid();
    });

    // 机器人控制按钮函数
    function controlRobotForward() {
        console.log('控制机器人前进');

        // 同时控制Scratch角色和画布机器人
        if (robotSprite) {
            robotForward(20); // 控制Scratch角色
        }

        // 控制画布机器人
        moveRobot('ArrowUp');
    }

    function controlRobotBack() {
        console.log('控制机器人后退');

        // 同时控制Scratch角色和画布机器人
        if (robotSprite) {
            robotForward(-20); // 控制Scratch角色后退（负距离）
        }

        // 控制画布机器人
        moveRobot('ArrowDown');
    }

    function controlRobotTurnLeft() {
        console.log('控制机器人左转');

        // 同时控制Scratch角色和画布机器人
        if (robotSprite) {
            robotTurnLeft(15); // 控制Scratch角色
        }

        // 控制画布机器人
        moveRobot('ArrowLeft');
    }

    function controlRobotTurnRight() {
        console.log('控制机器人右转');

        // 同时控制Scratch角色和画布机器人
        if (robotSprite) {
            robotTurnRight(15); // 控制Scratch角色
        }

        // 控制画布机器人
        moveRobot('ArrowRight');
    }

    // 同步画布机器人位置到Scratch角色
    function syncCanvasToSprite() {
        if (!robotSprite) return;

        // 将画布坐标转换为Scratch坐标
        const scratchX = (ROBOT.x - GRID_SIZE/2) * 10; // 缩放因子
        const scratchY = (GRID_SIZE/2 - ROBOT.y) * 10; // Y轴翻转

        robotSprite.setXY(scratchX, scratchY);

        // 同步方向（画布弧度转Scratch度数）
        const scratchDirection = (ROBOT.angle * 180 / Math.PI + 90) % 360;
        robotSprite.setDirection(scratchDirection);

        // 触发重绘
        if (vm && vm.runtime) {
            vm.runtime.requestRedraw();
        }
    }

    // 同步Scratch角色位置到画布机器人
    function syncSpriteToCanvas() {
        if (!robotSprite) return;

        // 将Scratch坐标转换为画布坐标
        const canvasX = robotSprite.x / 10 + GRID_SIZE/2;
        const canvasY = GRID_SIZE/2 - robotSprite.y / 10;

        ROBOT.x = canvasX - ROBOT.size/2;
        ROBOT.y = canvasY - ROBOT.size/2;

        // 同步方向（Scratch度数转画布弧度）
        ROBOT.angle = (robotSprite.direction - 90) * Math.PI / 180;

        // 重绘画布
        drawGrid();
    }

    // 重写drawRobot函数以同步位置
    const originalDrawRobot = drawRobot;
    drawRobot = function() {
        originalDrawRobot();
        // 每次绘制后同步到Scratch角色
        syncCanvasToSprite();
    };

    // 暴露控制函数到全局作用域，以便从父窗口调用
    window.robotControl = {
        forward: controlRobotForward,
        back: controlRobotBack,
        turnLeft: controlRobotTurnLeft,
        turnRight: controlRobotTurnRight,
        syncToSprite: syncCanvasToSprite,
        syncToCanvas: syncSpriteToCanvas
    };
</script>
</body>
</html>
