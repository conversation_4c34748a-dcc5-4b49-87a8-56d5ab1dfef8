import {connect} from 'react-redux';
import React from 'react';
import PropTypes from 'prop-types';

import {
    handleLogout
} from '../reducers/user';
import {
    openLoginModal,
    openUserInfoModal,
    openProjectListModal
} from '../reducers/modals';

import UserMenuComponent from '../components/menu-bar/user-menu.jsx';

class UserMenu extends React.Component {
    constructor (props) {
        super(props);
        this.state = {
            isOpen: false
        };
        this.handleClick = this.handleClick.bind(this);
        this.handleLogout = this.handleLogout.bind(this);
        this.handleClickOutside = this.handleClickOutside.bind(this);
        this.handleClickUserInfo = this.handleClickUserInfo.bind(this);
        this.handleClickMyProjects = this.handleClickMyProjects.bind(this);
    }

    componentDidMount () {
        document.addEventListener('mousedown', this.handleClickOutside);
    }

    componentWillUnmount () {
        document.removeEventListener('mousedown', this.handleClickOutside);
    }

    handleClick () {
        if (this.props.isLoggedIn) {
            this.setState(prevState => ({
                isOpen: !prevState.isOpen
            }));
        } else {
            this.props.onOpenLoginModal();
        }
    }

    handleLogout () {
        this.props.onLogout();
        this.setState({isOpen: false});
    }

    handleClickUserInfo () {
        this.props.onOpenUserInfoModal();
        this.setState({isOpen: false});
    }

    handleClickMyProjects () {
        this.props.onOpenProjectListModal();
        this.setState({isOpen: false});
    }

    handleClickOutside (e) {
        if (this.menuRef && !this.menuRef.contains(e.target)) {
            this.setState({isOpen: false});
        }
    }

    render () {
        const {
            isLoggedIn,
            isRtl,
            menuBarMenuClassName,
            username
        } = this.props;

        return (
            <div ref={div => (this.menuRef = div)}>
                <UserMenuComponent
                    isLoggedIn={isLoggedIn}
                    isOpen={this.state.isOpen}
                    isRtl={isRtl}
                    menuBarMenuClassName={menuBarMenuClassName}
                    onClick={this.handleClick}
                    onLogout={this.handleLogout}
                    onClickUserInfo={this.handleClickUserInfo}
                    onClickMyProjects={this.handleClickMyProjects}
                    username={username}
                />
            </div>
        );
    }
}

UserMenu.propTypes = {
    isLoggedIn: PropTypes.bool,
    isRtl: PropTypes.bool,
    menuBarMenuClassName: PropTypes.string,
    onLogout: PropTypes.func.isRequired,
    onOpenLoginModal: PropTypes.func.isRequired,
    onOpenUserInfoModal: PropTypes.func.isRequired,
    onOpenProjectListModal: PropTypes.func.isRequired,
    username: PropTypes.string
};

const mapStateToProps = state => ({
    isLoggedIn: state.user.isLoggedIn,
    username: state.user.username
});

const mapDispatchToProps = dispatch => ({
    onLogout: () => dispatch(handleLogout()),
    onOpenLoginModal: () => dispatch(openLoginModal()),
    onOpenUserInfoModal: () => dispatch(openUserInfoModal()),
    onOpenProjectListModal: () => dispatch(openProjectListModal())
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(UserMenu);
