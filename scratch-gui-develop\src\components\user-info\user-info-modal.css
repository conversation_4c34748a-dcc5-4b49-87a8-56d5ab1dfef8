@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modalContent {
    width: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(76, 151, 255, 0.2);
    animation: fadeIn 0.3s ease-out;
    max-height: 90vh; /* 进一步增加最大高度为视口高度的90% */
    display: flex;
    flex-direction: column;
    margin: 5vh auto !important; /* 减小上下边距，使模态框更靠近顶部 */
}

.container {
    padding: 1.5rem;
    background-color: #fff;
    border-radius: 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出 */
}

.header {
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.header::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #4C97FF, #3373CC);
    border-radius: 3px;
}

.header h2 {
    margin: 0;
    color: #4C97FF;
    font-size: 1.5rem;
    font-weight: 600;
}

.body {
    padding: 0 1rem;
    flex: 1;
    overflow-y: auto; /* 添加垂直滚动条 */
    max-height: calc(90vh - 5rem); /* 增加最大高度 */
    padding-bottom: 2rem; /* 确保底部有足够的空间 */
}

.formGroup {
    margin-bottom: 1rem; /* 减少底部间距 */
    position: relative;
}

.label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #575e75;
    font-size: 0.9rem;
}

.input {
    width: 100%;
    padding: 0.6rem; /* 减小内边距 */
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    height: 36px; /* 固定高度 */
    box-sizing: border-box; /* 确保内边距不会增加元素总高度 */
}

.input:focus {
    border-color: #4C97FF;
    box-shadow: 0 0 0 3px rgba(76, 151, 255, 0.2);
    outline: none;
}

.input:disabled {
    background-color: #f9f9f9;
    cursor: not-allowed;
    border-color: #e0e0e0;
}

.buttonGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 1.5rem; /* 减少顶部间距 */
    margin-bottom: 1.5rem; /* 增加底部间距 */
    padding-bottom: 0.5rem; /* 添加底部内边距 */
    flex-wrap: wrap; /* 允许按钮在小屏幕上换行 */
    gap: 0.75rem; /* 增加按钮之间的间距 */
    position: relative; /* 添加相对定位 */
    z-index: 10; /* 确保按钮在最上层 */
}

.button {
    border-radius: 6px;
    padding: 0.5rem 1rem; /* 减小内边距 */
    font-weight: 600;
    font-size: 0.9rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 90px; /* 稍微减小按钮宽度 */
    height: 36px; /* 固定按钮高度 */
    overflow: visible; /* 确保内容不被裁剪 */
}

.button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.buttonIcon {
    margin-right: 6px;
    font-size: 1rem;
}

.editButton {
    background-color: #4C97FF;
    color: white;
}

.editButton:hover {
    background-color: #3373CC;
}

.submitButton {
    background-color: #4C97FF;
    color: white;
}

.submitButton:hover {
    background-color: #3373CC;
}

.cancelButton {
    background-color: #f0f0f0;
    color: #575e75;
}

.cancelButton:hover {
    background-color: #e0e0e0;
}

/* 密码部分样式 */
.passwordSection {
    margin-top: 1.5rem; /* 减少顶部间距 */
    padding-top: 1.5rem;
    border-top: 1px solid #e0e0e0;
    padding-bottom: 2rem; /* 增加底部间距 */
    margin-bottom: 1rem; /* 添加底部外边距 */
}

.passwordHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    min-height: 2.5rem; /* 确保高度足够 */
}

.passwordHeader h3 {
    margin: 0;
    color: #4C97FF;
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.5; /* 改善文字显示 */
}

.passwordButton {
    background-color: #4C97FF;
    color: white;
    white-space: nowrap; /* 防止文本换行 */
    min-width: 100px; /* 确保按钮有足够的宽度 */
}

.passwordButton:hover {
    background-color: #3373CC;
}

.errorMessage {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    border-left: 4px solid #d32f2f;
}

/* 自定义滚动条样式 */
.body::-webkit-scrollbar {
    width: 8px;
}

.body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.body::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 4px;
}

.body::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0;
}

/* Firefox滚动条样式 */
.body {
    scrollbar-width: thin;
    scrollbar-color: #c0c0c0 #f1f1f1;
}

/* 确保模态框底部按钮完全显示 */
.ReactModal__Content {
    overflow: visible !important;
}

/* 确保模态框内容可以滚动 */
.ReactModal__Overlay {
    overflow-y: auto;
    padding-bottom: 20px;
}
