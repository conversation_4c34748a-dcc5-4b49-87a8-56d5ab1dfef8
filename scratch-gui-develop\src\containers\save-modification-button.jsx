import {connect} from 'react-redux';
import SaveModificationButton from '../components/save-modification-button/save-modification-button.jsx';
import {showStandardAlert, closeAlertWithId} from '../reducers/alerts';

const mapStateToProps = state => ({
    vm: state.scratchGui.vm,
    projectName: state.scratchGui.projectTitle,
    projectId: state.scratchGui.projectState.projectId
});

const mapDispatchToProps = dispatch => ({
    onSaveSuccess: (response) => {
        // 显示成功消息
        dispatch(showStandardAlert('saveModificationSuccess'));

        // 如果有版本信息，在控制台输出
        if (response && response.versionInfo) {
            console.log(`项目保存成功，版本号: ${response.versionInfo.versionNumber}`);
        }

        // 5秒后自动关闭提示
        setTimeout(() => {
            dispatch(closeAlertWithId('saveModificationSuccess'));
        }, 5000);
    },
    onSaveError: () => {
        // 显示错误消息
        dispatch(showStandardAlert('saveModificationError'));

        // 5秒后自动关闭提示
        setTimeout(() => {
            dispatch(closeAlertWithId('saveModificationError'));
        }, 5000);
    }
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(SaveModificationButton);
