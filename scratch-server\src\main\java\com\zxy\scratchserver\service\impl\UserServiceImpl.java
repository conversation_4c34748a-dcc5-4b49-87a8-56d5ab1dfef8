package com.zxy.scratchserver.service.impl;

import com.zxy.scratchserver.dto.AuthResponse;
import com.zxy.scratchserver.dto.LoginRequest;
import com.zxy.scratchserver.dto.RegisterRequest;
import com.zxy.scratchserver.dto.UpdatePasswordRequest;
import com.zxy.scratchserver.dto.UpdateUserRequest;
import com.zxy.scratchserver.dto.UserResponse;
import com.zxy.scratchserver.model.User;
import com.zxy.scratchserver.repository.UserRepository;
import com.zxy.scratchserver.security.JwtUtils;
import com.zxy.scratchserver.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public AuthResponse login(LoginRequest loginRequest) {
        try {
            // 验证用户名和密码
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword()));

            // 设置认证信息
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 生成JWT令牌
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            String jwt = jwtUtils.generateToken(userDetails);

            // 获取用户信息
            User user = userRepository.findByUsername(userDetails.getUsername())
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 构建响应
            UserResponse userResponse = UserResponse.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .createdAt(user.getCreatedAt())
                    .build();

            return AuthResponse.builder()
                    .token(jwt)
                    .user(userResponse)
                    .build();
        } catch (Exception e) {
            // 记录详细错误信息
            System.err.println("登录失败: " + e.getMessage());
            e.printStackTrace();
            throw e; // 重新抛出异常，让全局异常处理器处理
        }
    }

    @Override
    public AuthResponse register(RegisterRequest registerRequest) {
        try {
            // 检查用户名是否已存在
            if (userRepository.existsByUsername(registerRequest.getUsername())) {
                throw new RuntimeException("用户名已存在");
            }

            // 创建新用户
            User user = new User();
            user.setUsername(registerRequest.getUsername());
            user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));

            // 保存用户
            User savedUser = userRepository.save(user);

            // 生成JWT令牌
            UserDetails userDetails = org.springframework.security.core.userdetails.User
                    .withUsername(savedUser.getUsername())
                    .password(savedUser.getPassword())
                    .authorities(new String[]{})
                    .build();

            String jwt = jwtUtils.generateToken(userDetails);

            // 构建响应
            UserResponse userResponse = UserResponse.builder()
                    .id(savedUser.getId())
                    .username(savedUser.getUsername())
                    .createdAt(savedUser.getCreatedAt())
                    .build();

            return AuthResponse.builder()
                    .token(jwt)
                    .user(userResponse)
                    .build();
        } catch (Exception e) {
            // 记录详细错误信息
            System.err.println("注册失败: " + e.getMessage());
            e.printStackTrace();
            throw e; // 重新抛出异常，让全局异常处理器处理
        }
    }

    @Override
    public UserResponse getCurrentUser(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        return UserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .createdAt(user.getCreatedAt())
                .email(user.getEmail())
                .phoneNumber(user.getPhoneNumber())
                .build();
    }

    @Override
    public UserResponse updateUser(String username, UpdateUserRequest updateUserRequest) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 如果请求中包含新的用户名，并且与当前用户名不同，则检查新用户名是否已存在
        if (updateUserRequest.getUsername() != null &&
            !updateUserRequest.getUsername().equals(username) &&
            userRepository.existsByUsername(updateUserRequest.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }

        // 更新用户信息
        if (updateUserRequest.getUsername() != null && !updateUserRequest.getUsername().isEmpty()) {
            user.setUsername(updateUserRequest.getUsername());
        }

        if (updateUserRequest.getEmail() != null) {
            user.setEmail(updateUserRequest.getEmail());
        }

        if (updateUserRequest.getPhoneNumber() != null) {
            user.setPhoneNumber(updateUserRequest.getPhoneNumber());
        }

        // 保存更新后的用户信息
        User updatedUser = userRepository.save(user);

        // 返回更新后的用户信息
        return UserResponse.builder()
                .id(updatedUser.getId())
                .username(updatedUser.getUsername())
                .createdAt(updatedUser.getCreatedAt())
                .email(updatedUser.getEmail())
                .phoneNumber(updatedUser.getPhoneNumber())
                .build();
    }

    @Override
    public boolean updatePassword(String username, UpdatePasswordRequest updatePasswordRequest) {
        try {
            // 获取用户信息
            User user = userRepository.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            // 验证旧密码是否正确
            if (!passwordEncoder.matches(updatePasswordRequest.getOldPassword(), user.getPassword())) {
                throw new RuntimeException("旧密码不正确");
            }

            // 设置新密码
            user.setPassword(passwordEncoder.encode(updatePasswordRequest.getNewPassword()));

            // 保存更新后的用户信息
            userRepository.save(user);

            return true;
        } catch (Exception e) {
            // 记录详细错误信息
            System.err.println("修改密码失败: " + e.getMessage());
            e.printStackTrace();
            throw e; // 重新抛出异常，让全局异常处理器处理
        }
    }
}
