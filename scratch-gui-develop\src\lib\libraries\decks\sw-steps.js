// Intro
import introMove from './steps/intro-1-move.sw.gif';
import introSay from './steps/intro-2-say.sw.gif';
import introGreenFlag from './steps/intro-3-green-flag.sw.gif';

// Text to Speech
import speechAddExtension from './steps/speech-add-extension.sw.gif';
import speechSaySomething from './steps/speech-say-something.sw.png';
import speechSetVoice from './steps/speech-set-voice.sw.png';
import speechMoveAround from './steps/speech-move-around.sw.png';
import speechAddBackdrop from './steps/pick-backdrop.LTR.gif';
import speechAddSprite from './steps/speech-add-sprite.LTR.gif';
import speechSong from './steps/speech-song.sw.png';
import speechChangeColor from './steps/speech-change-color.sw.png';
import speechSpin from './steps/speech-spin.sw.png';
import speechGrowShrink from './steps/speech-grow-shrink.sw.png';

// Cartoon Network
import cnShowCharacter from './steps/cn-show-character.LTR.gif';
import cnSay from './steps/cn-say.sw.png';
import cnGlide from './steps/cn-glide.sw.png';
import cnPickSprite from './steps/cn-pick-sprite.LTR.gif';
import cnCollect from './steps/cn-collect.sw.png';
import cnVariable from './steps/add-variable.sw.gif';
import cnScore from './steps/cn-score.sw.png';
import cnBackdrop from './steps/cn-backdrop.sw.png';

// Add sprite
import addSprite from './steps/add-sprite.LTR.gif';

// Animate a name
import namePickLetter from './steps/name-pick-letter.LTR.gif';
import namePlaySound from './steps/name-play-sound.sw.png';
import namePickLetter2 from './steps/name-pick-letter2.LTR.gif';
import nameChangeColor from './steps/name-change-color.sw.png';
import nameSpin from './steps/name-spin.sw.png';
import nameGrow from './steps/name-grow.sw.png';

// Make Music
import musicPickInstrument from './steps/music-pick-instrument.LTR.gif';
import musicPlaySound from './steps/music-play-sound.sw.png';
import musicMakeSong from './steps/music-make-song.sw.png';
import musicMakeBeat from './steps/music-make-beat.sw.png';
import musicMakeBeatbox from './steps/music-make-beatbox.sw.png';

// Chase-Game
import chaseGameAddBackdrop from './steps/chase-game-add-backdrop.LTR.gif';
import chaseGameAddSprite1 from './steps/chase-game-add-sprite1.LTR.gif';
import chaseGameRightLeft from './steps/chase-game-right-left.sw.png';
import chaseGameUpDown from './steps/chase-game-up-down.sw.png';
import chaseGameAddSprite2 from './steps/chase-game-add-sprite2.LTR.gif';
import chaseGameMoveRandomly from './steps/chase-game-move-randomly.sw.png';
import chaseGamePlaySound from './steps/chase-game-play-sound.sw.png';
import chaseGameAddVariable from './steps/add-variable.sw.gif';
import chaseGameChangeScore from './steps/chase-game-change-score.sw.png';

// Clicker-Game (Pop Game)
import popGamePickSprite from './steps/pop-game-pick-sprite.LTR.gif';
import popGamePlaySound from './steps/pop-game-play-sound.sw.png';
import popGameAddScore from './steps/add-variable.sw.gif';
import popGameChangeScore from './steps/pop-game-change-score.sw.png';
import popGameRandomPosition from './steps/pop-game-random-position.sw.png';
import popGameChangeColor from './steps/pop-game-change-color.sw.png';
import popGameResetScore from './steps/pop-game-reset-score.sw.png';

// Animate A Character
import animateCharPickBackdrop from './steps/pick-backdrop.LTR.gif';
import animateCharPickSprite from './steps/animate-char-pick-sprite.LTR.gif';
import animateCharSaySomething from './steps/animate-char-say-something.sw.png';
import animateCharAddSound from './steps/animate-char-add-sound.sw.png';
import animateCharTalk from './steps/animate-char-talk.sw.png';
import animateCharMove from './steps/animate-char-move.sw.png';
import animateCharJump from './steps/animate-char-jump.sw.png';
import animateCharChangeColor from './steps/animate-char-change-color.sw.png';

// Tell A Story
import storyPickBackdrop from './steps/story-pick-backdrop.LTR.gif';
import storyPickSprite from './steps/story-pick-sprite.LTR.gif';
import storySaySomething from './steps/story-say-something.sw.png';
import storyPickSprite2 from './steps/story-pick-sprite2.LTR.gif';
import storyFlip from './steps/story-flip.sw.gif';
import storyConversation from './steps/story-conversation.sw.png';
import storyPickBackdrop2 from './steps/story-pick-backdrop2.LTR.gif';
import storySwitchBackdrop from './steps/story-switch-backdrop.sw.png';
import storyHideCharacter from './steps/story-hide-character.sw.png';
import storyShowCharacter from './steps/story-show-character.sw.png';

// Video Sensing
import videoAddExtension from './steps/video-add-extension.sw.gif';
import videoPet from './steps/video-pet.sw.png';
import videoAnimate from './steps/video-animate.sw.png';
import videoPop from './steps/video-pop.sw.png';

// Make it Fly
import flyChooseBackdrop from './steps/fly-choose-backdrop.LTR.gif';
import flyChooseCharacter from './steps/fly-choose-character.LTR.png';
import flySaySomething from './steps/fly-say-something.sw.png';
import flyMoveArrows from './steps/fly-make-interactive.sw.png';
import flyChooseObject from './steps/fly-object-to-collect.LTR.png';
import flyFlyingObject from './steps/fly-flying-heart.sw.png';
import flySelectFlyingSprite from './steps/fly-select-flyer.LTR.png';
import flyAddScore from './steps/add-variable.sw.gif';
import flyKeepScore from './steps/fly-keep-score.sw.png';
import flyAddScenery from './steps/fly-choose-scenery.LTR.gif';
import flyMoveScenery from './steps/fly-move-scenery.sw.png';
import flySwitchLooks from './steps/fly-switch-costume.sw.png';

// Pong
import pongAddBackdrop from './steps/pong-add-backdrop.LTR.png';
import pongAddBallSprite from './steps/pong-add-ball-sprite.LTR.png';
import pongBounceAround from './steps/pong-bounce-around.sw.png';
import pongAddPaddle from './steps/pong-add-a-paddle.LTR.gif';
import pongMoveThePaddle from './steps/pong-move-the-paddle.sw.png';
import pongSelectBallSprite from './steps/pong-select-ball.LTR.png';
import pongAddMoreCodeToBall from './steps/pong-add-code-to-ball.sw.png';
import pongAddAScore from './steps/add-variable.sw.gif';
import pongChooseScoreFromMenu from './steps/pong-choose-score.sw.png';
import pongInsertChangeScoreBlock from './steps/pong-insert-change-score.sw.png';
import pongResetScore from './steps/pong-reset-score.sw.png';
import pongAddLineSprite from './steps/pong-add-line.LTR.gif';
import pongGameOver from './steps/pong-game-over.sw.png';

// Imagine a World
import imagineTypeWhatYouWant from './steps/imagine-type-what-you-want.sw.png';
import imagineClickGreenFlag from './steps/imagine-click-green-flag.sw.png';
import imagineChooseBackdrop from './steps/imagine-choose-backdrop.LTR.png';
import imagineChooseSprite from './steps/imagine-choose-any-sprite.LTR.png';
import imagineFlyAround from './steps/imagine-fly-around.sw.png';
import imagineChooseAnotherSprite from './steps/imagine-choose-another-sprite.LTR.png';
import imagineLeftRight from './steps/imagine-left-right.sw.png';
import imagineUpDown from './steps/imagine-up-down.sw.png';
import imagineChangeCostumes from './steps/imagine-change-costumes.sw.png';
import imagineGlideToPoint from './steps/imagine-glide-to-point.sw.png';
import imagineGrowShrink from './steps/imagine-grow-shrink.sw.png';
import imagineChooseAnotherBackdrop from './steps/imagine-choose-another-backdrop.LTR.png';
import imagineSwitchBackdrops from './steps/imagine-switch-backdrops.sw.png';
import imagineRecordASound from './steps/imagine-record-a-sound.sw.gif';
import imagineChooseSound from './steps/imagine-choose-sound.sw.png';

// Add a Backdrop
import addBackdrop from './steps/add-backdrop.LTR.png';

// Add Effects
import addEffects from './steps/add-effects.sw.png';

// Hide and Show
import hideAndShow from './steps/hide-show.sw.png';

// Switch Costumes
import switchCostumes from './steps/switch-costumes.sw.png';

// Change Size
import changeSize from './steps/change-size.sw.png';

// Spin
import spinTurn from './steps/spin-turn.sw.png';
import spinPointInDirection from './steps/spin-point-in-direction.sw.png';

// Record a Sound
import recordASoundSoundsTab from './steps/record-a-sound-sounds-tab.sw.png';
import recordASoundClickRecord from './steps/record-a-sound-click-record.sw.png';
import recordASoundPressRecordButton from './steps/record-a-sound-press-record-button.sw.png';
import recordASoundChooseSound from './steps/record-a-sound-choose-sound.sw.png';
import recordASoundPlayYourSound from './steps/record-a-sound-play-your-sound.sw.png';

// Use Arrow Keys
import moveArrowKeysLeftRight from './steps/move-arrow-keys-left-right.sw.png';
import moveArrowKeysUpDown from './steps/move-arrow-keys-up-down.sw.png';

// Glide Around
import glideAroundBackAndForth from './steps/glide-around-back-and-forth.sw.png';
import glideAroundPoint from './steps/glide-around-point.sw.png';

// Code a Cartoon
import codeCartoonSaySomething from './steps/code-cartoon-01-say-something.sw.png';
import codeCartoonAnimate from './steps/code-cartoon-02-animate.sw.png';
import codeCartoonSelectDifferentCharacter from './steps/code-cartoon-03-select-different-character.LTR.png';
import codeCartoonUseMinusSign from './steps/code-cartoon-04-use-minus-sign.sw.png';
import codeCartoonGrowShrink from './steps/code-cartoon-05-grow-shrink.sw.png';
import codeCartoonSelectDifferentCharacter2 from './steps/code-cartoon-06-select-another-different-character.LTR.png';
import codeCartoonJump from './steps/code-cartoon-07-jump.sw.png';
import codeCartoonChangeScenes from './steps/code-cartoon-08-change-scenes.sw.png';
import codeCartoonGlideAround from './steps/code-cartoon-09-glide-around.sw.png';
import codeCartoonChangeCostumes from './steps/code-cartoon-10-change-costumes.sw.png';
import codeCartoonChooseMoreCharacters from './steps/code-cartoon-11-choose-more-characters.LTR.png';

// Talking Tales
import talesAddExtension from './steps/speech-add-extension.sw.gif';
import talesChooseSprite from './steps/talking-2-choose-sprite.LTR.png';
import talesSaySomething from './steps/talking-3-say-something.sw.png';
import talesChooseBackdrop from './steps/talking-4-choose-backdrop.LTR.png';
import talesSwitchBackdrop from './steps/talking-5-switch-backdrop.sw.png';
import talesChooseAnotherSprite from './steps/talking-6-choose-another-sprite.LTR.png';
import talesMoveAround from './steps/talking-7-move-around.sw.png';
import talesChooseAnotherBackdrop from './steps/talking-8-choose-another-backdrop.LTR.png';
import talesAnimateTalking from './steps/talking-9-animate.sw.png';
import talesChooseThirdBackdrop from './steps/talking-10-choose-third-backdrop.LTR.png';
import talesChooseSound from './steps/talking-11-choose-sound.sw.gif';
import talesDanceMoves from './steps/talking-12-dance-moves.sw.png';
import talesAskAnswer from './steps/talking-13-ask-and-answer.sw.png';

const swImages = {
    // Intro
    introMove: introMove,
    introSay: introSay,
    introGreenFlag: introGreenFlag,

    // Text to Speech
    speechAddExtension: speechAddExtension,
    speechSaySomething: speechSaySomething,
    speechSetVoice: speechSetVoice,
    speechMoveAround: speechMoveAround,
    speechAddBackdrop: speechAddBackdrop,
    speechAddSprite: speechAddSprite,
    speechSong: speechSong,
    speechChangeColor: speechChangeColor,
    speechSpin: speechSpin,
    speechGrowShrink: speechGrowShrink,

    // Cartoon Network
    cnShowCharacter: cnShowCharacter,
    cnSay: cnSay,
    cnGlide: cnGlide,
    cnPickSprite: cnPickSprite,
    cnCollect: cnCollect,
    cnVariable: cnVariable,
    cnScore: cnScore,
    cnBackdrop: cnBackdrop,

    // Add sprite
    addSprite: addSprite,

    // Animate a name
    namePickLetter: namePickLetter,
    namePlaySound: namePlaySound,
    namePickLetter2: namePickLetter2,
    nameChangeColor: nameChangeColor,
    nameSpin: nameSpin,
    nameGrow: nameGrow,

    // Make-Music
    musicPickInstrument: musicPickInstrument,
    musicPlaySound: musicPlaySound,
    musicMakeSong: musicMakeSong,
    musicMakeBeat: musicMakeBeat,
    musicMakeBeatbox: musicMakeBeatbox,

    // Chase-Game
    chaseGameAddBackdrop: chaseGameAddBackdrop,
    chaseGameAddSprite1: chaseGameAddSprite1,
    chaseGameRightLeft: chaseGameRightLeft,
    chaseGameUpDown: chaseGameUpDown,
    chaseGameAddSprite2: chaseGameAddSprite2,
    chaseGameMoveRandomly: chaseGameMoveRandomly,
    chaseGamePlaySound: chaseGamePlaySound,
    chaseGameAddVariable: chaseGameAddVariable,
    chaseGameChangeScore: chaseGameChangeScore,

    // Make-A-Pop/Clicker Game
    popGamePickSprite: popGamePickSprite,
    popGamePlaySound: popGamePlaySound,
    popGameAddScore: popGameAddScore,
    popGameChangeScore: popGameChangeScore,
    popGameRandomPosition: popGameRandomPosition,
    popGameChangeColor: popGameChangeColor,
    popGameResetScore: popGameResetScore,

    // Animate A Character
    animateCharPickBackdrop: animateCharPickBackdrop,
    animateCharPickSprite: animateCharPickSprite,
    animateCharSaySomething: animateCharSaySomething,
    animateCharAddSound: animateCharAddSound,
    animateCharTalk: animateCharTalk,
    animateCharMove: animateCharMove,
    animateCharJump: animateCharJump,
    animateCharChangeColor: animateCharChangeColor,

    // Tell A Story
    storyPickBackdrop: storyPickBackdrop,
    storyPickSprite: storyPickSprite,
    storySaySomething: storySaySomething,
    storyPickSprite2: storyPickSprite2,
    storyFlip: storyFlip,
    storyConversation: storyConversation,
    storyPickBackdrop2: storyPickBackdrop2,
    storySwitchBackdrop: storySwitchBackdrop,
    storyHideCharacter: storyHideCharacter,
    storyShowCharacter: storyShowCharacter,

    // Video Sensing
    videoAddExtension: videoAddExtension,
    videoPet: videoPet,
    videoAnimate: videoAnimate,
    videoPop: videoPop,

    // Make it Fly
    flyChooseBackdrop: flyChooseBackdrop,
    flyChooseCharacter: flyChooseCharacter,
    flySaySomething: flySaySomething,
    flyMoveArrows: flyMoveArrows,
    flyChooseObject: flyChooseObject,
    flyFlyingObject: flyFlyingObject,
    flySelectFlyingSprite: flySelectFlyingSprite,
    flyAddScore: flyAddScore,
    flyKeepScore: flyKeepScore,
    flyAddScenery: flyAddScenery,
    flyMoveScenery: flyMoveScenery,
    flySwitchLooks: flySwitchLooks,

    // Pong
    pongAddBackdrop: pongAddBackdrop,
    pongAddBallSprite: pongAddBallSprite,
    pongBounceAround: pongBounceAround,
    pongAddPaddle: pongAddPaddle,
    pongMoveThePaddle: pongMoveThePaddle,
    pongSelectBallSprite: pongSelectBallSprite,
    pongAddMoreCodeToBall: pongAddMoreCodeToBall,
    pongAddAScore: pongAddAScore,
    pongChooseScoreFromMenu: pongChooseScoreFromMenu,
    pongInsertChangeScoreBlock: pongInsertChangeScoreBlock,
    pongResetScore: pongResetScore,
    pongAddLineSprite: pongAddLineSprite,
    pongGameOver: pongGameOver,

    // Imagine a World
    imagineTypeWhatYouWant: imagineTypeWhatYouWant,
    imagineClickGreenFlag: imagineClickGreenFlag,
    imagineChooseBackdrop: imagineChooseBackdrop,
    imagineChooseSprite: imagineChooseSprite,
    imagineFlyAround: imagineFlyAround,
    imagineChooseAnotherSprite: imagineChooseAnotherSprite,
    imagineLeftRight: imagineLeftRight,
    imagineUpDown: imagineUpDown,
    imagineChangeCostumes: imagineChangeCostumes,
    imagineGlideToPoint: imagineGlideToPoint,
    imagineGrowShrink: imagineGrowShrink,
    imagineChooseAnotherBackdrop: imagineChooseAnotherBackdrop,
    imagineSwitchBackdrops: imagineSwitchBackdrops,
    imagineRecordASound: imagineRecordASound,
    imagineChooseSound: imagineChooseSound,

    // Add a Backdrop
    addBackdrop: addBackdrop,

    // Add Effects
    addEffects: addEffects,

    // Hide and Show
    hideAndShow: hideAndShow,

    // Switch Costumes
    switchCostumes: switchCostumes,

    // Change Size
    changeSize: changeSize,

    // Spin
    spinTurn: spinTurn,
    spinPointInDirection: spinPointInDirection,

    // Record a Sound
    recordASoundSoundsTab: recordASoundSoundsTab,
    recordASoundClickRecord: recordASoundClickRecord,
    recordASoundPressRecordButton: recordASoundPressRecordButton,
    recordASoundChooseSound: recordASoundChooseSound,
    recordASoundPlayYourSound: recordASoundPlayYourSound,

    // Use Arrow Keys
    moveArrowKeysLeftRight: moveArrowKeysLeftRight,
    moveArrowKeysUpDown: moveArrowKeysUpDown,

    // Glide Around
    glideAroundBackAndForth: glideAroundBackAndForth,
    glideAroundPoint: glideAroundPoint,

    // Code a Cartoon
    codeCartoonSaySomething: codeCartoonSaySomething,
    codeCartoonAnimate: codeCartoonAnimate,
    codeCartoonSelectDifferentCharacter: codeCartoonSelectDifferentCharacter,
    codeCartoonUseMinusSign: codeCartoonUseMinusSign,
    codeCartoonGrowShrink: codeCartoonGrowShrink,
    codeCartoonSelectDifferentCharacter2: codeCartoonSelectDifferentCharacter2,
    codeCartoonJump: codeCartoonJump,
    codeCartoonChangeScenes: codeCartoonChangeScenes,
    codeCartoonGlideAround: codeCartoonGlideAround,
    codeCartoonChangeCostumes: codeCartoonChangeCostumes,
    codeCartoonChooseMoreCharacters: codeCartoonChooseMoreCharacters,

    // Talking Tales
    talesAddExtension: talesAddExtension,
    talesChooseSprite: talesChooseSprite,
    talesSaySomething: talesSaySomething,
    talesAskAnswer: talesAskAnswer,
    talesChooseBackdrop: talesChooseBackdrop,
    talesSwitchBackdrop: talesSwitchBackdrop,
    talesChooseAnotherSprite: talesChooseAnotherSprite,
    talesMoveAround: talesMoveAround,
    talesChooseAnotherBackdrop: talesChooseAnotherBackdrop,
    talesAnimateTalking: talesAnimateTalking,
    talesChooseThirdBackdrop: talesChooseThirdBackdrop,
    talesChooseSound: talesChooseSound,
    talesDanceMoves: talesDanceMoves
};

export {swImages};
