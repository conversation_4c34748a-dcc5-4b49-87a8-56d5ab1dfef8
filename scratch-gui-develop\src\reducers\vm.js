import VM from 'scratch-vm';
import storage from '../lib/storage';
import registerK1Blocks from '../lib/k1-vm-extension';
import { setupK1WorkerPatch } from '../lib/vm-worker-util';

// 在创建VM前设置Worker补丁
setupK1WorkerPatch();

const SET_VM = 'scratch-gui/vm/SET_VM';
const defaultVM = new VM();
defaultVM.attachStorage(storage);

// 初始化时注册K1积木块到VM运行时
registerK1Blocks(defaultVM);

const initialState = defaultVM;

const reducer = function (state, action) {
    if (typeof state === 'undefined') state = initialState;
    switch (action.type) {
    case SET_VM:
        // 确保新的VM实例也注册了K1积木块
        registerK1Blocks(action.vm);
        return action.vm;
    default:
        return state;
    }
};
const setVM = function (vm) {
    return {
        type: SET_VM,
        vm: vm
    };
};

export {
    reducer as default,
    initialState as vmInitialState,
    setVM
};
