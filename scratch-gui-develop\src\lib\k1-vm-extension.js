/**
 * K1机器人VM扩展模块
 * 提供K1积木块在VM中的运行时支持
 */

/**
 * 创建K1扩展虚拟脚本
 * 这个函数在VM尝试加载K1扩展脚本时提供一个虚拟实现
 * @returns {string} K1扩展脚本内容
 */
const createK1ExtensionScript = () => {
    return `
        // K1扩展模拟脚本
        class K1Extension {
            constructor(runtime) {
                this.runtime = runtime;
            }

            getInfo() {
                return {
                    id: 'k1',
                    name: 'K1机器人',
                    blocks: []
                };
            }
        }

        // 将扩展注册到Scratch
        Scratch.extensions.register(new K1Extension());
    `;
};

/**
 * 设置K1扩展脚本处理器
 * 这个函数劫持window.fetch和importScripts，防止VM尝试从外部URL加载K1脚本
 */
const setupK1ScriptHandler = () => {
    // 只在浏览器环境中执行
    if (typeof window === 'undefined') return;

    console.log('设置K1扩展脚本处理器...');

    // 保存原始fetch函数
    const originalFetch = window.fetch;

    // 劫持fetch请求
    window.fetch = function(url, options) {
        if (url && typeof url === 'string' && (url.includes('/k1') || url.includes('k1.js'))) {
            console.log('拦截对K1扩展脚本的fetch请求:', url);

            // 返回模拟响应
            return Promise.resolve({
                ok: true,
                status: 200,
                text: () => Promise.resolve(createK1ExtensionScript()),
                json: () => Promise.resolve({})
            });
        }

        // 对于其他请求，使用原始fetch
        return originalFetch.apply(this, arguments);
    };

    // 如果在Worker环境中，劫持importScripts
    if (typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope) {
        const originalImportScripts = self.importScripts;

        self.importScripts = function() {
            const scripts = Array.from(arguments);
            const filteredScripts = scripts.filter(script => {
                if (typeof script === 'string' && (script.includes('/k1') || script.includes('k1.js'))) {
                    console.log('拦截对K1扩展脚本的importScripts请求:', script);
                    // 注入K1扩展代码
                    eval(createK1ExtensionScript());
                    return false;
                }
                return true;
            });

            // 如果还有其他脚本需要加载，调用原始importScripts
            if (filteredScripts.length > 0) {
                return originalImportScripts.apply(this, filteredScripts);
            }
        };
    }

    console.log('K1扩展脚本处理器设置完成');
};

/**
 * 注册K1积木块到VM运行时
 * 这确保VM在序列化和反序列化项目时能够正确处理K1积木块
 * @param {object} vm - Scratch VM实例
 */
const registerK1Blocks = vm => {
    if (!vm || !vm.runtime) {
        console.error('无法注册K1积木块：VM或运行时不可用');
        return;
    }

    // 设置K1扩展脚本处理器，防止VM尝试从外部URL加载K1脚本
    setupK1ScriptHandler();

    const runtime = vm.runtime;
    console.log('开始注册K1积木块到VM运行时...');

    // 如果扩展管理器可用，注册K1扩展
    if (vm.extensionManager) {
        try {
            // 防止VM尝试加载外部K1扩展脚本
            const originalLoadExtensionURL = vm.extensionManager._loadExtensionURL;
            vm.extensionManager._loadExtensionURL = function(url) {
                if (url.includes('/k1') || url.includes('k1.js')) {
                    console.log('拦截K1扩展加载请求:', url);
                    // 返回假的加载成功Promise
                    return Promise.resolve();
                }
                return originalLoadExtensionURL.call(this, url);
            };
        } catch (e) {
            console.error('修改扩展管理器失败:', e);
        }
    }

    // 注册K1积木块处理程序，确保VM能够识别这些积木块
    const k1BlockTypes = [
        'k1_forward',
        'k1_back',
        'k1_turn_left',
        'k1_turn_right',
        'k1_gpp_say',
        'k1_servo_open',
        'k1_servo_close',
        'k1_tracker_start',
        'k1_tracker_close',
        'k1_beep',
        'k1_colorful_led',
        'k1_set_cscript_mode',
        'k1_cexit',
        'k1_lightsensor',
        'k1_lightsensor_reporter',
        'k1_distsensor',
        'k1_distsensor_reporter',
        'k1_mic1sensor',
        'k1_mic1sensor_reporter',
        'k1_tracker',
        'k1_tracker_reporter',
        'k1_get_ps2value',
        'k1_get_ps2value_reporter'
    ];

    // 实现K1积木块的功能
    const implementK1BlockFunctions = () => {
        // 前进积木块
        runtime._primitives['k1_forward'] = (args, util) => {
            const speed = toNumber(args.SPEED || 4);
            const distance = toNumber(args.DISTANCE || 10);
            console.log(`执行K1前进: 速度=${speed}, 距离=${distance}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 根据精灵的方向计算移动的x和y分量
            const direction = target.direction;
            const radians = direction * Math.PI / 180;

            // 计算移动距离，速度作为移动的倍数
            const moveDistance = distance * (speed / 4);

            // 计算x和y的变化量
            const dx = moveDistance * Math.sin(radians);
            const dy = moveDistance * Math.cos(radians);

            // 更新精灵位置
            target.setXY(target.x + dx, target.y + dy);
        };

        // 后退积木块
        runtime._primitives['k1_back'] = (args, util) => {
            const speed = toNumber(args.SPEED || 4);
            const distance = toNumber(args.DISTANCE || 10);
            console.log(`执行K1后退: 速度=${speed}, 距离=${distance}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 根据精灵的方向计算移动的x和y分量
            const direction = target.direction;
            const radians = direction * Math.PI / 180;

            // 计算移动距离，速度作为移动的倍数，后退所以是负值
            const moveDistance = -distance * (speed / 4);

            // 计算x和y的变化量
            const dx = moveDistance * Math.sin(radians);
            const dy = moveDistance * Math.cos(radians);

            // 更新精灵位置
            target.setXY(target.x + dx, target.y + dy);
        };

        // 左转积木块
        runtime._primitives['k1_turn_left'] = (args, util) => {
            const degree = toNumber(args.DEGREE || 15);
            console.log(`执行K1左转: 角度=${degree}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 更新精灵方向
            target.setDirection(target.direction - degree);
        };

        // 右转积木块
        runtime._primitives['k1_turn_right'] = (args, util) => {
            const degree = toNumber(args.DEGREE || 15);
            console.log(`执行K1右转: 角度=${degree}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 更新精灵方向
            target.setDirection(target.direction + degree);
        };

        // 说话积木块
        runtime._primitives['k1_gpp_say'] = (args, util) => {
            const mode = toNumber(args.MODE || 1);
            const str = toString(args.TEXT || '');
            console.log(`执行K1说话: 模式=${mode}, 内容=${str}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 使用Scratch原生的looks_say积木块功能
            runtime._primitives.looks_say({
                MESSAGE: str
            }, util);

            // 如果是模式0，设置一个定时器来清除对话框
            if (mode === 0) {
                setTimeout(() => {
                    if (target) target.say(null); // 清除对话框
                }, 2000); // 2秒后清除
            }
        };

        // 开启机械手积木块
        runtime._primitives['k1_servo_open'] = (args, util) => {
            console.log('执行K1开启机械手');

            // 获取当前精灵
            const target = util.target;
            if (!target) return 0;

            // 这里可以添加一些视觉效果，比如改变造型
            // 返回成功
            return 0;
        };

        // 关闭机械手积木块
        runtime._primitives['k1_servo_close'] = (args, util) => {
            console.log('执行K1关闭机械手');

            // 获取当前精灵
            const target = util.target;
            if (!target) return 0;

            // 这里可以添加一些视觉效果，比如改变造型
            // 返回成功
            return 0;
        };

        // 开启巡线模式积木块
        runtime._primitives['k1_tracker_start'] = (args, util) => {
            console.log('执行K1开启巡线模式');

            // 获取当前精灵
            const target = util.target;
            if (!target) return 0;

            // 这里可以添加一些视觉效果，比如改变造型
            // 返回成功
            return 0;
        };

        // 关闭巡线模式积木块
        runtime._primitives['k1_tracker_close'] = (args, util) => {
            console.log('执行K1关闭巡线模式');

            // 获取当前精灵
            const target = util.target;
            if (!target) return 0;

            // 这里可以添加一些视觉效果，比如改变造型
            // 返回成功
            return 0;
        };

        // 蜂鸣器积木块
        runtime._primitives['k1_beep'] = (args, util) => {
            const bound = toNumber(args.BOUND || 1000);
            const time = toNumber(args.TIME || 500);
            console.log(`执行K1蜂鸣器: 频率=${bound}, 时间=${time}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 这里可以添加一些视觉效果，比如改变造型或者播放声音
        };

        // 炫彩LED灯积木块
        runtime._primitives['k1_colorful_led'] = (args, util) => {
            const mode = toNumber(args.MODE || 3);
            const rgb = toNumber(args.RGB || 1);
            console.log(`执行K1炫彩LED: 模式=${mode}, 颜色=${rgb}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 这里可以添加一些视觉效果，比如改变造型或者改变颜色效果
        };

        // 设置C程序运行模式积木块
        runtime._primitives['k1_set_cscript_mode'] = (args, util) => {
            const mode = toNumber(args.MODE || 2);
            console.log(`执行K1设置C程序运行模式: 模式=${mode}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 这里可以添加一些视觉效果
        };

        // 停止当前C程序积木块
        runtime._primitives['k1_cexit'] = (args, util) => {
            console.log('执行K1停止当前C程序');

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 这里可以添加一些视觉效果
        };

        // 光线传感器积木块 - 命令块版本
        runtime._primitives['k1_lightsensor'] = (args, util) => {
            console.log('执行K1读取光线传感器数值');

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 生成一个随机的光线传感器值
            const value = Math.floor(Math.random() * 100);

            // 使用Scratch原生的looks_say积木块功能
            runtime._primitives.looks_say({
                MESSAGE: `光线传感器值: ${value}`
            }, util);

            // 2秒后清除对话框
            setTimeout(() => {
                if (target) runtime._primitives.looks_say({MESSAGE: ''}, util);
            }, 2000);
        };

        // 光线传感器积木块 - 报告块版本
        runtime._primitives['k1_lightsensor_reporter'] = (args, util) => {
            console.log('获取K1光线传感器数值');

            // 生成一个随机的光线传感器值
            return Math.floor(Math.random() * 100);
        };

        // 距离传感器积木块 - 命令块版本
        runtime._primitives['k1_distsensor'] = (args, util) => {
            console.log('执行K1读取距离传感器数值');

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 生成一个随机的距离传感器值
            const value = Math.floor(Math.random() * 200);

            // 使用Scratch原生的looks_say积木块功能
            runtime._primitives.looks_say({
                MESSAGE: `距离传感器值: ${value}厘米`
            }, util);

            // 2秒后清除对话框
            setTimeout(() => {
                if (target) runtime._primitives.looks_say({MESSAGE: ''}, util);
            }, 2000);
        };

        // 距离传感器积木块 - 报告块版本
        runtime._primitives['k1_distsensor_reporter'] = (args, util) => {
            console.log('获取K1距离传感器数值');

            // 生成一个随机的距离传感器值
            return Math.floor(Math.random() * 200);
        };

        // 噪声传感器积木块 - 命令块版本
        runtime._primitives['k1_mic1sensor'] = (args, util) => {
            console.log('执行K1读取噪声传感器数值');

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 生成一个随机的噪声传感器值
            const value = Math.floor(Math.random() * 100);

            // 使用Scratch原生的looks_say积木块功能
            runtime._primitives.looks_say({
                MESSAGE: `噪声传感器值: ${value}`
            }, util);

            // 2秒后清除对话框
            setTimeout(() => {
                if (target) runtime._primitives.looks_say({MESSAGE: ''}, util);
            }, 2000);
        };

        // 噪声传感器积木块 - 报告块版本
        runtime._primitives['k1_mic1sensor_reporter'] = (args, util) => {
            console.log('获取K1噪声传感器数值');

            // 生成一个随机的噪声传感器值
            return Math.floor(Math.random() * 100);
        };

        // 循迹传感器积木块 - 命令块版本
        runtime._primitives['k1_tracker'] = (args, util) => {
            const id = toNumber(args.ID || 1);
            console.log(`执行K1读取循迹传感器数值: ID=${id}`);

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 生成一个随机的循迹传感器值
            const value = Math.floor(Math.random() * 2);

            // 使用Scratch原生的looks_say积木块功能
            runtime._primitives.looks_say({
                MESSAGE: `循迹传感器${id}值: ${value}`
            }, util);

            // 2秒后清除对话框
            setTimeout(() => {
                if (target) runtime._primitives.looks_say({MESSAGE: ''}, util);
            }, 2000);
        };

        // 循迹传感器积木块 - 报告块版本
        runtime._primitives['k1_tracker_reporter'] = (args, util) => {
            const id = toNumber(args.ID || 1);
            console.log(`获取K1循迹传感器数值: ID=${id}`);

            // 生成一个随机的循迹传感器值
            return Math.floor(Math.random() * 2);
        };

        // 手柄按键值积木块 - 命令块版本
        runtime._primitives['k1_get_ps2value'] = (args, util) => {
            console.log('执行K1读取手柄按键值');

            // 获取当前精灵
            const target = util.target;
            if (!target) return;

            // 生成一个随机的手柄按键值
            const value = Math.floor(Math.random() * 10);

            // 使用Scratch原生的looks_say积木块功能
            runtime._primitives.looks_say({
                MESSAGE: `手柄按键值: ${value}`
            }, util);

            // 2秒后清除对话框
            setTimeout(() => {
                if (target) runtime._primitives.looks_say({MESSAGE: ''}, util);
            }, 2000);
        };

        // 手柄按键值积木块 - 报告块版本
        runtime._primitives['k1_get_ps2value_reporter'] = (args, util) => {
            console.log('获取K1手柄按键值');

            // 生成一个随机的手柄按键值
            return Math.floor(Math.random() * 10);
        };
    };

    // 定义类型转换函数
    const toNumber = value => {
        if (typeof value === 'number') return value;
        if (typeof value === 'string') {
            const n = Number(value);
            return isNaN(n) ? 0 : n;
        }
        return 0;
    };

    const toString = value => {
        if (value === null || value === undefined) return '';
        return String(value);
    };

    // 实现K1积木块功能
    implementK1BlockFunctions();

    // 遍历所有K1积木块类型并确保VM能够识别它们
    k1BlockTypes.forEach(blockType => {
        // 检查运行时是否已经知道如何处理这个积木块类型
        if (!runtime._primitives[blockType]) {
            console.log(`警告: ${blockType}没有实现，使用默认处理程序`);

            // 为未实现的积木块类型注册一个默认操作函数
            runtime._primitives[blockType] = (args, util) => {
                console.log(`执行未实现的K1积木块: ${blockType}`, args);
                // 对于记者块，返回一个默认值
                if (blockType.includes('_reporter')) {
                    return 0;
                }
                // 对于命令块，不返回任何值
            };
        }

        console.log(`已注册K1积木块处理程序: ${blockType}`);
    });

    console.log('K1积木块注册完成，现在VM可以正确处理K1积木块');
};

export default registerK1Blocks;