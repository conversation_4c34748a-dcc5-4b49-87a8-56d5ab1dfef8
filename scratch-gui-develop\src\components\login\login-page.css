@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.loginPageWrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    z-index: 1;
}

.loginPageWrapper::before {
    content: none;
}

.loginContainer {
    width: 100%;
    max-width: 400px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.logoContainer {
    margin-bottom: 30px;
}

.systemTitle {
    font-size: 24px;
    color: #4d97ff;
    margin: 0;
    padding: 0;
}

.form {
    display: flex;
    flex-direction: column;
    text-align: left;
    position: relative;
    z-index: 3;
}

.formTitle {
    font-size: 20px;
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.formGroup {
    margin-bottom: 15px;
    position: relative;
}

.label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: #333;
}

.input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    position: relative;
    z-index: 4;
}

.input:focus {
    outline: none;
    border-color: #4d97ff;
    box-shadow: 0 0 0 2px rgba(77, 151, 255, 0.2);
}

.submitButton {
    margin-top: 10px;
    padding: 12px;
    background-color: #4d97ff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
    position: relative;
    z-index: 5;
}

.submitButton:hover {
    background-color: #3373cc;
}

.submitButton:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.switchForm {
    margin-top: 20px;
    text-align: center;
    font-size: 14px;
    position: relative;
    z-index: 5;
}

.switchButton {
    background: none;
    border: none;
    color: #4d97ff;
    cursor: pointer;
    font-size: 14px;
    text-decoration: underline;
    margin-left: 5px;
    padding: 0;
}

.switchButton:hover {
    color: #3373cc;
}

.errorMessage {
    color: #ff6680;
    background-color: rgba(255, 102, 128, 0.1);
    border: 1px solid #ff6680;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
    text-align: center;
    position: relative;
    z-index: 4;
}

.loggedInContainer {
    text-align: center;
    position: relative;
    z-index: 3;
}

.welcomeTitle {
    font-size: 20px;
    margin-bottom: 20px;
    color: #333;
}

.buttonGroup {
    display: flex;
    flex-direction: column;
    gap: 10px;
    position: relative;
    z-index: 4;
}

.enterButton {
    padding: 12px;
    background-color: #4d97ff;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
    position: relative;
    z-index: 5;
}

.enterButton:hover {
    background-color: #3373cc;
}

.logoutButton {
    padding: 12px;
    background-color: #ff6680;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
    position: relative;
    z-index: 5;
}

.logoutButton:hover {
    background-color: #e64d68;
}

/* 加载中覆盖层 */
.loadingOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.85);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 12px;
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease-out;
    pointer-events: none;
}

/* 加载中旋转动画 */
.loadingSpinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(76, 151, 255, 0.3);
    border-radius: 50%;
    border-top-color: #4C97FF;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

/* 加载中文本 */
.loadingText {
    color: #3373CC;
    font-size: 16px;
    font-weight: 600;
}

/* 表单输入错误样式 */
.inputError {
    border-color: #FF4757 !important;
    background-color: rgba(255, 71, 87, 0.05) !important;
}

.inputError:focus {
    box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.15) !important;
}

.fieldError {
    color: #FF4757;
    font-size: 12px;
    margin-top: 5px;
    animation: fadeIn 0.3s ease-out;
}

@media (max-width: 480px) {
    .loginContainer {
        padding: 30px 20px;
        margin: 0 15px;
    }

    .systemTitle {
        font-size: 24px;
    }

    .submitButton {
        min-width: 120px;
        padding: 10px 20px;
    }
}

/* 已登录提示区域 */
.alreadyLoggedIn {
    margin: 30px 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background-color: rgba(76, 151, 255, 0.1);
    border-radius: 10px;
    animation: fadeIn 0.5s ease-out;
    position: relative;
    z-index: 3;
}

.alreadyLoggedIn::before {
    content: "✓";
    display: block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 24px;
    margin-bottom: 10px;
    background-color: #4CAF50;
    color: white;
    border-radius: 50%;
    font-weight: bold;
}

.buttonGroup {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    justify-content: center;
}

.enterSystemButton {
    background: linear-gradient(45deg, #4CAF50, #2E7D32);
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
    position: relative;
    z-index: 5;
}

.enterSystemButton:hover {
    background: linear-gradient(45deg, #388E3C, #4CAF50);
    box-shadow: 0 6px 15px rgba(76, 175, 80, 0.4);
    transform: translateY(-2px);
}

.enterSystemButton:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(76, 175, 80, 0.3);
}

.logoutButton:hover {
    background: linear-gradient(45deg, #D32F2F, #F44336);
    box-shadow: 0 6px 15px rgba(244, 67, 54, 0.4);
    transform: translateY(-2px);
}

.logoutButton:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(244, 67, 54, 0.3);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
