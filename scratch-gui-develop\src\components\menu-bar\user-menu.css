.userInfo {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    cursor: pointer;
    color: #4C97FF;
    font-weight: bold;
    transition: all 0.2s ease;
}

.userInfo.loggedIn {
    color: white; /* 白色文字 */
    background-color: #4C97FF; /* 蓝色背景 */
    border-radius: 20px;
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 5px rgba(51, 115, 204, 0.3); /* 添加阴影 */
}

.userInfo:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.userInfo.loggedIn:hover {
    background-color: #3373CC; /* 深蓝色悬停背景 */
}

.profileName {
    margin-right: 0.5rem;
    font-weight: bold;
    color: inherit; /* 继承父元素的颜色 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* 添加文字阴影增强可读性 */
}

.dropdownCaretPosition {
    display: flex;
}

.dropdownCaretIcon {
    width: 0.5rem;
    height: 0.5rem;
    filter: brightness(0) invert(1); /* 将图标变为白色 */
}

.menu {
    position: absolute;
    top: 0.1rem; /* 紧贴用户名，菜单栏高度为3rem，稍微上移一点 */
    right: 0.1rem; /* 与用户名右侧对齐 */
    background-color: #4C97FF; /* 蓝色背景 */
    border: 1px solid #3373CC; /* 深蓝色边框 */
    border-radius: 8px; /* 增加圆角 */
    box-shadow: 0 4px 12px rgba(51, 115, 204, 0.3); /* 蓝色阴影 */
    padding: 0.5rem 0;
    min-width: 8rem; /* 适当减小宽度，使其更协调 */
    z-index: 100;
    margin-top: 0.25rem; /* 添加一点点间距，使视觉上更舒适 */
}

.menuItem {
    padding: 0.75rem 1rem; /* 增加内边距 */
    cursor: pointer;
    color: white; /* 白色文字 */
    font-weight: 500; /* 稍微加粗 */
    transition: all 0.2s ease; /* 平滑过渡 */
}

.menuItem:hover {
    background-color: #3373CC; /* 深蓝色悬停背景 */
    color: white; /* 保持白色文字 */
}

.menuDivider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.3); /* 半透明白色分隔线 */
    margin: 0.5rem 0;
}

/* 菜单项图标 */
.menuItem::before {
    margin-right: 8px;
    font-size: 14px;
    color: white; /* 白色图标 */
    opacity: 0.9; /* 稍微透明 */
}

/* 个人信息菜单项 */
.menuItem:nth-child(1)::before {
    content: "👤";
}

/* 我的项目菜单项 */
.menuItem:nth-child(2)::before {
    content: "📁";
}

/* 退出登录按钮样式 */
.menuItem[data-logout="true"] {
    color: white; /* 白色文字 */
    font-weight: bold;
    margin: 0.25rem 0.5rem;
    text-align: center;
    transition: all 0.2s ease;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1); /* 半透明白色背景 */
}

.menuItem[data-logout="true"]::before {
    content: "🚪";
    color: white; /* 白色图标 */
}

.menuItem[data-logout="true"]:hover {
    background-color: rgba(255, 255, 255, 0.2); /* 更明显的白色背景 */
    color: white; /* 保持白色文字 */
}

.logoutIcon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
    transform: rotate(180deg);
    display: inline-block;
}
