@import "../../css/units.css";
@import "../../css/colors.css";
@import "../../css/z-index.css";

.drag-layer {
    position: fixed;
    pointer-events: none;
    z-index: $z-index-drag-layer;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    direction: ltr;
}

.image-wrapper {
    /* Absolute allows wrapper to snuggly fit image */
    position: absolute;
}

.image {
    max-width: 80px;
    max-height: 80px;
    min-width: 50px;
    min-height: 50px;

    /* Center the dragging image on the given position */
    margin-left: -50%;
    margin-top: -50%;

    padding: 0.25rem;
    border: 2px solid $looks-secondary;
    background: $ui-white;
    border-radius: 0.5rem;

    /* Use the same drop shadow as stage dragging */
    box-shadow: 5px 5px 5px $ui-black-transparent;
}
