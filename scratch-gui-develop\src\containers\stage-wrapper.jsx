import PropTypes from 'prop-types';
import React from 'react';
import {connect} from 'react-redux';
import VM from 'scratch-vm';
import {STAGE_DISPLAY_SIZES} from '../lib/layout-constants.js';
import StageWrapperComponent from '../components/stage-wrapper/stage-wrapper.jsx';
import {closeRobotSensorModal} from '../reducers/modals.js';

const StageWrapper = props => <StageWrapperComponent {...props} />;

StageWrapper.propTypes = {
    isRendererSupported: PropTypes.bool.isRequired,
    stageSize: PropTypes.oneOf(Object.keys(STAGE_DISPLAY_SIZES)).isRequired,
    vm: PropTypes.instanceOf(VM).isRequired
};

const mapStateToProps = state => ({
    robotSensorVisible: state.scratchGui.modals.robotSensorModal
});

const mapDispatchToProps = dispatch => ({
    onCloseRobotSensor: () => dispatch(closeRobotSensorModal())
});

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(StageWrapper);
