@import "../../css/colors.css";
@import "../../css/units.css";

.modal-content {
    width: 360px;
}

.body {
    background: $ui-white;
    padding: 1.5rem 2.25rem;
}

.body input[type="checkbox"],
.body input[type="radio"] {
    margin: 3px;
}

.label {
    font-weight: 500;
    margin: 0 0 0.75rem;
}

.disabled-label {
    opacity: 0.5;
}

.variable-name-text-input {
    margin-bottom: 1.5rem;
    width: 100%;
    border: 1px solid $ui-black-transparent;
    border-radius: 5px;
    padding: 0 1rem;
    height: 3rem;
    color: $text-primary-transparent;
    font-size: .875rem;
}

.info-message {
    font-weight: normal;
    font-size: .875rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.options-row {
    display: flex;
    font-weight: normal;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.button-row {
    font-weight: bolder;
    text-align: right;
}

.button-row button {
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    background: white;
    border: 1px solid $ui-black-transparent;
    font-weight: 600;
    font-size: 0.85rem;
}

.button-row button.ok-button {
    background: $looks-secondary;
    border: $looks-secondary;
    color: white;
}

[dir="ltr"] .button-row button + button {
    margin-left: 0.5rem;
}

[dir="rtl"] .button-row button + button {
    margin-right: 0.5rem;
}

.cloud-option {
    display:flex;
    border-top: 1px dashed hsla(0, 0%, 0%, .25);
    overflow: visible;
    padding: 1rem 0;
    text-align: center;
    width: 100%;
    margin: 0 auto;
}

.cloud-option-text {
    opacity: .5;
}
